# API Endpoints Overview

This document provides a comprehensive overview of all API endpoints available in the application, organized by route groups and controllers.

## Auth Controller Endpoints
These endpoints are prefixed with `/api/auth` and handle authentication and user management.

1. **POST /api/auth/login**
   - Purpose: Login a member
   - Authentication: Not required
   - Description: Authenticates a user and returns an access token

2. **POST /api/auth/login_old_member**
   - Purpose: Login an old existing member without any associated email address
   - Authentication: Not required
   - Description: Authenticates a user using member ID and post code

3. **POST /api/auth/register**
   - Purpose: Register, login and send-out confirmation mails for a new member
   - Authentication: Not required
   - Description: Creates a new user account and returns an access token

4. **POST /api/auth/send_feedback**
   - Purpose: Sends feedback to the middleware
   - Authentication: Not required
   - Description: Submits user feedback

5. **POST /api/auth/reset_password**
   - Purpose: Initiate the password reset for a member
   - Authentication: Not required
   - Description: Sends a password reset token to the user's email

6. **POST /api/auth/reset_password_confirm**
   - Purpose: Complete the password reset for a member
   - Authentication: Not required
   - Description: Validates the reset token and sets a new password

7. **POST /api/auth/change_password**
   - Purpose: Changes the current password for a logged in member
   - Authentication: Required
   - Description: Updates the user's password

8. **POST /api/auth/confirm_email**
   - Purpose: Confirm the email address for a member
   - Authentication: Not required
   - Description: Validates the email confirmation token

9. **POST /api/auth/confirm_email_newsletter**
   - Purpose: Confirm email for newsletter subscription
   - Authentication: Not required
   - Description: Validates the newsletter subscription confirmation

10. **POST /api/auth/me**
    - Purpose: Return the account information of the logged in member
    - Authentication: Required
    - Description: Returns the current user's profile data

11. **POST /api/auth/add_email**
    - Purpose: Add an email address and send-out a confirmation email to a member
    - Authentication: Required
    - Description: Adds or updates the user's email address

12. **POST /api/auth/change_data**
    - Purpose: Change the account information for the current member
    - Authentication: Required
    - Description: Updates the user's profile information

13. **POST /api/auth/delete_account**
    - Purpose: Delete the current user's account
    - Authentication: Required
    - Description: Permanently removes the user account

14. **POST /api/auth/logout**
    - Purpose: Logout the current member
    - Authentication: Required
    - Description: Invalidates the current access token

15. **POST /api/auth/refresh**
    - Purpose: Refresh the access token for the current logged in member
    - Authentication: Required
    - Description: Issues a new access token

## Check Controller Endpoints
These endpoints are prefixed with `/api/check` and handle validation operations.

16. **POST /api/check/email**
    - Purpose: Verify and check if an email address is already used by an existing member
    - Authentication: Not required
    - Description: Validates email availability

17. **POST /api/check/member_id**
    - Purpose: Verify and check if a member ID is already used
    - Authentication: Not required
    - Description: Validates member ID existence and returns next steps

## Data Controller Endpoints
These endpoints are prefixed with `/api/data` and provide access to various data resources.

18. **GET /api/data/titles**
    - Purpose: Return all predefined titles
    - Authentication: Not required
    - Description: Lists available titles (Dr., Prof., etc.)

19. **GET /api/data/languages**
    - Purpose: Return all predefined languages
    - Authentication: Not required
    - Description: Lists available languages

20. **GET /api/data/countries**
    - Purpose: Return all predefined countries
    - Authentication: Not required
    - Description: Lists available countries

21. **GET /api/data/brands**
    - Purpose: Return all predefined brands
    - Authentication: Not required
    - Description: Lists available brands with details

22. **GET /api/data/benefits**
    - Purpose: Return all benefits
    - Authentication: Not required
    - Description: Lists available benefits for members

23. **GET /api/data/store_guides**
    - Purpose: Return all store guides
    - Authentication: Not required
    - Description: Lists available store guides

24. **GET /api/data/faqs**
    - Purpose: Return all FAQs
    - Authentication: Not required
    - Description: Lists frequently asked questions and answers

25. **GET /api/data/texts**
    - Purpose: Return all texts
    - Authentication: Not required
    - Description: Lists text resources, optionally filtered by language, date, or key

26. **GET /api/data/feeds**
    - Purpose: Return the orchestrated feed for the current API user
    - Authentication: Optional (returns different results for authenticated users)
    - Description: Lists feed items, potentially personalized for logged-in users

27. **GET /api/data/purchases**
    - Purpose: Return all user purchases
    - Authentication: Required
    - Description: Lists the current user's purchase history

## Push Controller Endpoints
These endpoints handle push notifications and data synchronization.

28. **POST /push/user_changed**
    - Purpose: Handle user data changes
    - Authentication: Special (middleware)
    - Description: Processes user data updates from external systems

29. **POST /push/user_deleted**
    - Purpose: Handle user deletion
    - Authentication: Special (middleware)
    - Description: Processes user deletion from external systems

30. **POST /push/user_voucher**
    - Purpose: Handle user voucher updates
    - Authentication: Special (middleware)
    - Description: Processes voucher data updates from external systems

31. **POST /push/user_purchase**
    - Purpose: Handle user purchase updates
    - Authentication: Special (middleware)
    - Description: Processes purchase data updates from external systems
