@extends('site.layouts.layout')

@section('content')

    <!-- Start [ s1 ] -->
    <div class="cc-section cc-s1">
        <div class="cc-section-bg">
            <picture>
                <source srcset="{{ textImagePath('b_home.a_banner.a_4k_bg_image', 'ps-intro-bg.jpg') }}" media="(min-width:3000px)">
                <source srcset="{{ textImagePath('b_home.a_banner.a_desktop_full_hd_bg_image', 'ps-intro-bg.jpg') }}" media="(min-width:1920px)">
                <source srcset="{{ textImagePath('b_home.a_banner.a_desktop_bg_bg_image', 'ps-intro-bg.jpg') }}" media="(min-width:1367px)">
                <source srcset="{{ textImagePath('b_home.a_banner.a_tablet_landscape_bg_image', 'ps-intro-bg.jpg') }}" media="(min-width:1024px)">
                <source srcset="{{ textImagePath('b_home.a_banner.a_tablet_bg_bg_image', 'ps-intro-bg.jpg') }}" media="(min-width:768px)">
                <source srcset="{{ textImagePath('b_home.a_banner.a_mobile_bg_image', 'ps-intro-bg.jpg') }}" media="(min-width:1px)">
                <img decoding="async" src="{{ textImagePath('b_home.a_banner.a_mobile_bg_image', 'ps-intro-bg.jpg') }}" alt="{{ fT('b_home.a_banner.b_title', 'Gewinnspiel') }}">
            </picture>
        </div>
        <div class="cc-section-inner">
            <div class="container">
                <div class="cc-s1-wrapper">
                    <div class="cc-s1-left">
                        <div class="text-a text-white">
                            <div class="cc-s1-l-thumb ratio">
                                <img decoding="async" src="{{ url('public') }}/gfx/ps-intro-logo.svg" alt="Days of Play logo">
                            </div>
                            <h1 class="h1">{!! fT('b_home.a_banner.b_title', 'Gewinnspiel') !!}</h1>
                            
                            {!! auto_ptag(fT('b_home.a_banner.c_description', '<p>Mini Games spielen, Trophäen sammeln und große Preise gewinnen!</p>')) !!}

                            <div class="btn-spacer">
                                <a href="{{ route('psn_login') }}" class="btn btn-white btn-small cc-adobe-track" {!! adobeTrackData('participate click', "home intro", "1", "link") !!}><span class="btn-text">{!! fT('b_home.a_banner.d_button_text', 'Jetzt Teilnehmen') !!}</span></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End [ s1 ] -->

    @if(isset($competition))
        <!-- Start [ s6 ] -->
        <div class="cc-section cc-s6">
            <div class="cc-section-inner">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <div class="cc-s6-wrapper">
                                <div class="cc-s6-t">
                                    <div class="text-a text-center">
                                        <h2 class="h2">{!! fT('b_home.b_competition.a_title', 'Das kannst du heute gewinnen') !!}</h2>
                                    </div>
                                </div>
                                <div class="cc-s6-slider-part">
                                    <div class="swiper cc-s6-slider">
                                        <div class="swiper-wrapper">
                                            <div class="swiper-slide">
                                                <div class="text-a text-center">
                                                    <div class="cc-pd-tagname">
                                                        <span class="cc-pd-tagname-box">
                                                            <span class="cc-pd-tagname-box-in">
                                                                <span class="cc-pd-tagname-text">{!! fT('b_home.b_competition.b_tagespreise', 'Tagespreise') !!}</span>
                                                            </span>
                                                        </span>
                                                    </div>
                                                    <div class="cc-s6-slide-box-thumb ratio ratio-1x1 contain">
                                                        <picture>
                                                            <source srcset="{{ ccImgUrl($competition->prize_image, 'cc-main-prize-gfx.jpg') }}" media="(min-width:768px)">
                                                            <source srcset="{{ ccImgUrl($competition->prize_mobile_image, 'cc-main-prize-gfx.jpg') }}" media="(min-width:1px)">
                                                            <img loading="lazy" decoding="async" src="{{ ccImgUrl($competition->prize_mobile_image, 'cc-main-prize-gfx.jpg') }}" alt="{{ $competition->prize_name }}">
                                                        </picture>
                                                    </div>
                                                    <h3 class="h3">{!! $competition->prize_name !!} </h3>
                                                    <div class="btn-spacer justify-content-center">
                                                        <a href="{{ route('psn_login') }}" class="btn btn-primary cc-adobe-track" {!! adobeTrackData('register click', "home competition", "2", "link") !!}><span class="btn-text">{!! fT('b_home.b_competition.c_anmelden', 'Anmelden') !!}</span></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End [ s6 ] -->
    @endif    

    <!-- Start [ s2 ] -->
    <div class="cc-section cc-s2">
        <div class="cc-section-bg d-none d-md-block">
            <picture>
                <source srcset="{{ textImagePath('b_home.c_how_it_work.a_4k_bg_image', 'cc-s2-bg.jpg') }}" media="(min-width:3000px)">
                <source srcset="{{ textImagePath('b_home.c_how_it_work.a_desktop_full_hd_bg_image', 'cc-s2-bg.jpg') }}" media="(min-width:1920px)">
                <source srcset="{{ textImagePath('b_home.c_how_it_work.a_desktop_bg_bg_image', 'cc-s2-bg.jpg') }}" media="(min-width:1367px)">
                <source srcset="{{ textImagePath('b_home.c_how_it_work.a_tablet_landscape_bg_image', 'cc-s2-bg.jpg') }}" media="(min-width:1024px)">
                <source srcset="{{ textImagePath('b_home.c_how_it_work.a_tablet_bg_image', 'cc-s2-bg.jpg') }}" media="(min-width:768px)">
                <source srcset="{{ textImagePath('b_home.c_how_it_work.a_mobile_bg_image', 'cc-s2-bg.jpg') }}" media="(min-width:1px)">
                <img loading="lazy" decoding="async" src="{{ textImagePath('b_home.c_how_it_work.a_mobile_bg_image', 'cc-s2-bg.jpg') }}" alt="{{ fT('b_home.c_how_it_work.b_title', 'So funktioniert es') }}">
            </picture>
        </div>
        <div class="cc-section-inner">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="cc-s2-wrapper">
                            <div class="row justify-content-center">
                                <div class="col-12 col-lg-10 col-xl-9 col-xxl-7">
                                    <div class="row justify-content-center">
                                        <div class="col-12">
                                            <div class="cc-s2-t">
                                                <div class="text-a text-center text-white">
                                                    <h2 class="h2">{!! fT('b_home.c_how_it_work.b_title', 'So funktioniert es') !!}</h2>
                                                    {!! auto_ptag(fT('b_home.c_how_it_work.c_description', '<p>Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna.</p>')) !!}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="cc-s2-mid ratio ratio-16x9">
                                        <span class="cc-video" data-id="{!! fT('b_home.c_how_it_work.d_youtube_id', 'W6tyHOfrFFw') !!}" id="home-how-to-work-video"></span>
                                        <span class="cc-video-poster">
                                            <picture>
                                                <source srcset="{{ textImagePath('b_home.c_how_it_work.e_tablet_landscape_youtube_image', 'ak-video-poster-b.png') }}" media="(min-width:1024px)">
                                                <source srcset="{{ textImagePath('b_home.c_how_it_work.e_tablet_youtube_image', 'ak-video-poster-b.png') }}" media="(min-width:768px)">
                                                <source srcset="{{ textImagePath('b_home.c_how_it_work.e_mobile_youtube_image', 'ak-video-poster-b.png') }}" media="(min-width:1px)">
                                                <img decoding="async" loading="lazy" src="{{ textImagePath('b_home.c_how_it_work.e_mobile_youtube_image', 'ak-video-poster-b.png') }}" alt="YouTube video poster image">
                                            </picture>
                                            <i class="cc_icon-play-with-outline"><svg role="img"><title>Play icon</title><use xlink:href="#cc_icon-play-with-outline"></use></svg></i>
                                        </span>
                                    </div>

                                    <div class="cc-s2-glry">
                                        <div class="row justify-content-between">
                                            
                                            @php 
                                            $iconImages = ['cc-section-ps.svg','cc-section-ps-a.svg','cc-section-ps-b.svg'];
                                            @endphp

                                            @for($i=1;$i<=3;$i++)
                                                <div class="col-12 col-md-4">
                                                    <div class="text-a text-md-center text-white">
                                                        <div class="cc-s2-thumb ratio ratio-1x1 contain">
                                                            <img decoding="async" loading="lazy" src="{{ textImagePath('b_home.c_how_it_work.f_section_'.$i.'_image', $iconImages[$i-1] ) }}" alt="how it works step {{ $i }}">
                                                        </div>
                                                        
                                                        {!! auto_ptag(fT('b_home.c_how_it_work.g_section_'.$i.'_text', '<p>Porem ipsum dolor sit amet, consectetur adipiscing elit.</p>')) !!}
                                                    </div>
                                                </div>
                                            @endfor
                                        </div>
                                    </div>

                                    <div class="cc-s2-t-btn">
                                        <div class="btn-spacer justify-content-center">
                                            <a href="{{ route('psn_login') }}" class="btn btn-white btn-small cc-adobe-track" {!! adobeTrackData('register click', "home how it work", "3", "link") !!}><span class="btn-text">{!! fT('b_home.b_competition.c_anmelden', 'Anmelden') !!}</span></a>
                                        </div>
                                    </div>

                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End [ s2 ] -->

    @include('site.elements.home-day_of_play_offer')
    
    <!-- Start [ s4 ] -->
    <div class="cc-section cc-s4">

        <div class="cc-section-inner">
            <div class="container">
                <div class="row">
                    <div class="col-12">

                        <div class="cc-s4-wrapper">
                            <div class="cc-s4-head">
                                <div class="text-a text-center">
                                    <h2 class="h2">{!! fT('b_home.e_social.a_title', 'Folge uns auf Social Media') !!}</h2>
                                </div>
                            </div>
                            <div class="cc-s4-glry">
                                <a href="{!! fT('b_home.e_social.b_twitter_link', 'https://x.com/PlayStationDE') !!}" target="_blank" rel="noopener noreferrer" class="cc-s4-glry-box cc-adobe-track" {!! adobeTrackData('twitter click', "home social", "4", "icon") !!}>
                                    <i class="cc_icon-twitter"><svg role="img"><title>Twitter icon</title><use xlink:href="#cc_icon-twitter"></use></svg></i>
                                    <span class="visually-hidden">twitter</span>
                                </a>
                                <a href="{!! fT('b_home.e_social.c_facebook_link', 'http://www.facebook.com/PlayStation.DE') !!}" target="_blank" rel="noopener noreferrer" class="cc-s4-glry-box cc-adobe-track" {!! adobeTrackData('facebook click', "home social", "5", "icon") !!}>
                                    <i class="cc_icon-facebook"><svg role="img"><title>facebook icon</title><use xlink:href="#cc_icon-facebook"></use></svg></i>
                                    <span class="visually-hidden">facebook</span>
                                </a>
                                <a href="{!! fT('b_home.e_social.d_instagram_link', 'http://instagram.com/playstationde') !!}" target="_blank" rel="noopener noreferrer" class="cc-s4-glry-box cc-adobe-track" {!! adobeTrackData('instagram click', "home social", "6", "icon") !!}>
                                    <i class="cc_icon-instagram"><svg role="img"><title>instagram icon</title><use xlink:href="#cc_icon-instagram"></use></svg></i>
                                    <span class="visually-hidden">instagram</span>
                                </a>
                                <a href="{!! fT('b_home.e_social.e_youtube_link', 'https://www.youtube.com/c/InsidePlaystation') !!}" target="_blank" rel="noopener noreferrer" class="cc-s4-glry-box cc-adobe-track" {!! adobeTrackData('youtube click', "home social", "7", "icon") !!}>
                                    <i class="cc_icon-youtube"><svg role="img"><title>youtube icon</title><use xlink:href="#cc_icon-youtube"></use></svg></i>
                                    <span class="visually-hidden">youtube</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <!-- End [ s4 ] -->

    @include('site.elements.home-faq')

@endsection