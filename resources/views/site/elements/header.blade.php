@if($page_type == "home")
    <!-- Start [ sony-bar ] -->
    <div id="sony-bar" class="sony-bar">
        <a href="https://www.sony.net/" target="_blank" class="dtm-no-track cc-adobe-track" {!! adobeTrackData('sony logo click', "header", "1", "icon") !!} rel="noreferrer noopener" aria-label="Sony">
            <span class="sony-logo sony-bar__logo"></span>
        </a>
    </div>
    <!-- End   [ sony-bar ] -->
@endif

<!-- Start [ home-header ] -->
<header class="home-header-wrapper" id="site-header">
    <div class="home-header">

        @if($page_type == "dashboard" && $is_mobile)
            <a href="{{ route('site_page', ['page' => 'dashboard']) }}" class="page-change-nav home-header-logo ratio ratio-1x1 contain cc-adobe-track" {!! adobeTrackData('dashboard click', "header", "2", "image") !!} data-action="back" data-href="dashboard" aria-label="Back to Dashboard">
                <picture>
                    <source srcset="{{ url('public') }}/gfx/ps-logo-white-r.svg" media="(min-width: 1px)">
                    <img decoding="async" src="{{ url('public') }}/gfx/ps-logo-white-r.svg" alt="PlayStation logo">
                </picture>
                <span class="visually-hidden">Dashboard</span>
            </a>
        @else
            <a target="_blank" href="https://www.playstation.com/" rel="noopener noreferrer" class="home-header-logo ratio ratio-1x1 contain cc-adobe-track" {!! adobeTrackData('playstation click', "header", "3", "image") !!}>
                <picture>
                    @if($page_type != "dashboard")
                    <source srcset="{{ url('public') }}/gfx/ps-logo-black.svg" media="(min-width: 768px)">
                    @endif
                    <source srcset="{{ url('public') }}/gfx/ps-logo-white-r.svg" media="(min-width: 1px)">
                    <img decoding="async" src="{{ url('public') }}/gfx/ps-logo-white-r.svg" alt="PlayStation.com logo">
                </picture>
                <span class="visually-hidden">PlayStation.com</span>
            </a>
        @endif

        <nav class="ps-header-social">

            @if($page_type == "home")
                <ul class="ps-navbar-login">
                    <li class="dropdown loginMenu">
                        <a href="#" class="btn btn-white btn-small dropdown-toggle cc-adobe-track" {!! adobeTrackData('dropdown click', "header", "4", "icon") !!} id="loginAccounts" role="button" data-bs-toggle="dropdown" data-bs-display="static" data-bs-auto-close="outside" aria-expanded="false" aria-label="login">
                            <span class="btn-text">{!! fT('a_layout.d_header.c_anmelden', 'Anmelden') !!}</span>
                            <i class="cc_icon-caret-down"><svg role="img"><title>Caret down</title><use xlink:href="#cc_icon-caret-down"></use></svg></i>
                        </a>

                        <ul class="dropdown-menu dropdown-menu-end login-dd-menu" aria-labelledby="loginAccounts">
                            <li>
                                <a href="{{ route('psn_login') }}" class="btn btn-primary cc-adobe-track" {!! adobeTrackData('PSN login click', "header", "5", "icon") !!}>
                                    <i class="cc_icon-ps"><svg role="img"><title>ps icon</title><use xlink:href="#cc_icon-ps"></use></svg></i>
                                    <span class="btn-text">{!! fT('a_layout.d_header.d_log_in_with_psn', 'Log in with PSN') !!}</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('site_google_login') }}" class="btn btn-secondary-outline cc-adobe-track" {!! adobeTrackData('Google login click', "header", "6", "icon") !!}>
                                    <i class="cc_icon-google"><svg role="img"><title>google icon</title><use xlink:href="#cc_icon-google"></use></svg></i>
                                    <span class="btn-text">{!! fT('a_layout.d_header.e_log_in_with_google', 'Log in with Google') !!}</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('site_twitch_login') }}" class="btn btn-twitch cc-adobe-track" {!! adobeTrackData('Twitch login click', "header", "7", "icon") !!}>
                                    <i class="cc_icon-twitch"><svg role="img"><title>twitch icon</title><use xlink:href="#cc_icon-twitch"></use></svg></i>
                                    <span class="btn-text">{!! fT('a_layout.d_header.e_log_in_with_twitch', 'Log in with Twitch') !!}</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            @else
                <div class="ps-tp-space">
                    <div class="ps-tp-cell">
                        <div class="ps-tp-row">
                            <span class="ps-tp-icon"><i class="cc_icon-ticket"><svg role="img"><title>User ticket Icon</title><use xlink:href="#cc_icon-ticket"></use></svg></i></span>
                            <span class="ps-tp-text">x<span class="cc-user-tickets">{{ numberFormat($psnUser->tickets) }}</span></span>
                        </div>
                    </div>
                    <div class="ps-tp-cell">
                        <ul class="ps-navbar-linked">
                            <li class="dropdown linkedMenu">
                                <a href="#" class="ps-tp-row dropdown-toggle cc-adobe-track" {!! adobeTrackData('dropdown click', "header", "8", "icon") !!} id="linked-account" role="button" data-bs-toggle="dropdown" data-bs-display="static" data-bs-auto-close="outside" aria-expanded="false" aria-label="linked">
                                    <span class="ps-profile-avtar ratio ratio-1x1 contain"><img decoding="async" src="{{ avtar_url($psnUser->avtar_url) }}" alt="{{ $psnUser->nickname }}"></span>
                                    <span class="cc-user-nickname ps-tp-text">{!! $psnUser->nickname !!}</span>
                                    <span class="ps-profile-down-arrow"><i class="cc_icon-caret-down"><svg role="img"><title>Caret down icon</title><use xlink:href="#cc_icon-caret-down"></use></svg></i></span>
                                </a>

                                <ul class="dropdown-menu dropdown-menu-end linked-dd-menu" aria-labelledby="linked-account">
                                    <li>
                                        <div class="text-a text-center">
                                            <h2 class="h5">{!! fT('a_layout.d_header.m_linked_accounts', 'Linked Accounts') !!}</h2>
                                        </div>
                                    </li>
                                    <li>
                                        <a href="{{ route('psn_login') }}" class="btn btn-primary{{ $psnUser->user_uuid ? ' pe-none' : '' }} cc-adobe-track" {!! adobeTrackData('psn login click', "header", "9", "icon") !!}>
                                            <i class="cc_icon-ps"><svg role="img"><title>ps icon</title><use xlink:href="#cc_icon-ps"></use></svg></i>
                                            <span class="btn-text">{!! fT('a_layout.d_header.n_psn', 'PSN') !!}</span>
                                            @if($psnUser->user_uuid)
                                                <span class="check-linked-icon green-bg"><i class="cc_icon-check-2"><svg role="img"><title>check icon</title><use xlink:href="#cc_icon-check-2"></use></svg></i></span>
                                            @else
                                                <span class="check-linked-icon"><i class="cc_icon-close-2"><svg role="img"><title>close icon</title><use xlink:href="#cc_icon-close-2"></use></svg></i></span>
                                            @endif
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('site_google_login') }}" class="btn btn-secondary-outline{{ $psnUser->google_user_id ? ' pe-none' : '' }} cc-adobe-track" {!! adobeTrackData('Google login click', "header", "10", "icon") !!}>
                                            <i class="cc_icon-google"><svg role="img"><title>google icon</title><use xlink:href="#cc_icon-google"></use></svg></i>
                                            <span class="btn-text">{!! fT('a_layout.d_header.o_google', 'Google') !!}</span>

                                            @if($psnUser->google_user_id)
                                                <span class="check-linked-icon green-bg"><i class="cc_icon-check-2"><svg role="img"><title>check icon</title><use xlink:href="#cc_icon-check-2"></use></svg></i></span>
                                            @else
                                                <span class="check-linked-icon"><i class="cc_icon-close-2"><svg role="img"><title>close icon</title><use xlink:href="#cc_icon-close-2"></use></svg></i></span>
                                            @endif
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('site_twitch_login') }}" class="btn btn-twitch{{ $psnUser->twitch_user_id ? ' pe-none' : '' }} cc-adobe-track" {!! adobeTrackData('twitch login click', "header", "11", "icon") !!}>
                                            <i class="cc_icon-twitch"><svg role="img"><title>twitch icon</title><use xlink:href="#cc_icon-twitch"></use></svg></i>
                                            <span class="btn-text">{!! fT('a_layout.d_header.p_twitch', 'Twitch') !!}</span>
                                            @if($psnUser->twitch_user_id)
                                                <span class="check-linked-icon green-bg"><i class="cc_icon-check-2"><svg role="img"><title>check icon</title><use xlink:href="#cc_icon-check-2"></use></svg></i></span>
                                            @else
                                                <span class="check-linked-icon"><i class="cc_icon-close-2"><svg role="img"><title>close icon</title><use xlink:href="#cc_icon-close-2"></use></svg></i></span>
                                            @endif
                                        </a>
                                    </li>
                                    <li>
                                        <form name="redeem-code" class="cc-redeem-code-form cc-form-a" action="{{ route('site_redeem_code') }}" method="post">
                                            <div class="form-group">
                                                <label class="form-label" for="cc-reedem-code">{!! fT('d_pages.l_redeem_code.a_redeem_code_label', 'Gib hier deinen Code ein') !!}</label>
                                                <div class="form-control-wrapper">
                                                    <input type="text" name="code" autocomplete="off" class="form-control" placeholder="{!! fT('d_pages.l_redeem_code.a_redeem_code_placeholder', 'XXX-XXX-XXX') !!}" id="cc-reedem-code">
                                                    <span class="invalid-feedback cc-redeem-code-error"></span>
                                                </div>
                                            </div>
                                            <div class="btn-spacer justify-content-center">
                                                <button type="submit" name="btn-submit" class="btn btn-primary">
                                                    <span class="btn-text">{!! fT('d_pages.l_redeem_code.b_submit_btn', 'Reedem') !!}</span>
                                                </button>
                                            </div>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </div>
                </div>
            @endif

            <ul class="ps-navbar-share">

                @if($page_type == "dashboard")

                    <li class="shareMenu">
                        <a href="{{ route('site_page', ['page' => 'notifications']) }}" class="ps-share-link transaction-list-link page-change-nav {{ ($psnUser->fcm_token) ? '' : 'unsubscribe' }} {{ ($unread_points_count > 0) ? 'with-dot' : '' }} cc-adobe-track" data-href="notifications" {!! adobeTrackData('notifications click', "header", "12", "icon") !!}>
                            <span class="ps-share-icon">
                                <i class="cc_icon-notification">
                                    <svg role="img"><title>Notification icon</title><use xlink:href="#cc_icon-notification"></use></svg>
                                    <svg role="img"><title>No-Notification icon</title><use xlink:href="#cc_icon-no-notification"></use></svg>
                                </i>
                            </span>
                            <span class="visually-hidden">Notification</span>
                        </a>
                    </li>

                @endif

                @php $shareIcon = $page_type == "home" ? "cc_icon-share" : "cc_icon-share-2"; @endphp

                @if($is_mobile)
                    <li class="shareMenu d-none cc-mobile">
                        <button type="button" class="ps-share-link btn-share cc-adobe-track" {!! adobeTrackData('mobile share click', "header", "13", "icon") !!} >
                            <span class="ps-share-icon">
                                <i class="{{ $shareIcon }}"><svg role="img"><title>share icon</title><use xlink:href="#{{ $shareIcon }}"></use></svg></i>
                            </span>
                            <span class="visually-hidden">Share</span>
                        </button>
                    </li>
                @else
                    <li class="dropdown shareMenu d-none cc-desktop">
                        <button type="button" class="ps-share-link dropdown-toggle cc-adobe-track" {!! adobeTrackData('social share dropdown click', "header", "14", "icon") !!} id="shareMenuLink" role="button" data-bs-toggle="dropdown" data-bs-display="static" data-bs-auto-close="outside" aria-expanded="false">
                            <span class="ps-share-icon">
                                <i class="{{ $shareIcon }}"><svg role="img"><title>share icon</title><use xlink:href="#{{ $shareIcon }}"></use></svg></i>
                            </span>
                            <span class="visually-hidden">Share</span>
                        </button>
                        <ul class="dropdown-menu share-dd-menu dropdown-menu-end" aria-labelledby="shareMenuLink">
                            <li>
                                <a class="dropdown-item btn-share facebook cc-adobe-track" {!! adobeTrackData('facebook click', "header", "15", "icon") !!}>
                                    <i class="cc_icon-facebook"><svg role="img"><title>facebook icon</title><use xlink:href="#cc_icon-facebook"></use></svg></i>
                                    {!! fT('a_layout.d_header.f_auf_facebook_teilen', 'auf Facebook teilen') !!}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item btn-share twitter cc-adobe-track" {!! adobeTrackData('Twitter click', "header", "16", "icon") !!}>
                                    <i class="cc_icon-twitter"><svg role="img"><title>twitter icon</title><use xlink:href="#cc_icon-twitter"></use></svg></i>
                                    {!! fT('a_layout.d_header.g_auf_twitter_teilen', 'auf Twitter teilen') !!}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item btn-share email cc-adobe-track" {!! adobeTrackData('Mail click', "header", "17", "icon") !!}>
                                    <i class="cc_icon-mail"><svg role="img"><title>mail icon</title><use xlink:href="#cc_icon-mail"></use></svg></i>
                                    {!! fT('a_layout.d_header.h_via_mail_senden', 'via Mail senden') !!}
                                </a>
                            </li>
                        </ul>
                    </li>
                @endif    
            </ul>

        </nav>
    </div>

</header>
<!-- End   [ home-header ] -->
