@php
$earnedTickets = $trophy_delta->tickets;

if($last_credits_tickets)  {
   
    foreach($last_credits_tickets as $cType => $cTicket)
        $earnedTickets += $cTicket;
}

$userTrophyRank = userTrophyRank($trophy_delta->trophy_level);
@endphp

<!-- Start [ trophies animation ] -->
<div class="cc-trophies-page">
    <div class="cc-trophies-page-in">
        <div class="dop-logo ratio">
            <img decoding="async" loading="lazy" src="{{ url('public/gfx/'.($is_mobile ? 'ps-intro-logo.png' : 'dop-logo.svg')) }}" alt="Days of play logo">
        </div>
        <div class="cc-trophies-box">
            <div class="cc-trophies-head text-a">
                <h2 {!! $is_mobile ? 'class="h3"' : '' !!}>{!! fT('d_pages.j_trophy_animation.a_title', 'Trophy Inforamation') !!}</h2>
            </div>

            <div class="cc-trophies-main-row">
                <div class="cc-trophies-main-l">
                    <div class="cc-trophies-leval-row">
                        <div class="cc-trophies-leval-icon ratio ratio-1x1">
                            <img decoding="async" loading="lazy" src="{{ url('public/gfx/trophy_level_'.$userTrophyRank.'.png') }}" alt="PSN Trophy Leval {{ $userTrophyRank }}">
                        </div>
                        <div class="cc-animation-group h1">
                            <div class="cc-animation-inner">
                                {!!  animationCountHtmls($trophy_delta->trophy_level, 'cc-trophies-leval-count-text')  !!}
                            </div>
                        </div>
                    </div>
                    <div class="cc-trophies-total-group">
                        <span class="cc-trophies-leval-total-text h3">{!! fT('d_pages.j_trophy_animation.b_total', 'Total') !!}</span>
                        <div class="cc-animation-group h2">
                            <div class="cc-animation-inner">
                                {!!  animationCountHtmls(($trophy_delta->bronze + $trophy_delta->silver + $trophy_delta->gold + $trophy_delta->platinum), 'cc-trophies-leval-total-count')  !!}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="cc-trophies-main-r">
                    <div class="cc-trophy-point-row">
                        <div class="cc-trophy-point-cell">
                            <div class="cc-trophy-icon">
                                <i class="cc_icon-bronze"><svg role="img"><title>Bronze icon</title><use xlink:href="#cc_icon-bronze"></use></svg></i>
                            </div>
                            <div class="cc-trophy-point-count">
                                <div class="cc-animation-group{{ $is_mobile ? '' : ' h2' }}">
                                    <div class="cc-animation-inner">
                                        {!!  animationCountHtmls($trophy_delta->bronze, 'cc-trophy-point-count-text')  !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="cc-trophy-point-cell">
                            <div class="cc-trophy-icon">
                                <i class="cc_icon-silver"><svg role="img"><title>Silver icon</title><use xlink:href="#cc_icon-silver"></use></svg></i>
                            </div>
                            <div class="cc-trophy-point-count">
                                <div class="cc-animation-group{{ $is_mobile ? '' : ' h2' }}">
                                    <div class="cc-animation-inner">
                                        {!!  animationCountHtmls($trophy_delta->silver, 'cc-trophy-point-count-text')  !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="cc-trophy-point-cell">
                            <div class="cc-trophy-icon">
                                <i class="cc_icon-gold"><svg role="img"><title>Gold icon</title><use xlink:href="#cc_icon-gold"></use></svg></i>
                            </div>
                            <div class="cc-trophy-point-count">
                                <div class="cc-animation-group{{ $is_mobile ? '' : ' h2' }}">
                                    <div class="cc-animation-inner">
                                        {!!  animationCountHtmls($trophy_delta->gold, 'cc-trophy-point-count-text')  !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="cc-trophy-point-cell">
                            <div class="cc-trophy-icon">
                                <i class="cc_icon-platinum"><svg role="img"><title>Platinum icon</title><use xlink:href="#cc_icon-platinum"></use></svg></i>
                            </div>
                            <div class="cc-trophy-point-count">
                                <div class="cc-animation-group{{ $is_mobile ? '' : ' h2' }}">
                                    <div class="cc-animation-inner">
                                        {!!  animationCountHtmls($trophy_delta->platinum, 'cc-trophy-point-count-text')  !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row trophies-ticket">

                @if($last_credits_tickets && isset($last_credits_tickets['psplus-membership']) && $last_credits_tickets['psplus-membership'] > 0)

                    <div class="col-{!! $is_mobile ? '12' : '6' !!}">
                        <div class="cc-trophies-head text-a">
                            <h2 {!! $is_mobile ? 'class="h3"' : '' !!}>{!! fT('d_pages.j_trophy_animation.c_optional_ps_information', 'Optional PS+ Information') !!}</h2>
                        </div>
                        <div class="cc-trophies-pluse">
                            <i class="cc_icon-ps-plus"><svg role="img"><title>ps plus icon</title><use xlink:href="#cc_icon-ps-plus"></use></svg></i>
                            <span class="cc-trophies-pluse-check">
                                <i class="cc_icon-check-2"><svg role="img"><title>Check-2 icon</title><use xlink:href="#cc_icon-check-2"></use></svg></i>
                            </span>
                        </div>
                    </div>

                @endif

                <div class="col-{!! $is_mobile ? '12' : '6' !!}">
                    <div class="cc-trophies-head text-a">
                        <h2 {!! $is_mobile ? 'class="h3"' : '' !!}>{!! fT('d_pages.j_trophy_animation.d_ticket_result', 'Ticket Result') !!}</h2>
                    </div>
                    <div class="cc-tiket-result-row">
                        <div class="cc-trophies-pluse">
                            <i class="cc_icon-ticket-gold"><svg role="img"><title>ticket icon</title><use xlink:href="#cc_icon-ticket-gold"></use></svg></i>
                        </div>
                        <div class="cc-tiket-result-count">
                            <span class="cc-tiket-result-x h1">X</span>
                            <div class="cc-animation-group h1">
                                <div class="cc-animation-inner">
                                    {!!  animationCountHtmls($earnedTickets, 'cc-tiket-result-text')  !!}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="cc-section-bg cc-trophies-bg">
        @if($is_mobile)
            <img decoding="async" src="{{ url('public') }}/gfx/cc-bg-mob.jpg" alt="Countdown bg">
        @else
            <picture>
                <source srcset="{{ url('public') }}/gfx/cc-bg.jpg" media="(min-width:3000px)">
                <source srcset="{{ url('public') }}/gfx/cc-bg.jpg" media="(min-width:1920px)">
                <source srcset="{{ url('public') }}/gfx/cc-bg.jpg" media="(min-width:1367px)">
                <source srcset="{{ url('public') }}/gfx/cc-bg.jpg" media="(min-width:1024px)">
                <source srcset="{{ url('public') }}/gfx/cc-bg.jpg" media="(min-width:768px)">
                <source srcset="{{ url('public') }}/gfx/cc-bg.jpg" media="(min-width:1px)">
                <img decoding="async" src="{{ url('public') }}/gfx/cc-bg.jpg" alt="Countdown bg">
            </picture>
        @endif
    </div>
</div>
<!-- End [ trophies animation ] -->