@extends('site.layouts.layout')
@section('content')

    @php
    $app_inputs = array(
        array(
            'heading' => 'App Settings',
            'fields' => array(
                array('name' => 'app_settings.allowed_ips', 'label' => 'Allowed IPs (Comma Separated)', 'type' => 'text'),
                array('name' => 'app_settings.allowed_ips_dev_gate', 'label' => 'Allowed IPs for dev gate (Comma Separated)', 'type' => 'text'),
                array('name' => 'app_settings.default_lang_id', 'label' => 'Default Language Id', 'type' => 'select', 'options' => $language_options, 'optionNoneSelected' => 'Select Language'),
                array('name' => 'app.task_attachment_parent_folder', 'label' => 'Task Attachment Parent Folder (Development Shared Drive)', 'type' => 'text'),
                array('name' => 'llm.main_model', 'label' => 'LLM Main Model', 'type' => 'text'),

                /*
                array('name' => 'app.google_recaptcha_site_key', 'label' => 'Google Recaptcha Site Key', 'type' => 'text'),
                array('name' => 'app.google_recaptcha_secret_key', 'label' => 'Google Recaptcha Secret Key', 'type' => 'text'),
                array('name' => 'app.google_recaptcha_min_score', 'label' => 'Google Recaptcha Min Score', 'type' => 'text'),
                */
            )
        ),
        array(
            'heading' => 'Store Sale Settings',
            'fields' => array(
                array('name' => 'ai_gen.add_discount_percent', 'label' => 'Add discount percent to ai texts', 'type' => 'text'),
            )
        ),
        array(
            'heading' => 'UGC Planner Settings',
            'fields' => array(
                array('name' => 'ugc.approval_followers_count', 'label' => 'Channel followers count for Auto post approval', 'type' => 'text'),
            )
        ),
        array(
            'heading' => 'Facebook App',
            'fields' => array(
                array('name' => 'facebook.app_id', 'label' => 'Facebook App ID', 'type' => 'text'),
                array('name' => 'facebook.app_secret', 'label' => 'Facebook App Secret', 'type' => 'text'),
                array('name' => 'facebook.app_graph_version', 'label' => 'Facebook App Version', 'type' => 'text'),
                array('name' => 'facebook.access_token', 'label' => 'Facebook Access Token', 'type' => 'text'),
                array('name' => 'facebook.token_expire_time', 'label' => 'Facebook Token Expire Time', 'type' => 'text'),
            )
        ),
    );

    $post_limit_inputs = [];
    foreach (socialChannels() as $channel => $value)  {

        if(!in_array($channel, ['youtube', 'blog']))  {

            $post_limit_inputs[] = array('name' => 'social_planning.'.$channel.'.day_posts_count', 'label' => ucfirst($channel).' Day Posts Count', 'type' => 'text');
        }
    }

    $social_planning_inputs = array(
        array(
            'heading' => 'Social Planning Posting Account Settings',
            'fields' => array(
                array('name' => 'social.facebook_account_id', 'label' => 'Facebook Page Account', 'type' => 'select', 'options' => (isset($social_account_options['facebook-page']) ? $social_account_options['facebook-page'] : []), 'optionNoneSelected' => 'Select Account'),
                array('name' => 'social.instagram_account_id', 'label' => 'Instagram Account', 'type' => 'select', 'options' => (isset($social_account_options['instagram']) ? $social_account_options['instagram'] : []), 'optionNoneSelected' => 'Select Account'),
                array('name' => 'social.twitter_account_id', 'label' => 'Twitter Account', 'type' => 'select', 'options' => (isset($social_account_options['twitter']) ? $social_account_options['twitter'] : []), 'optionNoneSelected' => 'Select Account'),
                array('name' => 'social.threads_account_id', 'label' => 'Threads Account', 'type' => 'select', 'options' => (isset($social_account_options['threads']) ? $social_account_options['threads'] : []), 'optionNoneSelected' => 'Select Account'),
                array('name' => 'social.social_posts_token', 'label' => 'Social Post Url Token', 'type' => 'text'),
            )
        ),
        array(
            'heading' => 'Social Posts per Channel per Day',
            'fields' => $post_limit_inputs
        ),
        array(
            'heading' => 'Social Channel Colors',
            'fields' => array(
                array('name' => 'social.facebook_color_code', 'label' => 'Facebook Color Code', 'type' => 'text'),
                array('name' => 'social.twitter_color_code', 'label' => 'Twitter Color Code', 'type' => 'text'),
                array('name' => 'social.instagram_color_code', 'label' => 'Instagram Color Code', 'type' => 'text'),
                array('name' => 'social.threads_color_code', 'label' => 'Threads Color Code', 'type' => 'text'),
                array('name' => 'social.stories_color_code', 'label' => 'Stories Color Code', 'type' => 'text'),
                array('name' => 'social.youtube_color_code', 'label' => 'Youtube Color Code', 'type' => 'text'),
                array('name' => 'social.blog_color_code', 'label' => 'Blog Color Code', 'type' => 'text'),
            )
        ),
        array(
            'heading' => 'Social Post Type Colors',
            'fields' => array(
                array('name' => 'social.link_color_code', 'label' => 'Link Post Type Color Code', 'type' => 'text'),
                array('name' => 'social.bild_color_code', 'label' => 'Bild Post Type Color Code', 'type' => 'text'),
                array('name' => 'social.video_color_code', 'label' => 'Video Post Type Color Code', 'type' => 'text'),
                array('name' => 'social.other_color_code', 'label' => 'Other Post Type Color Code', 'type' => 'text'),
            )
        ),
        array(
            'heading' => 'Social Post App Details',
            'fields' => array(
                array('name' => 'social_post.facebook.app_id', 'label' => 'Social Post Facebook App ID', 'type' => 'text'),
                array('name' => 'social_post.facebook.app_secret', 'label' => 'Social Post Facebook App Secret', 'type' => 'text'),
                array('name' => 'social_post.facebook.app_graph_version', 'label' => 'Social Post Facebook App Graph Version', 'type' => 'text'),

                array('name' => 'social_post.twitter.api_key', 'label' => 'Social Post Twitter Api Key', 'type' => 'text'),
                array('name' => 'social_post.twitter.api_secret', 'label' => 'Social Post Twitter Api Secret', 'type' => 'text'),
                array('name' => 'social_post.twitter.consumer_key', 'label' => 'Social Post Twitter Consumer Key', 'type' => 'text'),
                array('name' => 'social_post.twitter.consumer_secret', 'label' => 'Social Post Twitter Consumer Secret', 'type' => 'text'),

                array('name' => 'social_post.threads.app_id', 'label' => 'Social Post Threads App ID', 'type' => 'text'),
                array('name' => 'social_post.threads.app_secret', 'label' => 'Social Post Threads App Secret', 'type' => 'text'),
            )
        ),
        array(
            'heading' => 'Advent Social Post Setting',
            'fields' => array(
                array('name' => 'social_post.advent.main_topic_name', 'label' => 'Main Topic Name', 'type' => 'text'),
                array('name' => 'social_post.advent.main_gdrive_folder_id', 'label' => 'Main gDrive Folder ID', 'type' => 'text'),
                array('name' => 'social_post.advent.main_gdrive_folder_name', 'label' => 'Main Gdrive Folder Name', 'type' => 'text'),
                array('name' => 'social_post.advent.assigned_to', 'label' => 'Assign to Person', 'type' => 'select', 'options' => @$writer_users, 'optionNoneSelected' => 'Person Selection'),
            )
        )
    );

    $content_freigaben_cron_inputs = [];
    $post_statuses = ['draft' => 'Draft', 'creator' => 'Creator', 'ready-to-prepare' => 'Ready to Prepare'];
    foreach ($post_statuses as $post_status => $status_label) {

        $task_title = $post_status == 'ready-to-prepare' ?  fT('a_clickup.a_ugc_planner_'.$post_status.'.a_task_title') :
                                                            fT('a_clickup.a_content_freigaben_'.$post_status.'.a_task_title');

        $content_freigaben_cron_inputs[] = array(
            'name' => 'content_freigaben.'.$post_status.'_cron_status',
            'label' => $task_title." (".$status_label.")",
            'type' => 'checkbox_active'
        );
    }

    $clickup_inputs = array(
        array(
            'heading' => 'Click Up Settings',
            'fields' => array(
                array('name' => 'clickup.api_token', 'label' => 'Clickup Api Token (<a href="https://app.clickup.com/9018187363/settings/apps" target="_blank">ClickUp > Settings > Apps > API Token</a>)', 'type' => 'text'),
            )
        ),
        array(
            'heading' => 'Social Planning Settings',
            'fields' => array(
                //array('name' => 'social_planning.clickup_space_id', 'label' => 'Clickup Space ID', 'type' => 'text'),
                array('name' => 'social_planning.clickup_list_id', 'label' => 'Clickup List ID', 'type' => 'text'),
            )
        ),
        array(
            'heading' => 'Content Freigaben Settings',
            'fields' => array(
                array('name' => 'content_freigaben.clickup_list_id', 'label' => 'Clickup List ID', 'type' => 'text'),
                //array('name' => 'content_freigaben.clickup_graphic_list_id', 'label' => 'Clickup Graphic List ID', 'type' => 'text'),
                array('name' => 'content_freigaben.graphic_google_space_id', 'label' => 'Graphic Google Space ID', 'type' => 'text'),
            )
        ),
        array(
            'heading' => 'Content Freigaben Clickup Task Creation Settings',
            'fields' => $content_freigaben_cron_inputs
        ),
    );

    $tabs = array(
        'app' => array(
            'label' => 'APP',
            'boxes' => $app_inputs,
        ),
        'social_planning' => array(
            'label' => 'Social Planning',
            'boxes' => $social_planning_inputs,
        ),
        'clickup' => array(
            'label' => 'Clickup',
            'boxes' => $clickup_inputs,
        ),
    );
    @endphp

    <!--begin::Content wrapper-->
    <div class="d-flex flex-column flex-column-fluid">
        <!--begin::Toolbar-->
        <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
            <!--begin::Toolbar container-->
            <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
                <!--begin::Page title-->
                <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                    <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Manage App Settings</h1>
                </div>
                <!--end::Page title-->

                <div class="d-flex align-items-center gap-2 gap-lg-3">
                    <a href="{{ route('site_clickup_webhook_list') }}" class="btn btn-sm fw-bold btn-info" target="_blank">ClickUp Webhooks</a>
                </div>
            </div>
            <!--end::Toolbar container-->
        </div>
        <!--end::Toolbar-->

        <!--begin::Content-->
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <!--begin::Content container-->
            <div id="kt_app_content_container" class="app-container container-xxl">
                <!--begin::Contacts App- Add New Contact-->
                <div class="row g-7" id="app_setting_form" >

                    <!--begin::Content-->
                    <div class="col-xl-12">
                        <!--begin::Contacts-->
                        <div class="card card-flush h-lg-100">

                            <!--begin::Card body-->
                            <div class="card-body pt-5">

                                <div class="mb-5 hover-scroll-x">
                                    <div class="d-grid">
                                        <ul class="nav nav-tabs flex-nowrap text-nowrap">

                                            @foreach($tabs as $tabKey => $tab)
                                                <li class="nav-item">
                                                    <a class="nav-link btn btn-active-light btn-color-gray-600 btn-active-color-primary rounded-bottom-0 {{ ($tabKey == "app" ? 'active' : '') }}" data-bs-toggle="tab" href="#{{ $tabKey }}-tab">{{ $tab['label'] }}</a>
                                                </li>
                                            @endforeach

                                        </ul>
                                    </div>
                                </div>

                                <div class="tab-content" id="myTabContent">

                                    @foreach($tabs as $tabKey => $tab)

                                        <div class="tab-pane fade {{ ($tabKey == "app" ? 'show active' : '') }}" id="{{ $tabKey }}-tab" role="tabpanel">

                                            @foreach($tab['boxes'] as $box)

                                                <div class="card card-bordered mb-6">
                                                    <div class="card-header" style="min-height: 40px;">
                                                        <h3 class="card-title">{{ $box['heading'] }}</h3>
                                                    </div>

                                                    <div class="card-body">
                                                        @foreach($box['fields'] as $field)

                                                            @if($field['type'] == "textarea")

                                                                @include('site.form.textarea_component', ['name' => $field['name'], 'value' => @$setting_arr[$field['name']],
                                                                    'id' => str_replace(".", "_", $field['name']), 'label' => $field['label']])

                                                            @elseif($field['type'] == "select")

                                                                @include('site.form.select_component', ['name' => $field['name'], 'value' => @$setting_arr[$field['name']],'options' => $field['options'], 'optionNoneSelected' => $field['optionNoneSelected'],'id' => str_replace(".", "_", $field['name'])
                                                                , 'label' => $field['label']])

                                                            @elseif($field['type'] == "checkbox_active")

                                                                @include('site.form.checkbox_active_component', ['name' => $field['name'], 'value' => 1, 'label' => $field['label'], 'checked'=> (@@$setting_arr[$field['name']] ?: "") ])

                                                            @else

                                                                @include('site.form.text_component', ['name' => $field['name'], 'value' => @$setting_arr[$field['name']],
                                                                    'id' => str_replace(".", "_", $field['name']), 'label' => $field['label'], "inputCls" => @$field['inputCls']])

                                                            @endif

                                                        @endforeach
                                                    </div>
                                                </div>

                                            @endforeach

                                        </div>

                                    @endforeach

                                </div>

                            </div>
                            <!--end::Card body-->
                        </div>
                        <!--end::Contacts-->
                    </div>
                    <!--end::Content-->

                </div>
                <!--end::Contacts App- Add New Contact-->
            </div>
            <!--end::Content container-->
        </div>
        <!--end::Content-->
    </div>
    <!--end::Content wrapper-->

@endsection
