<div class="welcome-right-pane-content">
    <div class="welcome-right-pane-head">
        <h2 class="h4">{!! fT('g_saml_page.b_step_2.c_right_side_tile', 'Bitte wählen Sie eine Kategorie') !!}</h2>
        <hr>                                
    </div>
    <div class="sc-space with-bottom-glow">
        <div class="sc-scroll">
            <div class="row sc-list-group">

                @foreach ($categories as $category)
                    
                    <div class="col-6 col-sm-4 col-lg-6 col-xl-4 col-xxl-3 sc-list-item">
                        <div class="sc-list-box">
                            <label class="gs-toggle-a">
                                <input type="radio" value="{{ $category->id }}" name="card_category" class="card_category">
                                <span class="gs-toggle-a-ui-wrap">
                                    <span class="gs-toggle-a-check"><i class="cc_icon-check"><svg focusable="false" tabindex="-1"><use xlink:href="#cc_icon-check"></use></svg></i></span>
                                    <span class="gs-toggle-a-ui">
                                        <span class="gs-toggle-a-ui-imgbox cc_imgbox">
                                            <img loading="lazy" src="{{ getDefaultImagePath($category->list_image, 'car-thumb-360x203.jpg') }}" alt="{{ $category->title }}">
                                        </span>
                                    </span>
                                    <span class="gs-toggle-a-ui-text">
                                        <span class="gs-toggle-a-ctname">{!! $category->title !!}</span>
                                    </span>
                                </span>
                                <span class="gs-toggle-a-overlay">
                                    <span class="btn btn-outline-white"><span class="btn-text">{!! fT('g_saml_page.b_step_2.d_kategorie_gewahlt', 'Kategorie gewählt') !!}</span></span>
                                </span>
                            </label>
                        </div>
                    </div>

                @endforeach

            </div>
        </div>
    </div>

    <div class="welcome-right-footer">
        <div class="wr-footer-left">
            <button type="button" class="btn btn-secondary-alt wc-right-prev-link-2"><span class="btn-text">{!! fT('g_saml_page.b_step_2.e_zuruck', 'Zurück') !!}</span></button>
        </div>
        <div class="wr-footer-right">
            <button type="button" class="btn btn-primary wc-right-next-link-2 disabled"><span class="btn-text">{!! fT('g_saml_page.b_step_2.f_karten_der_kategorie_zeigen', 'Karten der Kategorie zeigen') !!}</span></button>
        </div>
    </div>
</div>