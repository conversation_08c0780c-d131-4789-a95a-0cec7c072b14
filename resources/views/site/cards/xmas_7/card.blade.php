<style>
    .xmas7_char1{width:6.938em;position:absolute;top:5.188em;left:12.438em;display:block; opacity: 0;}
    .xmas7_char2{width:8.313em;position:absolute;top:5.375em;left:20.063em;display:block; opacity: 0;}
    .xmas7_char3{width:5.938em;position:absolute;top:4.625em;left:29.375em;display:block; opacity: 0;}
    .xmas7_char1 > img, .xmas7_char2 > img, .xmas7_char3 > img{width:100%;height:auto;display:block;position:absolute;}
    .xmas7_char1 > img.xmas7_char1_img1{top:0;left:0;z-index:2;}
    .xmas7_char1 > img.xmas7_char1_img2{top:5.250em;left:0;z-index:1;}
    .xmas7_char1 > img.xmas7_char1_img3{top:5.000em;left:0;z-index:0;}
    .xmas7_char2 > img.xmas7_char2_img1{top:0;left:0;z-index:0;}
    .xmas7_char2 > img.xmas7_char2_img2{top:6.313em;left:0;z-index:1;}
    .xmas7_char3 > img.xmas7_char3_img1{top:0;left:0;z-index:1;}
    .xmas7_char3 > img.xmas7_char3_img2{top:0.813em;left:3.063em;z-index:0;}
    .xmas7_star{width:42.500em;height:auto;display:block;position:absolute;top:15.500em;left:3.125em; opacity: 0;}
    .xmas7_txt{width:19.438em;height:auto;display:block;position:absolute;bottom:3.500em;left:14.563em; opacity: 0;}
</style>

<!--Start[for desktop]-->
<div class="sep-card-container">
    <div class="sep-card-desktop">
        <div class="sep-card-desktop-container">                                                                
            <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/card.jpg" alt="Card" class="card-img"> 
            <span class="xmas7_char1">
                <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/xmas7_char1_img1.png" alt="" class="xmas7_char1_img1">
                <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/xmas7_char1_img2.png" alt="" class="xmas7_char1_img2">
                <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/xmas7_char1_img3.png" alt="" class="xmas7_char1_img3">
            </span>
            <span class="xmas7_char2">
                <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/xmas7_char2_img1.png" alt="" class="xmas7_char2_img1">
                <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/xmas7_char2_img2.png" alt="" class="xmas7_char2_img2">
            </span>                                                      
            <span class="xmas7_char3">
                <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/xmas7_char3_img1.png" alt="" class="xmas7_char3_img1">
                <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/xmas7_char3_img2.png" alt="" class="xmas7_char3_img2">
            </span>
            <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/xmas7_star.png" alt="" class="xmas7_star">
            <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/xmas7_txt.png" alt="" class="xmas7_txt">
        </div>
    </div>
</div>
<!--End [for desktop]--> 

   
<script nonce="{{ csp_nonce() }}" src="{{ url('public') }}/js/gsap.min.js"></script> 

<script nonce="{{ csp_nonce() }}">
	
	function initCard() {	
		
        var tl = new gsap.timeline({repeat:-1});

        tl.to(".xmas7_char1, .xmas7_char2, .xmas7_char3, .xmas7_star, .xmas7_txt", { duration: .5, opacity: 1, ease:Expo.easeInOut });	
        tl.fromTo(".xmas7_char1 > img", { scale:0 }, { duration: 1.5, scale: 1, ease:Expo.easeInOut });
        tl.fromTo(".xmas7_char2 > img", { scale:0 }, { duration: 1.5, scale: 1, ease:Expo.easeInOut }, "-=1.4");
        tl.fromTo(".xmas7_char3 > img", { scale:0 }, { duration: 1.5, scale: 1, ease:Expo.easeInOut }, "-=1.3");
        tl.fromTo(".xmas7_star", { scale:0 }, { duration: 1.5, scale: 1, ease:Expo.easeInOut }, "-=1.2");
        tl.fromTo(".xmas7_txt", { marginBottom:-300 }, { duration: 1.5, marginBottom: 0, ease:Bounce.easeInOut }, "-=1.1");
        tl.to(".xmas7_char1 > img.xmas7_char1_img2", { duration: 1.5, scale:0.6, ease:Expo.easeInOut });
        tl.to(".xmas7_char1 > img.xmas7_char1_img2", { duration: 1.5, scale: 1, ease:Expo.easeInOut });
        tl.fromTo(".xmas7_char2 > img.xmas7_char2_img2", { rotation:1 }, { duration: .5, rotation:45, ease:Expo.easeInOut });			
        tl.to(".xmas7_char3 > img.xmas7_char3_img2", { duration: .5, scale:0.6, ease:Expo.easeInOut });
        tl.to(".xmas7_char3 > img.xmas7_char3_img2", { duration: .5, scale:1, ease:Expo.easeInOut });
        tl.to(".xmas7_char1, .xmas7_char2, .xmas7_char3, .xmas7_star, .xmas7_txt", { duration: 1, opacity: 0, delay: 2, ease:Expo.easeInOut });		
			 			 			 		 			     
    }

    var isinitcard = false;

    jQuery(window).on('initcard', function() {
        if(isinitcard == false) {
            isinitcard = true;

            if(window.initCard) {
                initCard();
            } 
        }        		       
    });

</script>