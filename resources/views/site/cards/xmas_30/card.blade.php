<style>
    .sep-card-desktop-container > .card-img { opacity: 1; }
    .sep-card-desktop-container > .tree-1 { display: block; opacity: 0; position: absolute; top: 7.735849056603774%; left: 13%; width: 18.375%; height: auto; margin-left: -32%; }
    .sep-card-desktop-container > .tree-2 { display: block; opacity: 0; position: absolute; top: 49.**************%; left: 42.125%; width: 18.375%; height: auto; margin-left: -61%; }
    .sep-card-desktop-container > .star-1 { display: block; opacity: 0; position: absolute; top: 8.113207547169811%; left: 35.5%; width: 29%; height: auto; margin-top: -33%; }
    .sep-card-desktop-container > .star-2 { display: block; opacity: 0; position: absolute; top: 52.83018867924528%; left: 7.5%; width: 29.375%; height: auto; margin-top: 32%; }
    .sep-card-desktop-container > .text { display: block; opacity: 0; position: absolute; top: 35.66037735849057%; left: 68.125%; width: 23%; height: auto; margin-top: 44%; transform: scale(.75); }
</style>

<!--Start[for desktop]-->
<div class="sep-card-container">
    <div class="sep-card-desktop">
        <div class="sep-card-desktop-container">
            <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/card.jpg" alt="Card" class="card-img">                                
            <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/tree-1.png" alt="" class="card-shape tree-1">
            <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/tree-2.png" alt="" class="card-shape tree-2">
            <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/star-1.png" alt="" class="card-shape star-1">
            <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/star-2.png" alt="" class="card-shape star-2">
            <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/text.png" alt="" class="card-shape text">
        </div>
    </div>
</div>
<!--End [for desktop]-->


<script nonce="{{ csp_nonce() }}" src="{{ url('public') }}/js/gsap.min.js"></script> 

<script nonce="{{ csp_nonce() }}">
	
	function initCard() {	
		
        var tl = new gsap.timeline({repeat:-1});			
                                                                    
        tl.fromTo(".tree-1", { opacity:0, marginLeft: '-32%' }, { duration: 1, opacity:1, marginLeft: 0, delay:1 });
        tl.fromTo(".tree-2", { opacity:0, marginLeft: '-61%' }, { duration: 1, opacity:1, marginLeft: 0 }, "-=1");
        tl.fromTo(".star-1", { opacity:0, marginTop: '-33%' }, { duration: 1, opacity:1, marginTop: 0 });
        tl.fromTo(".star-2", { opacity:0, marginTop: '32%' }, { duration: 1, opacity:1, marginTop: 0 }, "-=1");
        tl.fromTo(".text", { opacity:0, marginTop: '44%', scale: .75 }, { duration: 1.5, opacity:1, marginTop: 0, scale: 1, ease: Power1.easeInOut });

        tl.to(".tree-1", { duration: .1, x:"+=05", yoyo:true, repeat:3, delay:2 });
        tl.to(".star-1", { duration: 1, rotation:360, ease:Power0.easeNone, delay:1.5 });
        tl.to(".tree-2", { duration: .1, x:"+=05", yoyo:true, repeat:3, delay:1 });
        tl.to(".star-2", { duration: 1, rotation:-360, ease:Power0.easeNone, delay:1.5 });

        tl.to(".tree-1", { duration: 1, opacity: 1, ease:Power0.easeNone, delay:3 });                                                                              

    }

    var isinitcard = false;

    jQuery(window).on('initcard', function() {
        if(isinitcard == false) {
            isinitcard = true;

            if(window.initCard) {
                initCard();
            } 
        }        		       
    });

</script>