<style>
    .xmas6-main-ele{width:12.000em;height:auto;display:block;position:absolute;top:2.688em;left:19.000em;position:absolute;}
    .xmas6-ele1{width:8.063em;height:auto;display:block;position:absolute;top:0em;left:1.500em;position:absolute;}
    .xmas6-ele2{width:6.750em;height:auto;display:block;position:absolute;top:0em;left:11.063em;position:absolute;}
    .xmas6-ele3{width:7.313em;height:auto;display:block;position:absolute;top:0em;left:31.000em;position:absolute;}
    .xmas6-ele4{width:5.500em;height:auto;display:block;position:absolute;top:0em;left:36.688em;position:absolute;}
    .xmas6-ele5{width:6.688em;height:auto;display:block;position:absolute;top:0em;left:41.125em;position:absolute;}
    .xmas6-txt{width:36.813em;height:auto;display:block;position:absolute;bottom:2.813em;left:6.625em;position:absolute;}
</style>

<!--Start[for desktop]-->
<div class="sep-card-container">
    <div class="sep-card-desktop">
        <div class="sep-card-desktop-container">                                                                
            <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/card.jpg" alt="Card" class="card-img">
            <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/xmas6-ele-txt.png" alt="" class="xmas6-txt"> 
            <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/xmas6-ele-main.png" alt="" class="xmas6-main-ele">
            <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/xmas6-ele1.png" alt="" class="xmas6-ele1">
            <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/xmas6-ele2.png" alt="" class="xmas6-ele2">
            <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/xmas6-ele3.png" alt="" class="xmas6-ele3">
            <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/xmas6-ele4.png" alt="" class="xmas6-ele4">
            <img src="{{ url('public') }}/cards/{{ $ecard->folder_name }}/xmas6-ele5.png" alt="" class="xmas6-ele5">                       
        </div>
    </div>
</div>
<!--End [for desktop]--> 

   
<script nonce="{{ csp_nonce() }}" src="{{ url('public') }}/js/gsap.min.js"></script> 

<script nonce="{{ csp_nonce() }}">
	
	function initCard() {
		
        var tl = new gsap.timeline({repeat:-1});
            
        tl.to(".xmas6-main-ele, .xmas6-ele1, .xmas6-ele2, .xmas6-ele3, .xmas6-ele4, .xmas6-ele5, .xmas6-txt", { duration: 1, opacity: 1, ease:Expo.easeInOut });
        tl.fromTo(".xmas6-main-ele", { marginTop:-600 }, { duration: 1.5, marginTop: 0, ease:Bounce.easeInOut });
        tl.fromTo(".xmas6-ele1", { marginTop:-600 }, { duration: 1.5, marginTop: 0, ease:Bounce.easeInOut }, "-=1.3");
        tl.fromTo(".xmas6-ele2", { marginTop:-600 }, { duration: 1.5, marginTop: 0, ease:Bounce.easeInOut }, "-=1.2");
        tl.fromTo(".xmas6-ele3", { marginTop:-600 }, { duration: 1.5, marginTop: 0, ease:Bounce.easeInOut }, "-=1.1");
        tl.fromTo(".xmas6-ele4", { marginTop:-600 }, { duration: 1.5, marginTop: 0, ease:Bounce.easeInOut }, "-=1.0");
        tl.fromTo(".xmas6-ele5", { marginTop:-600 }, { duration: 1.5, marginTop: 0, ease:Bounce.easeInOut }, "-=0.9");
        tl.fromTo(".xmas6-txt", { marginBottom:-200 }, { duration: 1.5, marginBottom: 0, ease:Bounce.easeInOut }, "-=0.8");	
        tl.to(".xmas6-main-ele, .xmas6-ele1, .xmas6-ele2, .xmas6-ele3, .xmas6-ele4, .xmas6-ele5, .xmas6-txt", { duration: 1, opacity: 0, delay: 2, ease:Expo.easeInOut });		 		 			     

    }

    var isinitcard = false;

    jQuery(window).on('initcard', function() {
        if(isinitcard == false) {
            isinitcard = true;

            if(window.initCard) {
                initCard();
            } 
        }        		       
    });

</script>