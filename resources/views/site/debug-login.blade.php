@extends('site.layouts.layout')

@section('content')



    <!--Start [ layout ]-->
    <div class="layout">

        <!--Start [ login ]-->
        <div class="cc-section fs-page login-page">
            <div class="fs-page-content">
                <div class="fs-page-cell">
                    <?php /* <a href="{{ route('site_index') }}" class="langauge-link" aria-label="lang-de"><img src="{{ url('public') }}/gfx/flag-german.svg" alt="flag-german"></a> */ ?>
                    <div class="login-content">
                        <div class="login-space">

                            <div class="login-tab-pane-content">
                                <form name="debug-saml-account-form" class="debug-saml-account-form form-a" action="{{ route('saml_debug_logged_in_submit') }}" method="post" autocomplete="off">
                                    <div class="row">
                                        <div class="col-12">
                                            <h2 class="h4">SAML Debug-Login)</h2>
                                        </div>
                                    </div>

                                    <input type="hidden" name="debug_token" value="{{ getDebugToken() }}">
                                    @csrf


                                    <div class="row">
                                        <div class="col-12">

                                            <div class="form-group">
                                                <label class="form-lbl please-enter-email">First Name: *</label>
                                                <div class="form-control-wrapper">
                                                    <input type="text" name="first_name" class="form-control" value="{{ session()->get('saml_first_name') }}" autocomplete="off">
                                                </div>
                                                @error('first_name')
                                                <div class="alert alert-danger">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="form-group">
                                                <label class="form-lbl please-enter-email">Last Name: *</label>
                                                <div class="form-control-wrapper">
                                                    <input type="text" name="last_name" class="form-control" value="{{ session()->get('saml_last_name') }}" autocomplete="off">
                                                </div>
                                                @error('last_name')
                                                <div class="alert alert-danger">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            @if(session()->get("saml_no_email"))
                                                <div class="form-group">
                                                    <label class="form-lbl please-enter-email">E-Mail-Adresse: *</label>
                                                    <div class="form-control-wrapper">
                                                        <input type="email" name="email" class="form-control" value="{{ session()->get('saml_email') }}" autocomplete="off">
                                                    </div>
                                                    @error('email')
                                                    <div class="alert alert-danger">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            @endif

                                            <div class="form-group">
                                                <label for="num_orgeinheit">
                                                    Organisation-Unit Number:
                                                </label>
                                                <input type="text" class="form-control" id="num_orgeinheit" name="num_orgeinheit" value="9999" placeholder="e.g. 0628">
                                                @error('num_orgeinheit')
                                                <div class="alert alert-danger">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="form-group">
                                                <label for="saml_institute">
                                                    Institute Name
                                                </label>
                                                <input type="text" class="form-control" id="saml_institute" name="saml_institute" placeholder="Bausparkasse der Österreichischen Sparkassen AG">
                                                @error('saml_institute')
                                                <div class="alert alert-danger">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            @if ($errors->any())
                                                <div class="alert alert-danger">
                                                    <ul>
                                                        @foreach ($errors->all() as $error)
                                                            <li>{{ $error }}</li>
                                                        @endforeach
                                                    </ul>
                                                </div>
                                            @endif

                                            <p class="d-none error form-error-text"></p>

                                            <div class="btn-spacer">
                                                <button type="submit" class="btn btn-primary w-100"><span class="btn-text">Test Sign-in</span></button>
                                            </div>



                                        </div>
                                    </div>
                                    <input type="hidden" name="token">
                                </form>
                            </div>

                        </div>
                    </div>
                </div>

                @include("site.elements.side_bar")

            </div>

            @include("site.elements.small_footer")

        </div>
        <!--End   [ login ]-->

    </div>
    <!--End   [ layout ]-->

@endsection
