@extends('admin.layouts.layout')
@section('content')

    <?php  $admin = Request::session()->get('backend_user');  ?>

    <!--begin::Content wrapper-->
    <div class="d-flex flex-column flex-column-fluid">
        <!--begin::Toolbar-->
        <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
            <!--begin::Toolbar container-->
            <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex flex-stack">
                <!--begin::Page title-->
                <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                    <!--begin::Title-->
                    <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Welcome {{ $admin->username }}!</h1>
                    <!--end::Title-->
                </div>
                <!--end::Page title-->
                
            </div>
            <!--end::Toolbar container-->
        </div>
        <!--end::Toolbar-->

        <!--begin::Content-->
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <!--begin::Content container-->
            <div id="kt_app_content_container" class="app-container container-fluid">
                
                <div class="card card-bordered mb-6">
                    <div class="card-header min-h-40px">
                        <h3 class="card-title">Select Criteria for Export</h3>
                    </div>
                    <div class="card-body">
                        <form id="kt_game_form" class="form" action="{{ route("admin_export_game") }}" method="POST">
                            
                            <div class="row" >
                                <div class="fv-row col-lg-12">
                                    <div class="">
                                        <div class="rounded border p-10">
                                            <div class="mb-10 d-flex">
                                                
                                                <div class="form-check form-check-custom form-check-solid me-10">
                                                    <input class="form-check-input" type="checkbox" value="has_metacritic_value" id="has_metacritic_value" name="export_value[]">
                                                    <label class="form-check-label" for="has_metacritic_value">
                                                        Has Metacritic Value
                                                    </label>
                                                </div>

                                                <div class="form-check form-check-custom form-check-solid me-10">
                                                    <input class="form-check-input" type="checkbox" value="missing_metacritic_value" id="missing_metacritic_value" name="export_value[]" >
                                                    <label class="form-check-label" for="missing_metacritic_value">
                                                        Missing Metacritic Value
                                                    </label>
                                                </div>
                                            
                                                <div class="form-check form-check-custom form-check-solid me-10">
                                                    <input class="form-check-input" type="checkbox" value="has_np_communication_id" id="has_np_communication_id" name="export_value[]">
                                                    <label class="form-check-label" for="kt_check_indeterminate_1">
                                                        Has NP Communication ID
                                                    </label>
                                                </div>
                                            
                                                <div class="form-check form-check-custom form-check-solid me-10">
                                                    <input class="form-check-input" type="checkbox" value="is_discounted" id="is_discounted" name="export_value[]">
                                                    <label class="form-check-label" for="is_discounted">
                                                        is discounted
                                                    </label>
                                                </div>
                                            
                                                <div class="form-check form-check-custom form-check-solid me-10">
                                                    <input class="form-check-input" type="checkbox" value="is_ps" id="is_ps" name="export_value[]">
                                                    <label class="form-check-label" for="is_ps">
                                                        is PS+
                                                    </label>
                                                </div>
                                                
                                            </div>
                                
                                            <div class="d-flex justify-content-end">
                                                <!--begin::Button-->
                                                <button type="submit" id="kt_admin_submit" class="btn btn-primary">
                                                    <span class="indicator-label">Export</span>
                                                    <span class="indicator-progress">Please wait...
                                                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                                </button>
                                                <!--end::Button-->
                                            </div>
                                            <!--end::Action buttons-->

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                        </form>
                    </div>
                </div>
                
            </div>
            <!--end::Content container-->
        </div>
        <!--end::Content-->

    </div>
    <!--end::Content wrapper-->
    
@endsection
