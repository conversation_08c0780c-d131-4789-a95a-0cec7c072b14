@php
$id = $id ?? $name;
$id = str_replace("[", "_", $id);
$id = str_replace("]", "", $id);

$allowedExts = isset($onlyExt) ? [".".$onlyExt] : [".xlsx"];
@endphp

<div class="fv-row mb-7 {{ $additionalClass ?? "" }}">
    @if(isset($label) && $label)

        @if(isset($text_form) && $text_form)
                
            <div class="row">
                <div class="col-md-6">
                    <label for="{{ $id ?? $name }}">{!! $label !!}</label>
                </div>
                <div class="col-md-6 text-end">
                    <a href="javascript:;" class="btn btn-sm btn-danger pt-1 pb-1 delete-app-text" data-key="{{ $name }}">Delete</a>
                </div>
            </div>

        @else
            <label for="{{ $id ?? $name }}" class="fs-6 fw-semibold form-label mt-3" >{!! $label !!}</label>
        @endif
        
    @endif

    <input name="{{ $name }}" id="{{ $id ?? $name }}" type="hidden"
           @if(isset($required) && $required) data-validation="required" @endif
           @isset($value) value="{{ $value }}" @endisset >
    
    <div class="text-left drop-input-container">
    	<input name="{{ $id ?? $name }}_upload" class="drag-upload-input func-drag-xls-upload" type="file" 
            accept="{{ implode(",", $allowedExts) }}" data-field="#{{ $id ?? $name }}"
        	data-form="#xls_upload_form" data-orig-name-id="{{ isset($origname_id) ? $origname_id : '' }}" />
    	
        <span class="form-control-label">
        	<span class="file-upload-icon">
                <img height="75" src="{{ url('public') }}/assets/media/icons/duotune/files/fil022.svg"/>
            </span>
            <span class="upload-file-text">Drag and Drop here or Browse File ({{ implode(", ", $allowedExts) }})</span> 
        </span>
        
        <p class="file-upload-error text-danger mt-2">
        	@if ($errors->has($name))
            	{{ $errors->first($name) }}
            @endif
        </p>
    </div>
    
    @if(isset($input_comment))
    	<p class="mt-2"><small>{{ $input_comment }}</small></p>
    @endif
    
    <div class="img-upload-wrapper mt-3 {{ $value ? 'd-block' : '' }}">
        <a href="{{ $value != "" ? component_uploads_url($value) : '' }}" target="_blank">{{ $value }}</a>
    </div>
    
    <div class="progress progress-md upload-progress mt-2 d-none">
        <div class="progress-bar bg-success progress-bar-striped progress-bar-animated" role="progressbar">
            <span class="sr-only">0% Complete</span>
        </div>
    </div>
</div>