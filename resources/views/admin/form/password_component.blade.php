<div class="form-group {{ $additionalClass ?? "" }}">
    @if(isset($label) && $label)
        <label for="{{ $id ?? $name }}">
            {{ $label }}
        </label>
    @endif
    <input type="password" class="form-control" name="{{ $name }}" id="{{ $id ?? $name }}"
           placeholder="{{ $placeholder ?? "" }}"
           @isset($value) value="{{ $value }}" @endisset @if(isset($readonly) && $readonly) readonly="readonly" @endif>
    @isset($info)
        <small id="{{ $id ?? $name . "_info" }}" class="form-text text-muted">
            {{ $info }}
        </small>
    @endisset
    @if ($errors->has($name))
        <span class="error help-block invalid-feedback show_error">
            @foreach($errors->get($name) as $error)
                {{ $error }} <br>
            @endforeach
        </span>
    @endif
</div>
