<?php
require_once "PsnApi.php";

class PsnLoginController{
	const UNIVERSAL_DEV_LOGIN_GATE_URL  = "https://devhost03.contentcreators.at/scedhost/psn/login";
	const UNIVERSAL_LIVE_LOGIN_GATE_URL = "https://ps.playstation.com/psn/login";
	const LIVE_LOGIN_GATE_URL           = "https://psvrtrophy.software.eu.playstation.com/psn/login";
	const STAGE_LOGIN_GATE_URL          = "https://psvrtrophystage.software.eu.playstation.com/psn/login";
	const DEVHOST_LOGIN_GATE_URL        = "https://devhost03.contentcreators.at/scedhost/psn/login";

	static function getLoginGateUrl(){
		if(stripos(@$_SERVER["SERVER_NAME"], "devhost03.contentcreators.at") !== false) $defaultLoginUrl = static::DEVHOST_LOGIN_GATE_URL;
		else if(stripos(@$_SERVER["SERVER_NAME"], "psvrtrophystage.software.eu.playstation.com") !== false) $defaultLoginUrl = static::STAGE_LOGIN_GATE_URL;
		else if(stripos(@$_SERVER["SERVER_NAME"], "psvrtrophy.software.eu.playstation.com") !== false) $defaultLoginUrl = static::LIVE_LOGIN_GATE_URL;
		else if(stripos(@$_SERVER["SERVER_NAME"], "localhost") !== false) $defaultLoginUrl = static::UNIVERSAL_DEV_LOGIN_GATE_URL;
		else $defaultLoginUrl = static::UNIVERSAL_LIVE_LOGIN_GATE_URL;
		return $defaultLoginUrl;
	}

	static function login(){
		// Write service session
		$serviceSessionData             = getServiceSession();
		$serviceSessionData["back_url"] = getBaseUrl() . "auth";
		setServiceSession($serviceSessionData);

		// Redirect user to psn login gate
		$defaultLoginUrl = static::getLoginGateUrl();
		redirect($defaultLoginUrl);
	}

	static function loginNewMethod(){
		// Redirect user to psn login gate
		$defaultLoginUrl = static::getLoginGateUrl();
		$requestToken    = ["back_url" => getBaseUrl() . "auth"];
		redirect($defaultLoginUrl . "?" . http_build_query(["t" => PsnEncryption::encryptText(json_encode($requestToken))]));
	}

	static function getAccessTokenFromGatewayCode($code = ""){
		$retrieveTokenUrl = str_replace("/login", "/retrieve_token", static::getLoginGateUrl());
		$dataJson         = file_get_contents($retrieveTokenUrl . "?" . http_build_query(["access_code" => PsnEncryption::decryptText($code)]));
		$data             = json_decode($dataJson, true);
		return json_decode($data["data"]["token"] ?? "", true);
	}

	static function auth(){
		// Read service session
		$serviceSessionData = getServiceSession();

		// Sanity test
		if(!isset($serviceSessionData["token"]) && !isset($_GET["c"])){
			die("No user token or access code");
		}

		// Try to login user
		$accessToken = $serviceSessionData["token"] ?? "";
		if(isset($_GET["c"]) && $_GET["c"]){
			$accessToken = static::getAccessTokenFromGatewayCode($_GET["c"]);
			$userData    = PsnApi::handleTokenAuth($accessToken);
		}
		else{
			$userData = PsnApi::handleTokenAuth($accessToken);
		}

		// Fetch user profile
		$userData["userProfile"] = json_decode(PsnApi::executeUrl("GET", "https://us-prof.np.community.playstation.net/userProfile/v1/users/me/profile2?fields=npId,onlineId,avatarUrls,plus,aboutMe,languagesUsed,trophySummary(@default,progress,earnedTrophies),isOfficiallyVerified,personalDetailSharing,personalDetailSharingRequestMessageFlag,primaryOnlineStatus,presences(@titleInfo,hasBroadcastData),friendRelation,requestMessageFlag,blocking,mutualFriendsCount,following,followerCount,friendsCount,followingUsersCount&avatarSizes=m,xl&profilePictureSizes=m,xl&languagesUsedLanguageSet=set3&psVitaTitleIcon=circled&titleIconSize=s", $accessToken["accessToken"]), true);

		$userData["trophies"] = [];
		try{
			$userData["trophies"]["base_url"] = PsnApi::getTrophyBaseUrl($accessToken["accessToken"]);
			//$userData["trophies"]["summary"]  = PsnApi::getTrophiesSummary($accessToken["accessToken"], $userData["onlineId"]);
			//$userData["trophies"]["test"]     = PsnApi::getTrophiesTest($accessToken["accessToken"], $userData["onlineId"]);
			//$userData["trophies"]["title"]    = PsnApi::getTrophiesForTitle($accessToken["accessToken"], "NPWR05506_00");
			//$userData["trophies"]["list"]     = PsnApi::getTrophyTitles($accessToken["accessToken"], $userData["onlineId"]);
		}
		catch(Exception|Throwable $exception){
			$userData["trophies"]["errors"] = (string)$exception;
			@logLine($exception->getMessage());
		}

		// Handle error
		if(!$userData){
			die("Invalid user token");
		}

		// Debug
		if(M(Config::get("app"), "debug"))
			logLine(sprintf("Logged in user token: %s", @json_encode($userData)), "debug");

		// Out logged in page
		outEmbedModule("logged_in", ["data" => $userData]);
	}
}