<?php

namespace Container1MxyNFN;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/*
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getRedirectionService extends SimpleSAML_KernelProdContainer
{
    /*
     * Gets the public 'SimpleSAML\Module\core\Controller\Redirection' shared autowired service.
     *
     * @return \SimpleSAML\Module\core\Controller\Redirection
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->services['SimpleSAML\\Module\\core\\Controller\\Redirection'] = new \SimpleSAML\Module\core\Controller\Redirection(($container->privates['SimpleSAML\\Configuration'] ?? $container->load('getConfigurationService')), ($container->privates['SimpleSAML\\Session'] ?? $container->load('getSessionService')));
    }
}
