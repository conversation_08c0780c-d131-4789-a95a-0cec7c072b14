<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerEulLbCN\SimpleSAML_KernelProdContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerEulLbCN/SimpleSAML_KernelProdContainer.php') {
    touch(__DIR__.'/ContainerEulLbCN.legacy');

    return;
}

if (!\class_exists(SimpleSAML_KernelProdContainer::class, false)) {
    \class_alias(\ContainerEulLbCN\SimpleSAML_KernelProdContainer::class, SimpleSAML_KernelProdContainer::class, false);
}

return new \ContainerEulLbCN\SimpleSAML_KernelProdContainer([
    'container.build_hash' => 'EulLbCN',
    'container.build_id' => 'e77b26a0',
    'container.build_time' => 1692732462,
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerEulLbCN');
