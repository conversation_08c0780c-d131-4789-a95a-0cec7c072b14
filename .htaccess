# Disable server signature
ServerSignature Off

# No index view
options -Indexes
options -MultiViews

# Disable gzip compression
SetEnv no-gzip 1

# File types
AddType application/manifest+json webmanifest

# Disable unused http methods
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{REQUEST_METHOD} !^(GET|POST)
    RewriteRule .* - [R=405,L]
</IfModule>

# Redirect to ssl
RewriteCond %{HTTPS} !=on
RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# @Change or add basepath lines here
    # --dynamic_line_dont_remove_1--
SetEnvIf HOST "localhost" BASE_PATH=/
SetEnvIf HOST "ps.playstation.com" BASE_PATH=/days-of-play-2025
# --dynamic_line_dont_remove_2--

# Jpg/Png to avif
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{HTTP_USER_AGENT} Chrome [OR]
    RewriteCond %{HTTP_USER_AGENT} "Google Page Speed Insights" [OR]
    RewriteCond %{HTTP_ACCEPT} image/avif
    RewriteCond %{DOCUMENT_ROOT}%{ENV:BASE_PATH}/$1.avif -f
    RewriteRule (.+)\.(jpg|png)$ $1.avif [T=image/avif,E=REQUEST_image]
</IfModule>

# avif to png
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{HTTP_ACCEPT} !image/avif
    RewriteCond %{DOCUMENT_ROOT}%{ENV:BASE_PATH}/$1.png -f
    RewriteRule (.+)\.(avif)$ $1.png [T=image/png,E=REQUEST_image]
</IfModule>

# Disable caching for ALL files
<IfModule mod_headers.c>
    Header unset ETag
    Header unset Last-Modified
    Header unset Cache-Control
    Header set Cache-Control "max-age=0, no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires "Wed, 11 Jan 1984 05:00:00 GMT"
</IfModule>

# Block access to certain files
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>
<Files ~ "\.(gitlab-ci.yml|env|env.live|env.stage|env.dev|env.local|env.local_2|config.js|md|gitignore|gitattributes|svnignore|editorconfig|styleci.yml|lock|xml|htaccess|bat|sh|git|svn|README)$">
    Order allow,deny
    Deny from all
</Files>
<Files ~ "(artisan|composer.phar|webpack.mix.js|composer.json|package.json|package-lock.json|firebase-auth-adminsdk.json|firebase-access-token.json)$">
    Order allow,deny
    Deny from all
</Files>

# Deny direct access to specific folders
<IfModule mod_rewrite.c>
	<IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>
    RewriteEngine On
    RewriteRule ^(app/|.svn/|bootstrap/|config/|database/|node_modules/|routes/|storage/|tests/|vendor/|resources/views/|resources/lang/|bin/) - [F,L,NC]
</IfModule>

<ifModule mod_alias.c>
    RedirectMatch 404 ^/app/.*$
    RedirectMatch 404 ^/.svn/.*$
    RedirectMatch 404 ^/bootstrap/.*$
    RedirectMatch 404 ^/config/.*$
    RedirectMatch 404 ^/database/.*$
    RedirectMatch 404 ^/node_modules/.*$
    RedirectMatch 404 ^/routes/.*$
    RedirectMatch 404 ^/storage/.*$
    RedirectMatch 404 ^/tests/.*$
    RedirectMatch 404 ^/vendor/.*$
    RedirectMatch 404 ^/resources/views/.*$
    RedirectMatch 404 ^/resources/lang/.*$
    RedirectMatch 404 ^/bin/.*$
</ifModule>

RedirectMatch 404 /\\.svn(/.*|$)
<FilesMatch "^\.">
	Order allow,deny
	Deny from all
</FilesMatch>

# Handle security headers
<IfModule mod_headers.c>
    # Reset
    <FilesMatch "(?<!ttf|woff|woff2|svg|eot|jpg|jpeg|png|gif|pdf|css|js|ico|webp|webmanifest)$">
        <If "%{HTTP_HOST} != 'localhost'">
        Header unset X-Content-Security-Policy
        Header unset Content-Security-Policy
        </If>
    </FilesMatch>

    Header unset X-Frame-Options
    Header unset X-Powered-By
    Header always unset X-Powered-By
    Header unset Server
    Header always unset Server

    Header unset Vary
    Header unset Vary
    Header always set Vary "Origin, User-Agent"

    # Feature-Policy
	# Header always set Feature-Policy: "camera 'none'; microphone 'none'"

	# Permissions-Policy
	Header always set Permissions-Policy: "camera=(), microphone=()"

    # Referrer-Policy
    Header always set Referrer-Policy: same-origin

    # Expect-CT
    # Header always set Expect-CT: max-age=86400

    # X-Content-Type
    Header always set X-Content-Type-Options nosniff

    # X-XSS-Protection
	Header always set X-XSS-Protection: "1; mode=block"

    # X-Frame-Options -> No iframes used in this app
    Header always set X-Frame-Options "SAMEORIGIN"

    # Strict-Transport-Security
    Header always set Strict-Transport-Security: "max-age=157680000; includeSubDomains; preload"

    # X-Permitted-Cross-Domain-Policies
    Header always set X-Permitted-Cross-Domain-Policies: none

    # Cross-Origin-Opener-Policy
    Header always set Cross-Origin-Opener-Policy: same-origin-allow-popups
</IfModule>

<FilesMatch ".*">
    <IfModule mod_headers.c>
        Header always set Access-Control-Allow-Origin "null"
        Header always set Access-Control-Allow-Methods "GET, POST"
        Header always set Access-Control-Allow-Headers "Content-Type"
        Header always set Access-Control-Max-Age "0"
        Header always set X-Content-Type-Options "nosniff"
        Header always set X-Frame-Options "DENY"
    </IfModule>
</FilesMatch>

# Add csp for files inside public webmanifest file
<FilesMatch "\.(jpg|jpeg|png|gif|ico|webp|avif|js|css|mp3|webmanifest)$">
    <IfModule mod_headers.c>
        Header always set Content-Security-Policy "default-src 'self';"
    </IfModule>
</FilesMatch>

<IfModule mod_rewrite.c>
	# Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Handle Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

