<?php

// NumObjs, NumInSet, Result

return [
    [
        84,
        7,
        3,
    ],
    [
        36,
        8,
        2,
    ],
    [
        120,
        8,
        3,
    ],
    [
        330,
        8,
        4,
    ],
    [
        171700,
        100,
        3,
    ],
    [
        '#NUM!',
        -7,
        -10,
    ],
    [
        '#NUM!',
        -7,
        10,
    ],
    [
        '#NUM!',
        7,
        -10,
    ],
    [
        4,
        2,
        3,
    ],
    [
        3,
        2,
        2,
    ],
    [
        2,
        2,
        1,
    ],
    [
        1,
        2,
        0,
    ],
    [
        3,
        2.5,
        2,
    ],
    [
        '#VALUE!',
        'ABCD',
        2,
    ],
    [
        '#VALUE!',
        3,
        'EFGH',
    ],
    [
        2002,
        10,
        5,
    ],
    [
        220,
        10,
        3,
    ],
    [
        53130,
        21,
        5,
    ],
    [
        6,
        6,
        1,
    ],
    [
        21,
        6,
        2,
    ],
    [
        56,
        6,
        3,
    ],
    [
        126,
        6,
        4,
    ],
    [
        252,
        6,
        5,
    ],
    [
        462,
        6,
        6,
    ],
    [
        792,
        6,
        7,
    ],
    [1, 0, 0],
    ['#NUM!', 0, 1],
    ['#VALUE!', 1, true],
    ['#VALUE!', 1, null],
];
