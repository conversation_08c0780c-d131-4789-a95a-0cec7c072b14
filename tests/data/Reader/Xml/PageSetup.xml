<?xml version="1.0"?>
<?mso-application progid="Excel.Sheet"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
  <Author><PERSON></Author>
  <LastAuthor><PERSON></LastAuthor>
  <LastPrinted>2020-07-04T11:51:41Z</LastPrinted>
  <Created>2020-06-29T17:37:00Z</Created>
  <LastSaved>2020-07-04T11:52:32Z</LastSaved>
  <Version>16.00</Version>
 </DocumentProperties>
 <OfficeDocumentSettings xmlns="urn:schemas-microsoft-com:office:office">
  <AllowPNG/>
 </OfficeDocumentSettings>
 <ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">
  <WindowHeight>13170</WindowHeight>
  <WindowWidth>21600</WindowWidth>
  <WindowTopX>2145</WindowTopX>
  <WindowTopY>2145</WindowTopY>
  <ProtectStructure>False</ProtectStructure>
  <ProtectWindows>False</ProtectWindows>
 </ExcelWorkbook>
 <Styles>
  <Style ss:ID="Default" ss:Name="Normal">
   <Alignment ss:Vertical="Bottom"/>
   <Borders/>
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Color="#000000"/>
   <Interior/>
   <NumberFormat/>
   <Protection/>
  </Style>
 </Styles>
 <Worksheet ss:Name="Sheet1">
  <Table ss:ExpandedColumnCount="3" ss:ExpandedRowCount="5" x:FullColumns="1"
   x:FullRows="1" ss:DefaultColumnWidth="51" ss:DefaultRowHeight="14.25">
   <Row ss:AutoFitHeight="0">
    <Cell><Data ss:Type="Number">1</Data></Cell>
    <Cell><Data ss:Type="Number">2</Data></Cell>
    <Cell><Data ss:Type="Number">3</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0">
    <Cell><Data ss:Type="Number">4</Data></Cell>
    <Cell><Data ss:Type="Number">5</Data></Cell>
    <Cell><Data ss:Type="Number">6</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0">
    <Cell><Data ss:Type="Number">7</Data></Cell>
    <Cell><Data ss:Type="Number">8</Data></Cell>
    <Cell><Data ss:Type="Number">9</Data></Cell>
   </Row>
   <Row ss:Index="5" ss:AutoFitHeight="0">
    <Cell ss:Formula="=SUM(R[-3]C:R[-3]C[2],R[-4]C[1]:R[-2]C[1])"><Data
      ss:Type="Number">30</Data></Cell>
    <Cell ss:Formula="=COUNT(R[-3]C[-1]:R[-3]C[1],R[-4]C:R[-2]C)"><Data
      ss:Type="Number">6</Data></Cell>
   </Row>
  </Table>
  <WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">
   <PageSetup>
    <Layout x:CenterHorizontal="1"/>
    <Header x:Margin="0.31496062992125984"/>
    <Footer x:Margin="0.31496062992125984"/>
    <PageMargins x:Bottom="0.74803149606299213" x:Left="0.51181102362204722"
     x:Right="0.51181102362204722" x:Top="0.94488188976377963"/>
   </PageSetup>
   <Unsynced/>
   <Print>
    <ValidPrinterInfo/>
    <PaperSizeIndex>9</PaperSizeIndex>
    <Scale>75</Scale>
    <VerticalResolution>0</VerticalResolution>
   </Print>
   <Selected/>
   <Panes>
    <Pane>
     <Number>3</Number>
     <RangeSelection>R1C1:R5C3</RangeSelection>
    </Pane>
   </Panes>
   <ProtectObjects>False</ProtectObjects>
   <ProtectScenarios>False</ProtectScenarios>
  </WorksheetOptions>
 </Worksheet>
 <Worksheet ss:Name="Sheet2">
  <Table ss:ExpandedColumnCount="3" ss:ExpandedRowCount="5" x:FullColumns="1"
   x:FullRows="1" ss:DefaultColumnWidth="51" ss:DefaultRowHeight="14.25">
   <Row ss:AutoFitHeight="0">
    <Cell><Data ss:Type="Number">1</Data></Cell>
    <Cell><Data ss:Type="Number">2</Data></Cell>
    <Cell><Data ss:Type="Number">3</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0">
    <Cell><Data ss:Type="Number">4</Data></Cell>
    <Cell><Data ss:Type="Number">5</Data></Cell>
    <Cell><Data ss:Type="Number">6</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0">
    <Cell><Data ss:Type="Number">7</Data></Cell>
    <Cell><Data ss:Type="Number">8</Data></Cell>
    <Cell><Data ss:Type="Number">9</Data></Cell>
   </Row>
   <Row ss:Index="5" ss:AutoFitHeight="0">
    <Cell ss:Formula="=SUM(R1C1:R1C3,R3C1:R3C3,R1C1:R3C1,R1C3:R3C3)"><Data
      ss:Type="Number">60</Data></Cell>
    <Cell ss:Formula="=COUNT(R1C1:R1C3,R3C1:R3C3,R1C1:R3C1,R1C3:R3C3)"><Data
      ss:Type="Number">12</Data></Cell>
   </Row>
  </Table>
  <WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">
   <PageSetup>
    <Layout x:Orientation="Landscape" x:CenterVertical="1"/>
    <Header x:Margin="0.31496062992125984"/>
    <Footer x:Margin="0.31496062992125984"/>
    <PageMargins x:Bottom="0.74803149606299213" x:Left="0.70866141732283472"
     x:Right="0.70866141732283472" x:Top="0.74803149606299213"/>
   </PageSetup>
   <Unsynced/>
   <Print>
    <LeftToRight/>
    <ValidPrinterInfo/>
    <PaperSizeIndex>9</PaperSizeIndex>
    <VerticalResolution>0</VerticalResolution>
   </Print>
   <Panes>
    <Pane>
     <Number>3</Number>
     <RangeSelection>R1C1:R5C3</RangeSelection>
    </Pane>
   </Panes>
   <ProtectObjects>False</ProtectObjects>
   <ProtectScenarios>False</ProtectScenarios>
  </WorksheetOptions>
 </Worksheet>
 <Worksheet ss:Name="Sheet3">
  <Names>
   <NamedRange ss:Name="Print_Area" ss:RefersTo="=Sheet3!R1C1:R5C3"/>
  </Names>
  <Table ss:ExpandedColumnCount="3" ss:ExpandedRowCount="5" x:FullColumns="1"
   x:FullRows="1" ss:DefaultColumnWidth="51" ss:DefaultRowHeight="14.25">
   <Row ss:AutoFitHeight="0">
    <Cell><Data ss:Type="Number">1</Data><NamedCell ss:Name="Print_Area"/></Cell>
    <Cell><Data ss:Type="Number">2</Data><NamedCell ss:Name="Print_Area"/></Cell>
    <Cell><Data ss:Type="Number">3</Data><NamedCell ss:Name="Print_Area"/></Cell>
   </Row>
   <Row ss:AutoFitHeight="0">
    <Cell><Data ss:Type="Number">4</Data><NamedCell ss:Name="Print_Area"/></Cell>
    <Cell><Data ss:Type="Number">5</Data><NamedCell ss:Name="Print_Area"/></Cell>
    <Cell><Data ss:Type="Number">6</Data><NamedCell ss:Name="Print_Area"/></Cell>
   </Row>
   <Row ss:AutoFitHeight="0">
    <Cell><Data ss:Type="Number">7</Data><NamedCell ss:Name="Print_Area"/></Cell>
    <Cell><Data ss:Type="Number">8</Data><NamedCell ss:Name="Print_Area"/></Cell>
    <Cell><Data ss:Type="Number">9</Data><NamedCell ss:Name="Print_Area"/></Cell>
   </Row>
   <Row ss:Index="5" ss:AutoFitHeight="0">
    <Cell ss:Formula="=SUM(R1C1:R1C3,R3C1:R3C3,R1C1:R3C1,R1C3:R3C3)"><Data
      ss:Type="Number">60</Data><NamedCell ss:Name="Print_Area"/></Cell>
    <Cell ss:Formula="=COUNT(R1C1:R1C3,R3C1:R3C3,R1C1:R3C1,R1C3:R3C3)"><Data
      ss:Type="Number">12</Data><NamedCell ss:Name="Print_Area"/></Cell>
   </Row>
  </Table>
  <WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">
   <PageSetup>
    <Layout x:CenterHorizontal="1" x:CenterVertical="1"/>
    <Header x:Margin="0.51181102362204722"/>
    <Footer x:Margin="0.51181102362204722"/>
    <PageMargins x:Bottom="0.94488188976377963" x:Left="0.70866141732283472"
     x:Right="0.70866141732283472" x:Top="0.94488188976377963"/>
   </PageSetup>
   <Unsynced/>
   <Print>
    <ValidPrinterInfo/>
    <PaperSizeIndex>9</PaperSizeIndex>
    <Scale>90</Scale>
    <VerticalResolution>0</VerticalResolution>
   </Print>
   <FreezePanes/>
   <FrozenNoSplit/>
   <SplitHorizontal>1</SplitHorizontal>
   <TopRowBottomPane>1</TopRowBottomPane>
   <SplitVertical>1</SplitVertical>
   <LeftColumnRightPane>1</LeftColumnRightPane>
   <ActivePane>0</ActivePane>
   <Panes>
    <Pane>
     <Number>3</Number>
    </Pane>
    <Pane>
     <Number>1</Number>
    </Pane>
    <Pane>
     <Number>2</Number>
    </Pane>
    <Pane>
     <Number>0</Number>
     <ActiveRow>0</ActiveRow>
     <ActiveCol>0</ActiveCol>
     <RangeSelection>R1C1:R5C3</RangeSelection>
    </Pane>
   </Panes>
   <ProtectObjects>False</ProtectObjects>
   <ProtectScenarios>False</ProtectScenarios>
  </WorksheetOptions>
 </Worksheet>
 <Worksheet ss:Name="Sheet4">
  <Table ss:ExpandedColumnCount="3" ss:ExpandedRowCount="5" x:FullColumns="1"
   x:FullRows="1" ss:DefaultColumnWidth="51" ss:DefaultRowHeight="14.25">
   <Row>
    <Cell><Data ss:Type="Number">1</Data></Cell>
    <Cell><Data ss:Type="Number">2</Data></Cell>
    <Cell><Data ss:Type="Number">3</Data></Cell>
   </Row>
   <Row>
    <Cell><Data ss:Type="Number">4</Data></Cell>
    <Cell><Data ss:Type="Number">5</Data></Cell>
    <Cell><Data ss:Type="Number">6</Data></Cell>
   </Row>
   <Row>
    <Cell><Data ss:Type="Number">7</Data></Cell>
    <Cell><Data ss:Type="Number">8</Data></Cell>
    <Cell><Data ss:Type="Number">9</Data></Cell>
   </Row>
   <Row ss:Index="5">
    <Cell ss:Formula="=SUM(R1C1:R1C3,R3C1:R3C3,R1C1:R3C1,R1C3:R3C3)"><Data
      ss:Type="Number">60</Data></Cell>
    <Cell ss:Formula="=COUNT(R1C1:R1C3,R3C1:R3C3,R1C1:R3C1,R1C3:R3C3)"><Data
      ss:Type="Number">12</Data></Cell>
   </Row>
  </Table>
  <WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">
   <PageSetup>
    <Header x:Margin="0.3"/>
    <Footer x:Margin="0.3"/>
    <PageMargins x:Bottom="0.75" x:Left="0.7" x:Right="0.7" x:Top="0.75"/>
   </PageSetup>
   <Panes>
    <Pane>
     <Number>3</Number>
     <RangeSelection>R1C1:R5C3</RangeSelection>
    </Pane>
   </Panes>
   <ProtectObjects>False</ProtectObjects>
   <ProtectScenarios>False</ProtectScenarios>
  </WorksheetOptions>
 </Worksheet>
</Workbook>
