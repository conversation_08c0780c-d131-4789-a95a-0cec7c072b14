<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/aiplatform/v1/model_deployment_monitoring_job.proto

namespace Google\Cloud\AIPlatform\V1\ModelDeploymentMonitoringBigQueryTable;

use UnexpectedValueException;

/**
 * Indicates where does the log come from.
 *
 * Protobuf type <code>google.cloud.aiplatform.v1.ModelDeploymentMonitoringBigQueryTable.LogSource</code>
 */
class LogSource
{
    /**
     * Unspecified source.
     *
     * Generated from protobuf enum <code>LOG_SOURCE_UNSPECIFIED = 0;</code>
     */
    const LOG_SOURCE_UNSPECIFIED = 0;
    /**
     * Logs coming from Training dataset.
     *
     * Generated from protobuf enum <code>TRAINING = 1;</code>
     */
    const TRAINING = 1;
    /**
     * Logs coming from Serving traffic.
     *
     * Generated from protobuf enum <code>SERVING = 2;</code>
     */
    const SERVING = 2;

    private static $valueToName = [
        self::LOG_SOURCE_UNSPECIFIED => 'LOG_SOURCE_UNSPECIFIED',
        self::TRAINING => 'TRAINING',
        self::SERVING => 'SERVING',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}


