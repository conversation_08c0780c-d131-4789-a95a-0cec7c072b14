<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/aiplatform/v1/vertex_rag_service.proto

namespace Google\Cloud\AIPlatform\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * The fact used in grounding.
 *
 * Generated from protobuf message <code>google.cloud.aiplatform.v1.Fact</code>
 */
class Fact extends \Google\Protobuf\Internal\Message
{
    /**
     * Query that is used to retrieve this fact.
     *
     * Generated from protobuf field <code>optional string query = 1;</code>
     */
    protected $query = null;
    /**
     * If present, it refers to the title of this fact.
     *
     * Generated from protobuf field <code>optional string title = 2;</code>
     */
    protected $title = null;
    /**
     * If present, this uri links to the source of the fact.
     *
     * Generated from protobuf field <code>optional string uri = 3;</code>
     */
    protected $uri = null;
    /**
     * If present, the summary/snippet of the fact.
     *
     * Generated from protobuf field <code>optional string summary = 4;</code>
     */
    protected $summary = null;
    /**
     * If present, the distance between the query vector and this fact vector.
     *
     * Generated from protobuf field <code>optional double vector_distance = 5 [deprecated = true];</code>
     * @deprecated
     */
    protected $vector_distance = null;
    /**
     * If present, according to the underlying Vector DB and the selected metric
     * type, the score can be either the distance or the similarity between the
     * query and the fact and its range depends on the metric type.
     * For example, if the metric type is COSINE_DISTANCE, it represents the
     * distance between the query and the fact. The larger the distance, the less
     * relevant the fact is to the query. The range is [0, 2], while 0 means the
     * most relevant and 2 means the least relevant.
     *
     * Generated from protobuf field <code>optional double score = 6;</code>
     */
    protected $score = null;
    /**
     * If present, chunk properties.
     *
     * Generated from protobuf field <code>optional .google.cloud.aiplatform.v1.RagChunk chunk = 7;</code>
     */
    protected $chunk = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $query
     *           Query that is used to retrieve this fact.
     *     @type string $title
     *           If present, it refers to the title of this fact.
     *     @type string $uri
     *           If present, this uri links to the source of the fact.
     *     @type string $summary
     *           If present, the summary/snippet of the fact.
     *     @type float $vector_distance
     *           If present, the distance between the query vector and this fact vector.
     *     @type float $score
     *           If present, according to the underlying Vector DB and the selected metric
     *           type, the score can be either the distance or the similarity between the
     *           query and the fact and its range depends on the metric type.
     *           For example, if the metric type is COSINE_DISTANCE, it represents the
     *           distance between the query and the fact. The larger the distance, the less
     *           relevant the fact is to the query. The range is [0, 2], while 0 means the
     *           most relevant and 2 means the least relevant.
     *     @type \Google\Cloud\AIPlatform\V1\RagChunk $chunk
     *           If present, chunk properties.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Cloud\Aiplatform\V1\VertexRagService::initOnce();
        parent::__construct($data);
    }

    /**
     * Query that is used to retrieve this fact.
     *
     * Generated from protobuf field <code>optional string query = 1;</code>
     * @return string
     */
    public function getQuery()
    {
        return isset($this->query) ? $this->query : '';
    }

    public function hasQuery()
    {
        return isset($this->query);
    }

    public function clearQuery()
    {
        unset($this->query);
    }

    /**
     * Query that is used to retrieve this fact.
     *
     * Generated from protobuf field <code>optional string query = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setQuery($var)
    {
        GPBUtil::checkString($var, True);
        $this->query = $var;

        return $this;
    }

    /**
     * If present, it refers to the title of this fact.
     *
     * Generated from protobuf field <code>optional string title = 2;</code>
     * @return string
     */
    public function getTitle()
    {
        return isset($this->title) ? $this->title : '';
    }

    public function hasTitle()
    {
        return isset($this->title);
    }

    public function clearTitle()
    {
        unset($this->title);
    }

    /**
     * If present, it refers to the title of this fact.
     *
     * Generated from protobuf field <code>optional string title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * If present, this uri links to the source of the fact.
     *
     * Generated from protobuf field <code>optional string uri = 3;</code>
     * @return string
     */
    public function getUri()
    {
        return isset($this->uri) ? $this->uri : '';
    }

    public function hasUri()
    {
        return isset($this->uri);
    }

    public function clearUri()
    {
        unset($this->uri);
    }

    /**
     * If present, this uri links to the source of the fact.
     *
     * Generated from protobuf field <code>optional string uri = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setUri($var)
    {
        GPBUtil::checkString($var, True);
        $this->uri = $var;

        return $this;
    }

    /**
     * If present, the summary/snippet of the fact.
     *
     * Generated from protobuf field <code>optional string summary = 4;</code>
     * @return string
     */
    public function getSummary()
    {
        return isset($this->summary) ? $this->summary : '';
    }

    public function hasSummary()
    {
        return isset($this->summary);
    }

    public function clearSummary()
    {
        unset($this->summary);
    }

    /**
     * If present, the summary/snippet of the fact.
     *
     * Generated from protobuf field <code>optional string summary = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setSummary($var)
    {
        GPBUtil::checkString($var, True);
        $this->summary = $var;

        return $this;
    }

    /**
     * If present, the distance between the query vector and this fact vector.
     *
     * Generated from protobuf field <code>optional double vector_distance = 5 [deprecated = true];</code>
     * @return float
     * @deprecated
     */
    public function getVectorDistance()
    {
        @trigger_error('vector_distance is deprecated.', E_USER_DEPRECATED);
        return isset($this->vector_distance) ? $this->vector_distance : 0.0;
    }

    public function hasVectorDistance()
    {
        @trigger_error('vector_distance is deprecated.', E_USER_DEPRECATED);
        return isset($this->vector_distance);
    }

    public function clearVectorDistance()
    {
        @trigger_error('vector_distance is deprecated.', E_USER_DEPRECATED);
        unset($this->vector_distance);
    }

    /**
     * If present, the distance between the query vector and this fact vector.
     *
     * Generated from protobuf field <code>optional double vector_distance = 5 [deprecated = true];</code>
     * @param float $var
     * @return $this
     * @deprecated
     */
    public function setVectorDistance($var)
    {
        @trigger_error('vector_distance is deprecated.', E_USER_DEPRECATED);
        GPBUtil::checkDouble($var);
        $this->vector_distance = $var;

        return $this;
    }

    /**
     * If present, according to the underlying Vector DB and the selected metric
     * type, the score can be either the distance or the similarity between the
     * query and the fact and its range depends on the metric type.
     * For example, if the metric type is COSINE_DISTANCE, it represents the
     * distance between the query and the fact. The larger the distance, the less
     * relevant the fact is to the query. The range is [0, 2], while 0 means the
     * most relevant and 2 means the least relevant.
     *
     * Generated from protobuf field <code>optional double score = 6;</code>
     * @return float
     */
    public function getScore()
    {
        return isset($this->score) ? $this->score : 0.0;
    }

    public function hasScore()
    {
        return isset($this->score);
    }

    public function clearScore()
    {
        unset($this->score);
    }

    /**
     * If present, according to the underlying Vector DB and the selected metric
     * type, the score can be either the distance or the similarity between the
     * query and the fact and its range depends on the metric type.
     * For example, if the metric type is COSINE_DISTANCE, it represents the
     * distance between the query and the fact. The larger the distance, the less
     * relevant the fact is to the query. The range is [0, 2], while 0 means the
     * most relevant and 2 means the least relevant.
     *
     * Generated from protobuf field <code>optional double score = 6;</code>
     * @param float $var
     * @return $this
     */
    public function setScore($var)
    {
        GPBUtil::checkDouble($var);
        $this->score = $var;

        return $this;
    }

    /**
     * If present, chunk properties.
     *
     * Generated from protobuf field <code>optional .google.cloud.aiplatform.v1.RagChunk chunk = 7;</code>
     * @return \Google\Cloud\AIPlatform\V1\RagChunk|null
     */
    public function getChunk()
    {
        return $this->chunk;
    }

    public function hasChunk()
    {
        return isset($this->chunk);
    }

    public function clearChunk()
    {
        unset($this->chunk);
    }

    /**
     * If present, chunk properties.
     *
     * Generated from protobuf field <code>optional .google.cloud.aiplatform.v1.RagChunk chunk = 7;</code>
     * @param \Google\Cloud\AIPlatform\V1\RagChunk $var
     * @return $this
     */
    public function setChunk($var)
    {
        GPBUtil::checkMessage($var, \Google\Cloud\AIPlatform\V1\RagChunk::class);
        $this->chunk = $var;

        return $this;
    }

}

