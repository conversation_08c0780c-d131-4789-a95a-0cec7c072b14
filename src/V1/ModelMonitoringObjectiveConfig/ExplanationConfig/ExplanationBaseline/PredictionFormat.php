<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/aiplatform/v1/model_monitoring.proto

namespace Google\Cloud\AIPlatform\V1\ModelMonitoringObjectiveConfig\ExplanationConfig\ExplanationBaseline;

use UnexpectedValueException;

/**
 * The storage format of the predictions generated BatchPrediction job.
 *
 * Protobuf type <code>google.cloud.aiplatform.v1.ModelMonitoringObjectiveConfig.ExplanationConfig.ExplanationBaseline.PredictionFormat</code>
 */
class PredictionFormat
{
    /**
     * Should not be set.
     *
     * Generated from protobuf enum <code>PREDICTION_FORMAT_UNSPECIFIED = 0;</code>
     */
    const PREDICTION_FORMAT_UNSPECIFIED = 0;
    /**
     * Predictions are in JSONL files.
     *
     * Generated from protobuf enum <code>JSONL = 2;</code>
     */
    const JSONL = 2;
    /**
     * Predictions are in BigQuery.
     *
     * Generated from protobuf enum <code>BIGQUERY = 3;</code>
     */
    const BIGQUERY = 3;

    private static $valueToName = [
        self::PREDICTION_FORMAT_UNSPECIFIED => 'PREDICTION_FORMAT_UNSPECIFIED',
        self::JSONL => 'JSONL',
        self::BIGQUERY => 'BIGQUERY',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}


