<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/aiplatform/v1/vertex_rag_data.proto

namespace Google\Cloud\AIPlatform\V1\CorpusStatus;

use UnexpectedValueException;

/**
 * RagCorpus life state.
 *
 * Protobuf type <code>google.cloud.aiplatform.v1.CorpusStatus.State</code>
 */
class State
{
    /**
     * This state is not supposed to happen.
     *
     * Generated from protobuf enum <code>UNKNOWN = 0;</code>
     */
    const UNKNOWN = 0;
    /**
     * RagCorpus resource entry is initialized, but hasn't done validation.
     *
     * Generated from protobuf enum <code>INITIALIZED = 1;</code>
     */
    const INITIALIZED = 1;
    /**
     * RagCorpus is provisioned successfully and is ready to serve.
     *
     * Generated from protobuf enum <code>ACTIVE = 2;</code>
     */
    const ACTIVE = 2;
    /**
     * RagCorpus is in a problematic situation.
     * See `error_message` field for details.
     *
     * Generated from protobuf enum <code>ERROR = 3;</code>
     */
    const ERROR = 3;

    private static $valueToName = [
        self::UNKNOWN => 'UNKNOWN',
        self::INITIALIZED => 'INITIALIZED',
        self::ACTIVE => 'ACTIVE',
        self::ERROR => 'ERROR',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}


