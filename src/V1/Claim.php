<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/aiplatform/v1/vertex_rag_service.proto

namespace Google\Cloud\AIPlatform\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Claim that is extracted from the input text and facts that support it.
 *
 * Generated from protobuf message <code>google.cloud.aiplatform.v1.Claim</code>
 */
class Claim extends \Google\Protobuf\Internal\Message
{
    /**
     * Index in the input text where the claim starts (inclusive).
     *
     * Generated from protobuf field <code>optional int32 start_index = 1;</code>
     */
    protected $start_index = null;
    /**
     * Index in the input text where the claim ends (exclusive).
     *
     * Generated from protobuf field <code>optional int32 end_index = 2;</code>
     */
    protected $end_index = null;
    /**
     * Indexes of the facts supporting this claim.
     *
     * Generated from protobuf field <code>repeated int32 fact_indexes = 3;</code>
     */
    private $fact_indexes;
    /**
     * Confidence score of this corroboration.
     *
     * Generated from protobuf field <code>optional float score = 4;</code>
     */
    protected $score = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $start_index
     *           Index in the input text where the claim starts (inclusive).
     *     @type int $end_index
     *           Index in the input text where the claim ends (exclusive).
     *     @type array<int>|\Google\Protobuf\Internal\RepeatedField $fact_indexes
     *           Indexes of the facts supporting this claim.
     *     @type float $score
     *           Confidence score of this corroboration.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Cloud\Aiplatform\V1\VertexRagService::initOnce();
        parent::__construct($data);
    }

    /**
     * Index in the input text where the claim starts (inclusive).
     *
     * Generated from protobuf field <code>optional int32 start_index = 1;</code>
     * @return int
     */
    public function getStartIndex()
    {
        return isset($this->start_index) ? $this->start_index : 0;
    }

    public function hasStartIndex()
    {
        return isset($this->start_index);
    }

    public function clearStartIndex()
    {
        unset($this->start_index);
    }

    /**
     * Index in the input text where the claim starts (inclusive).
     *
     * Generated from protobuf field <code>optional int32 start_index = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setStartIndex($var)
    {
        GPBUtil::checkInt32($var);
        $this->start_index = $var;

        return $this;
    }

    /**
     * Index in the input text where the claim ends (exclusive).
     *
     * Generated from protobuf field <code>optional int32 end_index = 2;</code>
     * @return int
     */
    public function getEndIndex()
    {
        return isset($this->end_index) ? $this->end_index : 0;
    }

    public function hasEndIndex()
    {
        return isset($this->end_index);
    }

    public function clearEndIndex()
    {
        unset($this->end_index);
    }

    /**
     * Index in the input text where the claim ends (exclusive).
     *
     * Generated from protobuf field <code>optional int32 end_index = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setEndIndex($var)
    {
        GPBUtil::checkInt32($var);
        $this->end_index = $var;

        return $this;
    }

    /**
     * Indexes of the facts supporting this claim.
     *
     * Generated from protobuf field <code>repeated int32 fact_indexes = 3;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getFactIndexes()
    {
        return $this->fact_indexes;
    }

    /**
     * Indexes of the facts supporting this claim.
     *
     * Generated from protobuf field <code>repeated int32 fact_indexes = 3;</code>
     * @param array<int>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFactIndexes($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::INT32);
        $this->fact_indexes = $arr;

        return $this;
    }

    /**
     * Confidence score of this corroboration.
     *
     * Generated from protobuf field <code>optional float score = 4;</code>
     * @return float
     */
    public function getScore()
    {
        return isset($this->score) ? $this->score : 0.0;
    }

    public function hasScore()
    {
        return isset($this->score);
    }

    public function clearScore()
    {
        unset($this->score);
    }

    /**
     * Confidence score of this corroboration.
     *
     * Generated from protobuf field <code>optional float score = 4;</code>
     * @param float $var
     * @return $this
     */
    public function setScore($var)
    {
        GPBUtil::checkFloat($var);
        $this->score = $var;

        return $this;
    }

}

