<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/aiplatform/v1/io.proto

namespace Google\Cloud\AIPlatform\V1\GoogleDriveSource\ResourceId;

use UnexpectedValueException;

/**
 * The type of the Google Drive resource.
 *
 * Protobuf type <code>google.cloud.aiplatform.v1.GoogleDriveSource.ResourceId.ResourceType</code>
 */
class ResourceType
{
    /**
     * Unspecified resource type.
     *
     * Generated from protobuf enum <code>RESOURCE_TYPE_UNSPECIFIED = 0;</code>
     */
    const RESOURCE_TYPE_UNSPECIFIED = 0;
    /**
     * File resource type.
     *
     * Generated from protobuf enum <code>RESOURCE_TYPE_FILE = 1;</code>
     */
    const RESOURCE_TYPE_FILE = 1;
    /**
     * Folder resource type.
     *
     * Generated from protobuf enum <code>RESOURCE_TYPE_FOLDER = 2;</code>
     */
    const RESOURCE_TYPE_FOLDER = 2;

    private static $valueToName = [
        self::RESOURCE_TYPE_UNSPECIFIED => 'RESOURCE_TYPE_UNSPECIFIED',
        self::RESOURCE_TYPE_FILE => 'RESOURCE_TYPE_FILE',
        self::RESOURCE_TYPE_FOLDER => 'RESOURCE_TYPE_FOLDER',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}


