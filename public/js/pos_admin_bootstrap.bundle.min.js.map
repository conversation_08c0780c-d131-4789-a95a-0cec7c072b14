{"version": 3, "sources": ["../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/event-handler.js", "../../js/src/base-component.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../node_modules/@popperjs/core/lib/enums.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeName.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindow.js", "../../node_modules/@popperjs/core/lib/dom-utils/instanceOf.js", "../../node_modules/@popperjs/core/lib/modifiers/applyStyles.js", "../../node_modules/@popperjs/core/lib/utils/getBasePlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/contains.js", "../../node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js", "../../node_modules/@popperjs/core/lib/dom-utils/isTableElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getParentNode.js", "../../node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js", "../../node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js", "../../node_modules/@popperjs/core/lib/utils/within.js", "../../node_modules/@popperjs/core/lib/utils/mergePaddingObject.js", "../../node_modules/@popperjs/core/lib/utils/getFreshSideObject.js", "../../node_modules/@popperjs/core/lib/utils/expandToHashMap.js", "../../node_modules/@popperjs/core/lib/modifiers/arrow.js", "../../node_modules/@popperjs/core/lib/modifiers/computeStyles.js", "../../node_modules/@popperjs/core/lib/modifiers/eventListeners.js", "../../node_modules/@popperjs/core/lib/utils/getOppositePlacement.js", "../../node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js", "../../node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js", "../../node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js", "../../node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js", "../../node_modules/@popperjs/core/lib/utils/rectToClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js", "../../node_modules/@popperjs/core/lib/utils/getVariation.js", "../../node_modules/@popperjs/core/lib/utils/computeOffsets.js", "../../node_modules/@popperjs/core/lib/utils/detectOverflow.js", "../../node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js", "../../node_modules/@popperjs/core/lib/modifiers/flip.js", "../../node_modules/@popperjs/core/lib/modifiers/hide.js", "../../node_modules/@popperjs/core/lib/modifiers/offset.js", "../../node_modules/@popperjs/core/lib/modifiers/popperOffsets.js", "../../node_modules/@popperjs/core/lib/modifiers/preventOverflow.js", "../../node_modules/@popperjs/core/lib/utils/getAltAxis.js", "../../node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js", "../../node_modules/@popperjs/core/lib/utils/orderModifiers.js", "../../node_modules/@popperjs/core/lib/createPopper.js", "../../node_modules/@popperjs/core/lib/utils/debounce.js", "../../node_modules/@popperjs/core/lib/utils/mergeByName.js", "../../node_modules/@popperjs/core/lib/popper-lite.js", "../../node_modules/@popperjs/core/lib/popper.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "names": ["storeData", "id", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "trim", "getSelectorFromElement", "querySelector", "getElementFromSelector", "getTransitionDurationFromElement", "_window$getComputedSt", "window", "getComputedStyle", "transitionDuration", "transitionDelay", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "split", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "obj", "nodeType", "emulateTransitionEnd", "duration", "called", "emulatedDuration", "addEventListener", "listener", "removeEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "toString", "call", "match", "toLowerCase", "RegExp", "test", "Error", "toUpperCase", "isVisible", "style", "parentNode", "elementStyle", "parentNodeStyle", "display", "visibility", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "hasAttribute", "onDOMContentLoaded", "callback", "readyState", "isRTL", "documentElement", "dir", "mapData", "set", "key", "data", "bs<PERSON><PERSON>", "get", "keyProperties", "delete", "Data", "instance", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "getUidEvent", "uid", "getEvent", "<PERSON><PERSON><PERSON><PERSON>", "events", "handler", "delegationSelector", "uidEventList", "i", "len", "length", "event", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "replace", "custom", "has", "add<PERSON><PERSON><PERSON>", "oneOff", "_normalizeParams", "handlers", "previousFn", "fn", "dom<PERSON><PERSON>s", "querySelectorAll", "target", "this", "<PERSON><PERSON><PERSON><PERSON>", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "on", "one", "_normalizeParams2", "inNamespace", "isNamespace", "startsWith", "elementEvent", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "includes", "removeNamespacedHandlers", "slice", "keyHandlers", "trigger", "args", "jQueryEvent", "$", "isNative", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "defineProperty", "preventDefault", "BaseComponent", "_element", "constructor", "DATA_KEY", "dispose", "getInstance", "NAME", "<PERSON><PERSON>", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "closest", "_this", "classList", "remove", "contains", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "jQueryInterface", "each", "handle<PERSON><PERSON><PERSON>", "alertInstance", "JQUERY_NO_CONFLICT", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "<PERSON><PERSON>", "toggle", "setAttribute", "normalizeData", "val", "normalizeDataKey", "chr", "button", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "SelectorEngine", "matches", "find", "_ref", "concat", "Element", "prototype", "findOne", "children", "_ref2", "child", "parents", "ancestor", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "EVENT_KEY", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "PointerType", "TOUCH", "PEN", "Carousel", "_BaseComponent", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "index", "_this2", "activeIndex", "_getItemIndex", "direction", "_extends", "_handleSwipe", "absDeltax", "abs", "_this3", "_keydown", "_addTouchEventListeners", "_this4", "start", "pointerType", "clientX", "touches", "end", "clearTimeout", "itemImg", "e", "add", "move", "tagName", "indexOf", "_getItemByDirection", "activeElement", "isNextDirection", "isPrevDirection", "lastItemIndex", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "from", "_setActiveIndicatorElement", "indicators", "nextIndicator", "elementInterval", "parseInt", "defaultInterval", "directionalClassName", "orderClassName", "_this5", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "carouselInterface", "action", "TypeError", "ride", "dataApiClickHandler", "slideIndex", "carousels", "parent", "Collapse", "_isTransitioning", "_triggerArray", "SELECTOR_DATA_TOGGLE", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "j<PERSON>y", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "bottom", "right", "basePlacements", "variationPlacements", "reduce", "acc", "placement", "placements", "modifierPhases", "getNodeName", "nodeName", "getWindow", "node", "ownerDocument", "defaultView", "isHTMLElement", "HTMLElement", "applyStyles$1", "name", "enabled", "phase", "state", "elements", "styles", "assign", "effect", "initialStyles", "popper", "options", "strategy", "margin", "arrow", "reference", "hasOwnProperty", "attribute", "requires", "getBasePlacement", "getLayoutRect", "x", "y", "width", "offsetWidth", "height", "rootNode", "getRootNode", "ShadowRoot", "isSameNode", "host", "isTableElement", "getDocumentElement", "getParentNode", "assignedSlot", "getTrueOffsetParent", "offsetParent", "html", "getOffsetParent", "currentNode", "css", "transform", "perspective", "<PERSON><PERSON><PERSON><PERSON>", "getContainingBlock", "getMainAxisFromPlacement", "within", "min", "max", "mergePaddingObject", "paddingObject", "expandToHashMap", "hashMap", "arrow$1", "_state$modifiersData$", "arrowElement", "popperOffsets", "modifiersData", "basePlacement", "axis", "padding", "arrowRect", "minProp", "maxProp", "endDiff", "rects", "startDiff", "arrowOffsetParent", "clientSize", "clientHeight", "clientWidth", "centerToReference", "center", "axisProp", "centerOffset", "_options$element", "_options$padding", "requiresIfExists", "unsetSides", "mapToStyles", "_Object$assign2", "popperRect", "offsets", "gpuAcceleration", "adaptive", "_roundOffsets", "dpr", "devicePixelRatio", "round", "roundOffsets", "hasX", "hasY", "sideX", "sideY", "win", "_Object$assign", "commonStyles", "computeStyles$1", "_ref3", "_options$gpuAccelerat", "_options$adaptive", "data-popper-placement", "passive", "eventListeners", "_options$scroll", "scroll", "_options$resize", "resize", "scrollParents", "scrollParent", "update", "hash", "getOppositePlacement", "matched", "getOppositeVariationPlacement", "getWindowScroll", "pageXOffset", "pageYOffset", "getWindowScrollBarX", "isScrollParent", "_getComputedStyle", "overflow", "overflowX", "overflowY", "listScrollParents", "list", "getScrollParent", "isBody", "visualViewport", "updatedList", "rectToClientRect", "getClientRectFromMixedType", "clippingParent", "userAgent", "getViewportRect", "clientTop", "clientLeft", "getInnerBoundingClientRect", "winScroll", "scrollWidth", "scrollHeight", "getDocumentRect", "getClippingRect", "boundary", "rootBoundary", "mainClippingParents", "clippingParents", "clipperElement", "getClippingParents", "firstClippingParent", "clippingRect", "accRect", "getVariation", "computeOffsets", "variation", "commonX", "commonY", "mainAxis", "ceil", "detectOverflow", "_options", "_options$placement", "_options$boundary", "_options$rootBoundary", "_options$elementConte", "elementContext", "_options$altBoundary", "altBoundary", "altContext", "referenceElement", "clippingClientRect", "contextElement", "referenceClientRect", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "multiply", "computeAutoPlacement", "flipVariations", "_options$allowedAutoP", "allowedAutoPlacements", "allPlacements", "allowedPlacements", "overflows", "sort", "a", "b", "flip$1", "_skip", "_options$mainAxis", "checkMainAxis", "_options$altAxis", "altAxis", "checkAltAxis", "specifiedFallbackPlacements", "fallbackPlacements", "_options$flipVariatio", "preferredPlacement", "oppositePlacement", "getExpandedFallbackPlacements", "referenceRect", "checksMap", "Map", "makeFallbackChecks", "firstFittingPlacement", "_basePlacement", "isStartVariation", "isVertical", "mainVariationSide", "altVariationSide", "checks", "every", "check", "_loop", "_i", "fittingPlacement", "reset", "getSideOffsets", "preventedOffsets", "isAnySideFullyClipped", "some", "side", "hide$1", "preventOverflow", "referenceOverflow", "popperAltOverflow", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "data-popper-reference-hidden", "data-popper-escaped", "offset$1", "_options$offset", "invertDistance", "skidding", "distance", "distanceAndSkiddingToXY", "_data$state$placement", "popperOffsets$1", "preventOverflow$1", "_options$tether", "tether", "_options$tetherOffset", "tetherOffset", "isBasePlacement", "tetherOffsetValue", "mainSide", "altSide", "additive", "minLen", "maxLen", "arrowPaddingObject", "arrowPaddingMin", "arrowPaddingMax", "arrowLen", "minOffset", "maxOffset", "clientOffset", "offsetModifierValue", "tetherMin", "tetherMax", "preventedOffset", "_mainSide", "_altSide", "_offset", "_preventedOffset", "getCompositeRect", "elementOrVirtualElement", "isFixed", "isOffsetParentAnElement", "order", "modifiers", "map", "visited", "result", "modifier", "dep", "depModifier", "DEFAULT_OPTIONS", "areValidElements", "_len", "arguments", "Array", "_key", "popperGenerator", "generatorOptions", "_generatorOptions", "_generatorOptions$def", "defaultModifiers", "_generatorOptions$def2", "defaultOptions", "pending", "orderedModifiers", "effectCleanupFns", "isDestroyed", "setOptions", "cleanupModifierEffects", "merged", "orderModifiers", "current", "existing", "m", "_ref3$options", "cleanupFn", "noopFn", "forceUpdate", "_state$elements", "_state$orderedModifie", "_state$orderedModifie2", "Promise", "resolve", "then", "undefined", "destroy", "onFirstUpdate", "createPopper", "computeStyles", "applyStyles", "flip", "REGEXP_KEYDOWN", "ARROW_UP_KEY", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "popperConfig", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "disabled", "isActive", "clearMenus", "getParentFromElement", "<PERSON><PERSON>", "_getPopperConfig", "focus", "stopPropagation", "_getPlacement", "parentDropdown", "isEnd", "getPropertyValue", "dropdownInterface", "toggles", "context", "clickEvent", "dropdownMenu", "dataApiKeydownHandler", "items", "backdrop", "Modal", "_dialog", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_scrollbarWidth", "showEvent", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "transition", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "append<PERSON><PERSON><PERSON>", "_enforceFocus", "transitionComplete", "_this6", "_triggerBackdropTransition", "_this7", "_this8", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "_this9", "animate", "createElement", "className", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "_this10", "isModalOverflowing", "modalTransitionDuration", "paddingLeft", "paddingRight", "innerWidth", "_getScrollbarWidth", "_this11", "actualPadding", "calculatedPadding", "<PERSON><PERSON><PERSON><PERSON>", "marginRight", "<PERSON><PERSON><PERSON><PERSON>", "scrollDiv", "scrollbarWidth", "_this12", "uriAttrs", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "DefaultAllowlist", "*", "area", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "el", "el<PERSON>ame", "attributeList", "allowedAttributes", "attr", "allowedAttributeList", "attrName", "nodeValue", "regExp", "attrRegex", "allowedAttribute", "innerHTML", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "customClass", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "dataKey", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "findShadowRoot", "attachShadow", "root", "isInTheDom", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "_tip$classList", "complete", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "updateAttachment", "flipModifier", "_handlePopperPlacementChange", "CLASS_PREFIX", "eventIn", "eventOut", "_fixTitle", "originalTitleType", "dataAttributes", "dataAttr", "tabClass", "token", "tClass", "popperData", "Popover", "_getContent", "method", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targetSelector", "targetBCR", "item", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "queries", "link", "join", "listGroup", "SELECTOR_NAV_LINKS", "navItem", "spy", "Tab", "listElement", "itemSelector", "hideEvent", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdown", "autohide", "Toast", "_clearTimeout"], "mappings": ";;;;;uvBAOA,ICOQA,EACFC,EDWAC,EAAS,SAAAC,GACb,GACEA,GAAUC,KAAKC,MArBH,IAqBSD,KAAKE,gBACnBC,SAASC,eAAeL,IAEjC,OAAOA,GAGHM,EAAc,SAAAC,GAClB,IAAIC,EAAWD,EAAQE,aAAa,kBAEpC,IAAKD,GAAyB,MAAbA,EAAkB,CACjC,IAAME,EAAWH,EAAQE,aAAa,QAEtCD,EAAWE,GAAyB,MAAbA,EAAmBA,EAASC,OAAS,KAG9D,OAAOH,GAGHI,EAAyB,SAAAL,GAC7B,IAAMC,EAAWF,EAAYC,GAE7B,OAAIC,GACKJ,SAASS,cAAcL,GAAYA,EAGrC,MAGHM,EAAyB,SAAAP,GAC7B,IAAMC,EAAWF,EAAYC,GAE7B,OAAOC,EAAWJ,SAASS,cAAcL,GAAY,MAGjDO,EAAmC,SAAAR,GACvC,IAAKA,EACH,OAAO,EAFyC,IAAAS,EAMJC,OAAOC,iBAAiBX,GAAhEY,EAN4CH,EAM5CG,mBAAoBC,EANwBJ,EAMxBI,gBAEpBC,EAA0BC,OAAOC,WAAWJ,GAC5CK,EAAuBF,OAAOC,WAAWH,GAG/C,OAAKC,GAA4BG,GAKjCL,EAAqBA,EAAmBM,MAAM,KAAK,GACnDL,EAAkBA,EAAgBK,MAAM,KAAK,GAxEf,KA0EtBH,OAAOC,WAAWJ,GAAsBG,OAAOC,WAAWH,KAPzD,GAULM,EAAuB,SAAAnB,GAC3BA,EAAQoB,cAAc,IAAIC,MA7EL,mBAgFjBC,EAAY,SAAAC,GAAG,OAAKA,EAAI,IAAMA,GAAKC,UAEnCC,EAAuB,SAACzB,EAAS0B,GACrC,IAAIC,GAAS,EAEPC,EAAmBF,EADD,EAQxB1B,EAAQ6B,iBA5Fa,iBAuFrB,SAASC,IACPH,GAAS,EACT3B,EAAQ+B,oBAzFW,gBAyFyBD,MAI9CE,YAAW,WACJL,GACHR,EAAqBnB,KAEtB4B,IAGCK,EAAkB,SAACC,EAAeC,EAAQC,GAC9CC,OAAOC,KAAKF,GAAaG,SAAQ,SAAAC,GAC/B,IAnGWjB,EAmGLkB,EAAgBL,EAAYI,GAC5BE,EAAQP,EAAOK,GACfG,EAAYD,GAASpB,EAAUoB,GACnC,UArGAnB,OADSA,EAuGFmB,GArGT,GAAUnB,EAGL,GAAGqB,SAASC,KAAKtB,GAAKuB,MAAM,eAAe,GAAGC,cAoGnD,IAAK,IAAIC,OAAOP,GAAeQ,KAAKN,GAClC,MAAM,IAAIO,MACLhB,EAAciB,cAAdjB,aACQM,EADX,oBACuCG,EADpCT,wBAEmBO,EAFtB,UAOFW,EAAY,SAAApD,GAChB,IAAKA,EACH,OAAO,EAGT,GAAIA,EAAQqD,OAASrD,EAAQsD,YAActD,EAAQsD,WAAWD,MAAO,CACnE,IAAME,EAAe5C,iBAAiBX,GAChCwD,EAAkB7C,iBAAiBX,EAAQsD,YAEjD,MAAgC,SAAzBC,EAAaE,SACU,SAA5BD,EAAgBC,SACY,WAA5BF,EAAaG,WAGjB,OAAO,GA0BHC,EAAO,WAAA,OAAM,cAEbC,EAAS,SAAA5D,GAAO,OAAIA,EAAQ6D,cAE5BC,EAAY,WAAM,IACdC,EAAWrD,OAAXqD,OAER,OAAIA,IAAWlE,SAASmE,KAAKC,aAAa,qBACjCF,EAGF,MAGHG,EAAqB,SAAAC,GACG,YAAxBtE,SAASuE,WACXvE,SAASgC,iBAAiB,mBAAoBsC,GAE9CA,KAIEE,EAAyC,QAAjCxE,SAASyE,gBAAgBC,IC/KjCC,GACElF,EAAY,GACdC,EAAK,EACF,CACLkF,IADK,SACDzE,EAAS0E,EAAKC,QACa,IAAlB3E,EAAQ4E,QACjB5E,EAAQ4E,MAAQ,CACdF,IAAAA,EACAnF,GAAAA,GAEFA,KAGFD,EAAUU,EAAQ4E,MAAMrF,IAAMoF,GAEhCE,IAZK,SAYD7E,EAAS0E,GACX,IAAK1E,QAAoC,IAAlBA,EAAQ4E,MAC7B,OAAO,KAGT,IAAME,EAAgB9E,EAAQ4E,MAC9B,OAAIE,EAAcJ,MAAQA,EACjBpF,EAAUwF,EAAcvF,IAG1B,MAETwF,OAxBK,SAwBE/E,EAAS0E,GACd,QAA6B,IAAlB1E,EAAQ4E,MAAnB,CAIA,IAAME,EAAgB9E,EAAQ4E,MAC1BE,EAAcJ,MAAQA,WACjBpF,EAAUwF,EAAcvF,WACxBS,EAAQ4E,WAMjBI,EAAO,SACHC,EAAUP,EAAKC,GACrBH,EAAQC,IAAIQ,EAAUP,EAAKC,IAFzBK,EAAO,SAIHC,EAAUP,GAChB,OAAOF,EAAQK,IAAII,EAAUP,IAL3BM,EAAO,SAOAC,EAAUP,GACnBF,EAAQO,OAAOE,EAAUP,IC/CvBQ,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GAClBC,EAAW,EACTC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAERC,EAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,WASF,SAASC,EAAY5F,EAAS6F,GAC5B,OAAQA,GAAUA,EAAP,KAAeP,KAAiBtF,EAAQsF,UAAYA,IAGjE,SAASQ,EAAS9F,GAChB,IAAM6F,EAAMD,EAAY5F,GAKxB,OAHAA,EAAQsF,SAAWO,EACnBR,EAAcQ,GAAOR,EAAcQ,IAAQ,GAEpCR,EAAcQ,GAsCvB,SAASE,EAAYC,EAAQC,EAASC,QAA2B,IAA3BA,IAAAA,EAAqB,MAGzD,IAFA,IAAMC,EAAe9D,OAAOC,KAAK0D,GAExBI,EAAI,EAAGC,EAAMF,EAAaG,OAAQF,EAAIC,EAAKD,IAAK,CACvD,IAAMG,EAAQP,EAAOG,EAAaC,IAElC,GAAIG,EAAMC,kBAAoBP,GAAWM,EAAML,qBAAuBA,EACpE,OAAOK,EAIX,OAAO,KAGT,SAASE,EAAgBC,EAAmBT,EAASU,GACnD,IAAMC,EAAgC,iBAAZX,EACpBO,EAAkBI,EAAaD,EAAeV,EAGhDY,EAAYH,EAAkBI,QAAQ3B,EAAgB,IACpD4B,EAASxB,EAAasB,GAY5B,OAVIE,IACFF,EAAYE,GAGGrB,EAAasB,IAAIH,KAGhCA,EAAYH,GAGP,CAACE,EAAYJ,EAAiBK,GAGvC,SAASI,EAAWjH,EAAS0G,EAAmBT,EAASU,EAAcO,GACrE,GAAiC,iBAAtBR,GAAmC1G,EAA9C,CAIKiG,IACHA,EAAUU,EACVA,EAAe,MAP4D,IAAAQ,EAU5BV,EAAgBC,EAAmBT,EAASU,GAAtFC,EAVsEO,EAAA,GAU1DX,EAV0DW,EAAA,GAUzCN,EAVyCM,EAAA,GAWvEnB,EAASF,EAAS9F,GAClBoH,EAAWpB,EAAOa,KAAeb,EAAOa,GAAa,IACrDQ,EAAatB,EAAYqB,EAAUZ,EAAiBI,EAAaX,EAAU,MAEjF,GAAIoB,EACFA,EAAWH,OAASG,EAAWH,QAAUA,MAD3C,CAMA,IAAMrB,EAAMD,EAAYY,EAAiBE,EAAkBI,QAAQ5B,EAAgB,KAC7EoC,EAAKV,EAhFb,SAAoC5G,EAASC,EAAUqH,GACrD,OAAO,SAASrB,EAAQM,GAGtB,IAFA,IAAMgB,EAAcvH,EAAQwH,iBAAiBvH,GAElCwH,EAAWlB,EAAXkB,OAAkBA,GAAUA,IAAWC,KAAMD,EAASA,EAAOnE,WACtE,IAAK,IAAI8C,EAAImB,EAAYjB,OAAQF,KAC/B,GAAImB,EAAYnB,KAAOqB,EAOrB,OANAlB,EAAMoB,eAAiBF,EAEnBxB,EAAQiB,QACVU,EAAaC,IAAI7H,EAASuG,EAAMuB,KAAMR,GAGjCA,EAAGS,MAAMN,EAAQ,CAAClB,IAM/B,OAAO,MA8DPyB,CAA2BhI,EAASiG,EAASU,GA7FjD,SAA0B3G,EAASsH,GACjC,OAAO,SAASrB,EAAQM,GAOtB,OANAA,EAAMoB,eAAiB3H,EAEnBiG,EAAQiB,QACVU,EAAaC,IAAI7H,EAASuG,EAAMuB,KAAMR,GAGjCA,EAAGS,MAAM/H,EAAS,CAACuG,KAsF1B0B,CAAiBjI,EAASiG,GAE5BqB,EAAGpB,mBAAqBU,EAAaX,EAAU,KAC/CqB,EAAGd,gBAAkBA,EACrBc,EAAGJ,OAASA,EACZI,EAAGhC,SAAWO,EACduB,EAASvB,GAAOyB,EAEhBtH,EAAQ6B,iBAAiBgF,EAAWS,EAAIV,KAG1C,SAASsB,EAAclI,EAASgG,EAAQa,EAAWZ,EAASC,GAC1D,IAAMoB,EAAKvB,EAAYC,EAAOa,GAAYZ,EAASC,GAE9CoB,IAILtH,EAAQ+B,oBAAoB8E,EAAWS,EAAIa,QAAQjC,WAC5CF,EAAOa,GAAWS,EAAGhC,WAe9B,IAAMsC,EAAe,CACnBQ,GADmB,SAChBpI,EAASuG,EAAON,EAASU,GAC1BM,EAAWjH,EAASuG,EAAON,EAASU,GAAc,IAGpD0B,IALmB,SAKfrI,EAASuG,EAAON,EAASU,GAC3BM,EAAWjH,EAASuG,EAAON,EAASU,GAAc,IAGpDkB,IATmB,SASf7H,EAAS0G,EAAmBT,EAASU,GACvC,GAAiC,iBAAtBD,GAAmC1G,EAA9C,CADqD,IAAAsI,EAKJ7B,EAAgBC,EAAmBT,EAASU,GAAtFC,EAL8C0B,EAAA,GAKlC9B,EALkC8B,EAAA,GAKjBzB,EALiByB,EAAA,GAM/CC,EAAc1B,IAAcH,EAC5BV,EAASF,EAAS9F,GAClBwI,EAAc9B,EAAkB+B,WAAW,KAEjD,QAA+B,IAApBjC,EAAX,CAUIgC,GACFnG,OAAOC,KAAK0D,GAAQzD,SAAQ,SAAAmG,IA1ClC,SAAkC1I,EAASgG,EAAQa,EAAW8B,GAC5D,IAAMC,EAAoB5C,EAAOa,IAAc,GAE/CxE,OAAOC,KAAKsG,GAAmBrG,SAAQ,SAAAsG,GACrC,GAAIA,EAAWC,SAASH,GAAY,CAClC,IAAMpC,EAAQqC,EAAkBC,GAEhCX,EAAclI,EAASgG,EAAQa,EAAWN,EAAMC,gBAAiBD,EAAML,wBAoCrE6C,CAAyB/I,EAASgG,EAAQ0C,EAAchC,EAAkBsC,MAAM,OAIpF,IAAMJ,EAAoB5C,EAAOa,IAAc,GAC/CxE,OAAOC,KAAKsG,GAAmBrG,SAAQ,SAAA0G,GACrC,IAAMJ,EAAaI,EAAYnC,QAAQ1B,EAAe,IAEtD,IAAKmD,GAAe7B,EAAkBoC,SAASD,GAAa,CAC1D,IAAMtC,EAAQqC,EAAkBK,GAEhCf,EAAclI,EAASgG,EAAQa,EAAWN,EAAMC,gBAAiBD,EAAML,4BAvB3E,CAEE,IAAKF,IAAWA,EAAOa,GACrB,OAGFqB,EAAclI,EAASgG,EAAQa,EAAWL,EAAiBI,EAAaX,EAAU,SAsBtFiD,QA/CmB,SA+CXlJ,EAASuG,EAAO4C,GACtB,GAAqB,iBAAV5C,IAAuBvG,EAChC,OAAO,KAGT,IAKIoJ,EALEC,EAAIvF,IACJ+C,EAAYN,EAAMO,QAAQ3B,EAAgB,IAC1CoD,EAAchC,IAAUM,EACxByC,EAAW5D,EAAasB,IAAIH,GAG9B0C,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EACnBC,EAAM,KA4CV,OA1CInB,GAAec,IACjBD,EAAcC,EAAEhI,MAAMkF,EAAO4C,GAE7BE,EAAErJ,GAASkJ,QAAQE,GACnBG,GAAWH,EAAYO,uBACvBH,GAAkBJ,EAAYQ,gCAC9BH,EAAmBL,EAAYS,sBAG7BP,GACFI,EAAM7J,SAASiK,YAAY,eACvBC,UAAUlD,EAAW0C,GAAS,GAElCG,EAAM,IAAIM,YAAYzD,EAAO,CAC3BgD,QAAAA,EACAU,YAAY,SAKI,IAATd,GACT9G,OAAOC,KAAK6G,GAAM5G,SAAQ,SAAAmC,GACxBrC,OAAO6H,eAAeR,EAAKhF,EAAK,CAC9BG,IAD8B,WAE5B,OAAOsE,EAAKzE,SAMhB+E,GACFC,EAAIS,iBAGFX,GACFxJ,EAAQoB,cAAcsI,GAGpBA,EAAID,uBAA2C,IAAhBL,GACjCA,EAAYe,iBAGPT,ICpTLU,EAAAA,WACJ,SAAAA,EAAYpK,GACLA,IAIL0H,KAAK2C,SAAWrK,EAChBgF,EAAahF,EAAS0H,KAAK4C,YAAYC,SAAU7C,0BAGnD8C,QAAA,WACExF,EAAgB0C,KAAK2C,SAAU3C,KAAK4C,YAAYC,UAChD7C,KAAK2C,SAAW,QAKXI,YAAP,SAAmBzK,GACjB,OAAOgF,EAAahF,EAAS0H,KAAK6C,mDAIlC,MAxBY,oBAEVH,GCQAM,EAAO,QAqBPC,EAAAA,SAAAA,uFASJC,MAAA,SAAM5K,GACJ,IAAM6K,EAAc7K,EAAU0H,KAAKoD,gBAAgB9K,GAAW0H,KAAK2C,SAC7DU,EAAcrD,KAAKsD,mBAAmBH,GAExB,OAAhBE,GAAwBA,EAAYtB,kBAIxC/B,KAAKuD,eAAeJ,MAKtBC,gBAAA,SAAgB9K,GACd,OAAOO,EAAuBP,IAAYA,EAAQkL,QAAR,aAG5CF,mBAAA,SAAmBhL,GACjB,OAAO4H,EAAasB,QAAQlJ,EAzCf,qBA4CfiL,eAAA,SAAejL,GAAS,IAAAmL,EAAAzD,KAGtB,GAFA1H,EAAQoL,UAAUC,OAvCC,QAyCdrL,EAAQoL,UAAUE,SA1CJ,QA0CnB,CAKA,IAAM1K,EAAqBJ,EAAiCR,GAE5D4H,EAAaS,IAAIrI,EJ7EE,iBI6EuB,WAAA,OAAMmL,EAAKI,gBAAgBvL,MACrEyB,EAAqBzB,EAASY,QAP5B8G,KAAK6D,gBAAgBvL,MAUzBuL,gBAAA,SAAgBvL,GACVA,EAAQsD,YACVtD,EAAQsD,WAAWkI,YAAYxL,GAGjC4H,EAAasB,QAAQlJ,EA9DP,sBAmETyL,gBAAP,SAAuBtJ,GACrB,OAAOuF,KAAKgE,MAAK,WACf,IAAI/G,EAAOK,EAAa0C,KA5Eb,YA8EN/C,IACHA,EAAO,IAAIgG,EAAMjD,OAGJ,UAAXvF,GACFwC,EAAKxC,GAAQuF,YAKZiE,cAAP,SAAqBC,GACnB,OAAO,SAAUrF,GACXA,GACFA,EAAM4D,iBAGRyB,EAAchB,MAAMlD,iDAtEtB,MAxBa,iBAoBXiD,CAAcP,GAoFpBxC,EAAaQ,GAAGvI,SAhGU,0BAJD,4BAoGyC8K,EAAMgB,cAAc,IAAIhB,IAS1FzG,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAGoD,GAChCrB,EAAE/B,GAAGoD,GAAQC,EAAMc,gBACnBpC,EAAE/B,GAAGoD,GAAMoB,YAAcnB,EACzBtB,EAAE/B,GAAGoD,GAAMqB,WAAa,WAEtB,OADA1C,EAAE/B,GAAGoD,GAAQmB,EACNlB,EAAMc,qBClInB,IAiBMO,EAAAA,SAAAA,+EASJC,OAAA,WAEEvE,KAAK2C,SAAS6B,aAAa,eAAgBxE,KAAK2C,SAASe,UAAUa,OAvB7C,cA4BjBR,gBAAP,SAAuBtJ,GACrB,OAAOuF,KAAKgE,MAAK,WACf,IAAI/G,EAAOK,EAAa0C,KAlCb,aAoCN/C,IACHA,EAAO,IAAIqH,EAAOtE,OAGL,WAAXvF,GACFwC,EAAKxC,kDArBT,MApBa,kBAgBX6J,CAAe5B,GC5BrB,SAAS+B,EAAcC,GACrB,MAAY,SAARA,GAIQ,UAARA,IAIAA,IAAQrL,OAAOqL,GAAKxJ,WACf7B,OAAOqL,GAGJ,KAARA,GAAsB,SAARA,EACT,KAGFA,GAGT,SAASC,EAAiB3H,GACxB,OAAOA,EAAIoC,QAAQ,UAAU,SAAAwF,GAAG,MAAA,IAAQA,EAAIvJ,iBD4C9C6E,EAAaQ,GAAGvI,SA7CU,2BAFG,6BA+CyC,SAAA0G,GACpEA,EAAM4D,iBAEN,IAAMoC,EAAShG,EAAMkB,OAAOyD,QAlDD,6BAoDvBvG,EAAOK,EAAauH,EA1DT,aA2DV5H,IACHA,EAAO,IAAIqH,EAAOO,IAGpB5H,EAAKsH,YAUP/H,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAF,OAC3B+B,EAAE/B,GAAF,OAAa0E,EAAOP,gBACpBpC,EAAE/B,GAAF,OAAWwE,YAAcE,EAEzB3C,EAAE/B,GAAF,OAAWyE,WAAa,WAEtB,OADA1C,EAAE/B,GAAF,OAAauE,EACNG,EAAOP,qBCvEpB,IAAMe,EAAc,CAClBC,iBADkB,SACDzM,EAAS0E,EAAKhC,GAC7B1C,EAAQkM,aAAR,WAAgCG,EAAiB3H,GAAQhC,IAG3DgK,oBALkB,SAKE1M,EAAS0E,GAC3B1E,EAAQ2M,gBAAR,WAAmCN,EAAiB3H,KAGtDkI,kBATkB,SASA5M,GAChB,IAAKA,EACH,MAAO,GAGT,IAAM6M,EAAa,GAUnB,OARAxK,OAAOC,KAAKtC,EAAQ8M,SACjBC,QAAO,SAAArI,GAAG,OAAIA,EAAI+D,WAAW,SAC7BlG,SAAQ,SAAAmC,GACP,IAAIsI,EAAUtI,EAAIoC,QAAQ,MAAO,IACjCkG,EAAUA,EAAQC,OAAO,GAAGlK,cAAgBiK,EAAQhE,MAAM,EAAGgE,EAAQ1G,QACrEuG,EAAWG,GAAWb,EAAcnM,EAAQ8M,QAAQpI,OAGjDmI,GAGTK,iBA3BkB,SA2BDlN,EAAS0E,GACxB,OAAOyH,EAAcnM,EAAQE,aAAR,WAAgCmM,EAAiB3H,MAGxEyI,OA/BkB,SA+BXnN,GACL,IAAMoN,EAAOpN,EAAQqN,wBAErB,MAAO,CACLC,IAAKF,EAAKE,IAAMzN,SAASmE,KAAKuJ,UAC9BC,KAAMJ,EAAKI,KAAO3N,SAASmE,KAAKyJ,aAIpCC,SAxCkB,SAwCT1N,GACP,MAAO,CACLsN,IAAKtN,EAAQ2N,UACbH,KAAMxN,EAAQ4N,cC3DdC,EAAiB,CACrBC,QADqB,SACb9N,EAASC,GACf,OAAOD,EAAQ8N,QAAQ7N,IAGzB8N,KALqB,SAKhB9N,EAAUD,GAAoC,IAAAgO,EACjD,YADiD,IAApChO,IAAAA,EAAUH,SAASyE,kBACzB0J,EAAA,IAAGC,OAAHlG,MAAAiG,EAAaE,QAAQC,UAAU3G,iBAAiB3E,KAAK7C,EAASC,KAGvEmO,QATqB,SASbnO,EAAUD,GAChB,YADoD,IAApCA,IAAAA,EAAUH,SAASyE,iBAC5B4J,QAAQC,UAAU7N,cAAcuC,KAAK7C,EAASC,IAGvDoO,SAbqB,SAaZrO,EAASC,GAAU,IAAAqO,EACpBD,GAAWC,EAAA,IAAGL,OAAHlG,MAAAuG,EAAatO,EAAQqO,UAEtC,OAAOA,EAAStB,QAAO,SAAAwB,GAAK,OAAIA,EAAMT,QAAQ7N,OAGhDuO,QAnBqB,SAmBbxO,EAASC,GAKf,IAJA,IAAMuO,EAAU,GAEZC,EAAWzO,EAAQsD,WAEhBmL,GAAYA,EAASjN,WAAakN,KAAKC,cA1BhC,IA0BgDF,EAASjN,UACjEkG,KAAKoG,QAAQW,EAAUxO,IACzBuO,EAAQI,KAAKH,GAGfA,EAAWA,EAASnL,WAGtB,OAAOkL,GAGTK,KAnCqB,SAmChB7O,EAASC,GAGZ,IAFA,IAAI6O,EAAW9O,EAAQ+O,uBAEhBD,GAAU,CACf,GAAIA,EAAShB,QAAQ7N,GACnB,MAAO,CAAC6O,GAGVA,EAAWA,EAASC,uBAGtB,MAAO,IAGTC,KAjDqB,SAiDhBhP,EAASC,GAGZ,IAFA,IAAI+O,EAAOhP,EAAQiP,mBAEZD,GAAM,CACX,GAAItH,KAAKoG,QAAQkB,EAAM/O,GACrB,MAAO,CAAC+O,GAGVA,EAAOA,EAAKC,mBAGd,MAAO,KC5CLvE,EAAO,WAEPwE,EAAS,eAQTC,EAAU,CACdC,SAAU,IACVC,UAAU,EACVC,OAAO,EACPC,MAAO,QACPC,MAAM,EACNC,OAAO,GAGHC,EAAc,CAClBN,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,MAAO,mBACPC,KAAM,UACNC,MAAO,WAwCHE,EAAc,CAClBC,MAAO,QACPC,IAAK,OAQDC,EAAAA,SAAAA,GACJ,SAAAA,EAAY9P,EAASmC,GAAQ,IAAAgJ,EAAA,OAC3BA,EAAA4E,EAAAlN,KAAA6E,KAAM1H,IAAN0H,MAEKsI,OAAS,KACd7E,EAAK8E,UAAY,KACjB9E,EAAK+E,eAAiB,KACtB/E,EAAKgF,WAAY,EACjBhF,EAAKiF,YAAa,EAClBjF,EAAKkF,aAAe,KACpBlF,EAAKmF,YAAc,EACnBnF,EAAKoF,YAAc,EAEnBpF,EAAKqF,QAAUrF,EAAKsF,WAAWtO,GAC/BgJ,EAAKuF,mBAAqB7C,EAAeO,QA5BjB,uBA4B8CjD,EAAKd,UAC3Ec,EAAKwF,gBAAkB,iBAAkB9Q,SAASyE,iBAAmBsM,UAAUC,eAAiB,EAChG1F,EAAK2F,cAAgB3I,QAAQzH,OAAOqQ,cAEpC5F,EAAK6F,qBAjBsB7F,oCAgC7B6D,KAAA,WACOtH,KAAK0I,YACR1I,KAAKuJ,OAlFY,WAsFrBC,gBAAA,YAGOrR,SAASsR,QAAU/N,EAAUsE,KAAK2C,WACrC3C,KAAKsH,UAITH,KAAA,WACOnH,KAAK0I,YACR1I,KAAKuJ,OA/FY,WAmGrB1B,MAAA,SAAMhJ,GACCA,IACHmB,KAAKyI,WAAY,GAGftC,EAAeO,QAzEI,2CAyEwB1G,KAAK2C,YAClDlJ,EAAqBuG,KAAK2C,UAC1B3C,KAAK0J,OAAM,IAGbC,cAAc3J,KAAKuI,WACnBvI,KAAKuI,UAAY,QAGnBmB,MAAA,SAAM7K,GACCA,IACHmB,KAAKyI,WAAY,GAGfzI,KAAKuI,YACPoB,cAAc3J,KAAKuI,WACnBvI,KAAKuI,UAAY,MAGfvI,KAAK8I,SAAW9I,KAAK8I,QAAQpB,WAAa1H,KAAKyI,YACjDzI,KAAK4J,kBAEL5J,KAAKuI,UAAYsB,aACd1R,SAAS2R,gBAAkB9J,KAAKwJ,gBAAkBxJ,KAAKsH,MAAMyC,KAAK/J,MACnEA,KAAK8I,QAAQpB,cAKnBsC,GAAA,SAAGC,GAAO,IAAAC,EAAAlK,KACRA,KAAKwI,eAAiBrC,EAAeO,QA1GZ,wBA0G0C1G,KAAK2C,UACxE,IAAMwH,EAAcnK,KAAKoK,cAAcpK,KAAKwI,gBAE5C,KAAIyB,EAAQjK,KAAKsI,OAAO1J,OAAS,GAAKqL,EAAQ,GAI9C,GAAIjK,KAAK0I,WACPxI,EAAaS,IAAIX,KAAK2C,SAzIZ,oBAyIkC,WAAA,OAAMuH,EAAKF,GAAGC,UAD5D,CAKA,GAAIE,IAAgBF,EAGlB,OAFAjK,KAAK6H,aACL7H,KAAK0J,QAIP,IAAMW,EAAYJ,EAAQE,EAzJP,OACA,OA4JnBnK,KAAKuJ,OAAOc,EAAWrK,KAAKsI,OAAO2B,QAGrCnH,QAAA,WACEuF,EAAA5B,UAAM3D,QAAN3H,KAAA6E,MACAE,EAAaC,IAAIH,KAAK2C,SAAU6E,GAEhCxH,KAAKsI,OAAS,KACdtI,KAAK8I,QAAU,KACf9I,KAAKuI,UAAY,KACjBvI,KAAKyI,UAAY,KACjBzI,KAAK0I,WAAa,KAClB1I,KAAKwI,eAAiB,KACtBxI,KAAKgJ,mBAAqB,QAK5BD,WAAA,SAAWtO,GAMT,OALAA,EAAM6P,EAAA,GACD7C,EACAhN,GAELF,EAAgByI,EAAMvI,EAAQuN,GACvBvN,KAGT8P,aAAA,WACE,IAAMC,EAAYxS,KAAKyS,IAAIzK,KAAK6I,aAEhC,KAAI2B,GA/MgB,IA+MpB,CAIA,IAAMH,EAAYG,EAAYxK,KAAK6I,YAEnC7I,KAAK6I,YAAc,EAGfwB,EAAY,GACdrK,KAAKmH,OAIHkD,EAAY,GACdrK,KAAKsH,WAITgC,mBAAA,WAAqB,IAAAoB,EAAA1K,KACfA,KAAK8I,QAAQnB,UACfzH,EAAaQ,GAAGV,KAAK2C,SAzMR,uBAyMiC,SAAA9D,GAAK,OAAI6L,EAAKC,SAAS9L,MAG5C,UAAvBmB,KAAK8I,QAAQjB,QACf3H,EAAaQ,GAAGV,KAAK2C,SA5ML,0BA4MiC,SAAA9D,GAAK,OAAI6L,EAAK7C,MAAMhJ,MACrEqB,EAAaQ,GAAGV,KAAK2C,SA5ML,0BA4MiC,SAAA9D,GAAK,OAAI6L,EAAKhB,MAAM7K,OAGnEmB,KAAK8I,QAAQf,OAAS/H,KAAKiJ,iBAC7BjJ,KAAK4K,6BAITA,wBAAA,WAA0B,IAAAC,EAAA7K,KAClB8K,EAAQ,SAAAjM,GACRgM,EAAKzB,eAAiBnB,EAAYpJ,EAAMkM,YAAYtP,eACtDoP,EAAKjC,YAAc/J,EAAMmM,QACfH,EAAKzB,gBACfyB,EAAKjC,YAAc/J,EAAMoM,QAAQ,GAAGD,UAalCE,EAAM,SAAArM,GACNgM,EAAKzB,eAAiBnB,EAAYpJ,EAAMkM,YAAYtP,iBACtDoP,EAAKhC,YAAchK,EAAMmM,QAAUH,EAAKjC,aAG1CiC,EAAKN,eACsB,UAAvBM,EAAK/B,QAAQjB,QASfgD,EAAKhD,QACDgD,EAAKlC,cACPwC,aAAaN,EAAKlC,cAGpBkC,EAAKlC,aAAerO,YAAW,SAAAuE,GAAK,OAAIgM,EAAKnB,MAAM7K,KAxR5B,IAwR6DgM,EAAK/B,QAAQpB,YAIrGvB,EAAeE,KAxOO,qBAwOiBrG,KAAK2C,UAAU9H,SAAQ,SAAAuQ,GAC5DlL,EAAaQ,GAAG0K,EAzPA,yBAyP2B,SAAAC,GAAC,OAAIA,EAAE5I,uBAGhDzC,KAAKoJ,eACPlJ,EAAaQ,GAAGV,KAAK2C,SA/PJ,2BA+PiC,SAAA9D,GAAK,OAAIiM,EAAMjM,MACjEqB,EAAaQ,GAAGV,KAAK2C,SA/PN,yBA+PiC,SAAA9D,GAAK,OAAIqM,EAAIrM,MAE7DmB,KAAK2C,SAASe,UAAU4H,IArPG,mBAuP3BpL,EAAaQ,GAAGV,KAAK2C,SAvQL,0BAuQiC,SAAA9D,GAAK,OAAIiM,EAAMjM,MAChEqB,EAAaQ,GAAGV,KAAK2C,SAvQN,yBAuQiC,SAAA9D,GAAK,OA5C1C,SAAAA,GAEPA,EAAMoM,SAAWpM,EAAMoM,QAAQrM,OAAS,EAC1CiM,EAAKhC,YAAc,EAEnBgC,EAAKhC,YAAchK,EAAMoM,QAAQ,GAAGD,QAAUH,EAAKjC,YAuCI2C,CAAK1M,MAC9DqB,EAAaQ,GAAGV,KAAK2C,SAvQP,wBAuQiC,SAAA9D,GAAK,OAAIqM,EAAIrM,UAIhE8L,SAAA,SAAS9L,GACP,IAAI,kBAAkBtD,KAAKsD,EAAMkB,OAAOyL,SAIxC,OAAQ3M,EAAM7B,KACZ,IApTiB,YAqTf6B,EAAM4D,iBACNzC,KAAKmH,OACL,MACF,IAvTkB,aAwThBtI,EAAM4D,iBACNzC,KAAKsH,WAMX8C,cAAA,SAAc9R,GAKZ,OAJA0H,KAAKsI,OAAShQ,GAAWA,EAAQsD,WAC/BuK,EAAeE,KA7QC,iBA6QmB/N,EAAQsD,YAC3C,GAEKoE,KAAKsI,OAAOmD,QAAQnT,MAG7BoT,oBAAA,SAAoBrB,EAAWsB,GAC7B,IAAMC,EAlTa,SAkTKvB,EAClBwB,EAlTa,SAkTKxB,EAClBF,EAAcnK,KAAKoK,cAAcuB,GACjCG,EAAgB9L,KAAKsI,OAAO1J,OAAS,EAI3C,IAHuBiN,GAAmC,IAAhB1B,GACjByB,GAAmBzB,IAAgB2B,KAEtC9L,KAAK8I,QAAQhB,KACjC,OAAO6D,EAGT,IACMI,GAAa5B,GA7TA,SA4TLE,GAAgC,EAAI,IACRrK,KAAKsI,OAAO1J,OAEtD,OAAsB,IAAfmN,EACL/L,KAAKsI,OAAOtI,KAAKsI,OAAO1J,OAAS,GACjCoB,KAAKsI,OAAOyD,MAGhBC,mBAAA,SAAmBC,EAAeC,GAChC,IAAMC,EAAcnM,KAAKoK,cAAc6B,GACjCG,EAAYpM,KAAKoK,cAAcjE,EAAeO,QA1S3B,wBA0SyD1G,KAAK2C,WAEvF,OAAOzC,EAAasB,QAAQxB,KAAK2C,SApUpB,oBAoU2C,CACtDsJ,cAAAA,EACA5B,UAAW6B,EACXG,KAAMD,EACNpC,GAAImC,OAIRG,2BAAA,SAA2BhU,GACzB,GAAI0H,KAAKgJ,mBAAoB,CAG3B,IAFA,IAAMuD,EAAapG,EAAeE,KAvThB,UAuTsCrG,KAAKgJ,oBAEpDtK,EAAI,EAAGA,EAAI6N,EAAW3N,OAAQF,IACrC6N,EAAW7N,GAAGgF,UAAUC,OAlUN,UAqUpB,IAAM6I,EAAgBxM,KAAKgJ,mBAAmBrC,SAC5C3G,KAAKoK,cAAc9R,IAGjBkU,GACFA,EAAc9I,UAAU4H,IA1UN,cA+UxB1B,gBAAA,WACE,IAAMtR,EAAU0H,KAAKwI,gBAAkBrC,EAAeO,QAvU7B,wBAuU2D1G,KAAK2C,UAEzF,GAAKrK,EAAL,CAIA,IAAMmU,EAAkBpT,OAAOqT,SAASpU,EAAQE,aAAa,oBAAqB,IAE9EiU,GACFzM,KAAK8I,QAAQ6D,gBAAkB3M,KAAK8I,QAAQ6D,iBAAmB3M,KAAK8I,QAAQpB,SAC5E1H,KAAK8I,QAAQpB,SAAW+E,GAExBzM,KAAK8I,QAAQpB,SAAW1H,KAAK8I,QAAQ6D,iBAAmB3M,KAAK8I,QAAQpB,aAIzE6B,OAAA,SAAOc,EAAW/R,GAAS,IAQrBsU,EACAC,EACAX,EAVqBY,EAAA9M,KACnB2L,EAAgBxF,EAAeO,QAxVZ,wBAwV0C1G,KAAK2C,UAClEoK,EAAqB/M,KAAKoK,cAAcuB,GACxCqB,EAAc1U,GAAYqT,GAAiB3L,KAAK0L,oBAAoBrB,EAAWsB,GAE/EsB,EAAmBjN,KAAKoK,cAAc4C,GACtCE,EAAYzM,QAAQT,KAAKuI,WAgB/B,GA1YmB,SAgYf8B,GACFuC,EA1WmB,sBA2WnBC,EA1WkB,qBA2WlBX,EAjYiB,SAmYjBU,EA/WiB,oBAgXjBC,EA7WkB,qBA8WlBX,EApYkB,SAuYhBc,GAAeA,EAAYtJ,UAAUE,SAtXnB,UAuXpB5D,KAAK0I,YAAa,OAKpB,IADmB1I,KAAKgM,mBAAmBgB,EAAad,GACzCnK,kBAIV4J,GAAkBqB,EAAvB,CAcA,GATAhN,KAAK0I,YAAa,EAEdwE,GACFlN,KAAK6H,QAGP7H,KAAKsM,2BAA2BU,GAChChN,KAAKwI,eAAiBwE,EAElBhN,KAAK2C,SAASe,UAAUE,SA7YP,SA6YmC,CACtDoJ,EAAYtJ,UAAU4H,IAAIuB,GAE1B3Q,EAAO8Q,GAEPrB,EAAcjI,UAAU4H,IAAIsB,GAC5BI,EAAYtJ,UAAU4H,IAAIsB,GAE1B,IAAM1T,EAAqBJ,EAAiC6S,GAE5DzL,EAAaS,IAAIgL,ER9dA,iBQ8d+B,WAC9CqB,EAAYtJ,UAAUC,OAAOiJ,EAAsBC,GACnDG,EAAYtJ,UAAU4H,IA1ZJ,UA4ZlBK,EAAcjI,UAAUC,OA5ZN,SA4ZgCkJ,EAAgBD,GAElEE,EAAKpE,YAAa,EAElBpO,YAAW,WACT4F,EAAasB,QAAQsL,EAAKnK,SA/apB,mBA+a0C,CAC9CsJ,cAAee,EACf3C,UAAW6B,EACXG,KAAMU,EACN/C,GAAIiD,MAEL,MAGLlT,EAAqB4R,EAAezS,QAEpCyS,EAAcjI,UAAUC,OA5aJ,UA6apBqJ,EAAYtJ,UAAU4H,IA7aF,UA+apBtL,KAAK0I,YAAa,EAClBxI,EAAasB,QAAQxB,KAAK2C,SA9bhB,mBA8bsC,CAC9CsJ,cAAee,EACf3C,UAAW6B,EACXG,KAAMU,EACN/C,GAAIiD,IAIJC,GACFlN,KAAK0J,YAMFyD,kBAAP,SAAyB7U,EAASmC,GAChC,IAAIwC,EAAOK,EAAahF,EA/eX,eAgfTwQ,EAAOwB,EAAA,GACN7C,EACA3C,EAAYI,kBAAkB5M,IAGb,iBAAXmC,IACTqO,EAAOwB,EAAA,GACFxB,EACArO,IAIP,IAAM2S,EAA2B,iBAAX3S,EAAsBA,EAASqO,EAAQlB,MAM7D,GAJK3K,IACHA,EAAO,IAAImL,EAAS9P,EAASwQ,IAGT,iBAAXrO,EACTwC,EAAK+M,GAAGvP,QACH,GAAsB,iBAAX2S,EAAqB,CACrC,QAA4B,IAAjBnQ,EAAKmQ,GACd,MAAM,IAAIC,UAAJ,oBAAkCD,EAAlC,KAGRnQ,EAAKmQ,UACItE,EAAQpB,UAAYoB,EAAQwE,OACrCrQ,EAAK4K,QACL5K,EAAKyM,YAIF3F,gBAAP,SAAuBtJ,GACrB,OAAOuF,KAAKgE,MAAK,WACfoE,EAAS+E,kBAAkBnN,KAAMvF,SAI9B8S,oBAAP,SAA2B1O,GACzB,IAAMkB,EAASlH,EAAuBmH,MAEtC,GAAKD,GAAWA,EAAO2D,UAAUE,SA3eT,YA2exB,CAIA,IAAMnJ,EAAM6P,EAAA,GACPxF,EAAYI,kBAAkBnF,GAC9B+E,EAAYI,kBAAkBlF,OAE7BwN,EAAaxN,KAAKxH,aAAa,oBAEjCgV,IACF/S,EAAOiN,UAAW,GAGpBU,EAAS+E,kBAAkBpN,EAAQtF,GAE/B+S,GACFlQ,EAAayC,EA1iBF,eA0iBoBiK,GAAGwD,GAGpC3O,EAAM4D,2DA3cN,OAAOgF,mCAIP,MAtGa,oBA0EXW,CAAiB1F,GA6evBxC,EAAaQ,GAAGvI,SA3gBU,6BAiBE,sCA0fyCiQ,EAASmF,qBAE9ErN,EAAaQ,GAAG1H,OA9gBS,6BA8gBoB,WAG3C,IAFA,IAAMyU,EAAYtH,EAAeE,KA5fR,6BA8fhB3H,EAAI,EAAGC,EAAM8O,EAAU7O,OAAQF,EAAIC,EAAKD,IAC/C0J,EAAS+E,kBAAkBM,EAAU/O,GAAIpB,EAAamQ,EAAU/O,GA7jBnD,mBAwkBjBlC,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAGoD,GAChCrB,EAAE/B,GAAGoD,GAAQoF,EAASrE,gBACtBpC,EAAE/B,GAAGoD,GAAMoB,YAAcgE,EACzBzG,EAAE/B,GAAGoD,GAAMqB,WAAa,WAEtB,OADA1C,EAAE/B,GAAGoD,GAAQmB,EACNiE,EAASrE,qBCllBtB,IAAMf,EAAO,WAKPyE,GAAU,CACdlD,QAAQ,EACRmJ,OAAQ,IAGJ1F,GAAc,CAClBzD,OAAQ,UACRmJ,OAAQ,oBA0BJC,GAAAA,SAAAA,GACJ,SAAAA,EAAYrV,EAASmC,GAAQ,IAAAgJ,GAC3BA,EAAA4E,EAAAlN,KAAA6E,KAAM1H,IAAN0H,MAEK4N,kBAAmB,EACxBnK,EAAKqF,QAAUrF,EAAKsF,WAAWtO,GAC/BgJ,EAAKoK,cAAgB1H,EAAeE,KAC/ByH,sCAA+BxV,EAAQT,GAAvCiW,mDACyCxV,EAAQT,GADpD,MAMF,IAFA,IAAMkW,EAAa5H,EAAeE,KAnBT,+BAqBhB3H,EAAI,EAAGC,EAAMoP,EAAWnP,OAAQF,EAAIC,EAAKD,IAAK,CACrD,IAAMsP,EAAOD,EAAWrP,GAClBnG,EAAWI,EAAuBqV,GAClCC,EAAgB9H,EAAeE,KAAK9N,GACvC8M,QAAO,SAAA6I,GAAS,OAAIA,IAAc5V,KAEpB,OAAbC,GAAqB0V,EAAcrP,SACrC6E,EAAK0K,UAAY5V,EACjBkL,EAAKoK,cAAc3G,KAAK8G,IApBD,OAwB3BvK,EAAK2K,QAAU3K,EAAKqF,QAAQ4E,OAASjK,EAAK4K,aAAe,KAEpD5K,EAAKqF,QAAQ4E,QAChBjK,EAAK6K,0BAA0B7K,EAAKd,SAAUc,EAAKoK,eAGjDpK,EAAKqF,QAAQvE,QACfd,EAAKc,SA/BoBd,oCA+C7Bc,OAAA,WACMvE,KAAK2C,SAASe,UAAUE,SAlER,QAmElB5D,KAAKuO,OAELvO,KAAKwO,UAITA,KAAA,WAAO,IAAAtE,EAAAlK,KACL,IAAIA,KAAK4N,mBAAoB5N,KAAK2C,SAASe,UAAUE,SA1EjC,QA0EpB,CAIA,IAAI6K,EACAC,EAEA1O,KAAKoO,SAUgB,KATvBK,EAAUtI,EAAeE,KA1EN,qBA0E6BrG,KAAKoO,SAClD/I,QAAO,SAAA2I,GACN,MAAmC,iBAAxB9D,EAAKpB,QAAQ4E,OACfM,EAAKxV,aAAa,oBAAsB0R,EAAKpB,QAAQ4E,OAGvDM,EAAKtK,UAAUE,SAvFJ,gBA0FVhF,SACV6P,EAAU,MAId,IAAME,EAAYxI,EAAeO,QAAQ1G,KAAKmO,WAC9C,GAAIM,EAAS,CACX,IAAMG,EAAiBH,EAAQpI,MAAK,SAAA2H,GAAI,OAAIW,IAAcX,KAG1D,IAFAU,EAAcE,EAAiBtR,EAAasR,EAvHjC,eAuH6D,OAErDF,EAAYd,iBAC7B,OAKJ,IADmB1N,EAAasB,QAAQxB,KAAK2C,SAhHjC,oBAiHGZ,iBAAf,CAII0M,GACFA,EAAQ5T,SAAQ,SAAAgU,GACVF,IAAcE,GAChBlB,EAASmB,kBAAkBD,EAAY,QAGpCH,GACHpR,EAAauR,EA1IN,cA0I4B,SAKzC,IAAME,EAAY/O,KAAKgP,gBAEvBhP,KAAK2C,SAASe,UAAUC,OA5HA,YA6HxB3D,KAAK2C,SAASe,UAAU4H,IA5HE,cA8H1BtL,KAAK2C,SAAShH,MAAMoT,GAAa,EAE7B/O,KAAK6N,cAAcjP,QACrBoB,KAAK6N,cAAchT,SAAQ,SAAAvC,GACzBA,EAAQoL,UAAUC,OAjIG,aAkIrBrL,EAAQkM,aAAa,iBAAiB,MAI1CxE,KAAKiP,kBAAiB,GAEtB,IAYMC,EAAU,UADaH,EAAU,GAAGtT,cAAgBsT,EAAUzN,MAAM,IAEpEpI,EAAqBJ,EAAiCkH,KAAK2C,UAEjEzC,EAAaS,IAAIX,KAAK2C,STrMH,iBSsLF,WACfuH,EAAKvH,SAASe,UAAUC,OA1IA,cA2IxBuG,EAAKvH,SAASe,UAAU4H,IA5IF,WADJ,QA+IlBpB,EAAKvH,SAAShH,MAAMoT,GAAa,GAEjC7E,EAAK+E,kBAAiB,GAEtB/O,EAAasB,QAAQ0I,EAAKvH,SAxJf,wBAiKb5I,EAAqBiG,KAAK2C,SAAUzJ,GACpC8G,KAAK2C,SAAShH,MAAMoT,GAAgB/O,KAAK2C,SAASuM,GAAlD,UAGFX,KAAA,WAAO,IAAA7D,EAAA1K,KACL,IAAIA,KAAK4N,kBAAqB5N,KAAK2C,SAASe,UAAUE,SAjKlC,UAqKD1D,EAAasB,QAAQxB,KAAK2C,SAzKjC,oBA0KGZ,iBAAf,CAIA,IAAMgN,EAAY/O,KAAKgP,gBAEvBhP,KAAK2C,SAAShH,MAAMoT,GAAgB/O,KAAK2C,SAASgD,wBAAwBoJ,GAA1E,KAEA7S,EAAO8D,KAAK2C,UAEZ3C,KAAK2C,SAASe,UAAU4H,IA9KE,cA+K1BtL,KAAK2C,SAASe,UAAUC,OAhLA,WADJ,QAmLpB,IAAMwL,EAAqBnP,KAAK6N,cAAcjP,OAC9C,GAAIuQ,EAAqB,EACvB,IAAK,IAAIzQ,EAAI,EAAGA,EAAIyQ,EAAoBzQ,IAAK,CAC3C,IAAM8C,EAAUxB,KAAK6N,cAAcnP,GAC7BsP,EAAOnV,EAAuB2I,GAEhCwM,IAASA,EAAKtK,UAAUE,SAzLZ,UA0LdpC,EAAQkC,UAAU4H,IAvLC,aAwLnB9J,EAAQgD,aAAa,iBAAiB,IAK5CxE,KAAKiP,kBAAiB,GAStBjP,KAAK2C,SAAShH,MAAMoT,GAAa,GACjC,IAAM7V,EAAqBJ,EAAiCkH,KAAK2C,UAEjEzC,EAAaS,IAAIX,KAAK2C,STvPH,iBS6OF,WACf+H,EAAKuE,kBAAiB,GACtBvE,EAAK/H,SAASe,UAAUC,OAlMA,cAmMxB+G,EAAK/H,SAASe,UAAU4H,IApMF,YAqMtBpL,EAAasB,QAAQkJ,EAAK/H,SAzMd,yBAgNd5I,EAAqBiG,KAAK2C,SAAUzJ,OAGtC+V,iBAAA,SAAiBG,GACfpP,KAAK4N,iBAAmBwB,KAG1BtM,QAAA,WACEuF,EAAA5B,UAAM3D,QAAN3H,KAAA6E,MACAA,KAAK8I,QAAU,KACf9I,KAAKoO,QAAU,KACfpO,KAAK6N,cAAgB,KACrB7N,KAAK4N,iBAAmB,QAK1B7E,WAAA,SAAWtO,GAOT,OANAA,EAAM6P,EAAA,GACD7C,GACAhN,IAEE8J,OAAS9D,QAAQhG,EAAO8J,QAC/BhK,EAAgByI,EAAMvI,EAAQuN,IACvBvN,KAGTuU,cAAA,WACE,OAAOhP,KAAK2C,SAASe,UAAUE,SApOrB,SAAA,QACC,YAsObyK,WAAA,WAAa,IAAAxD,EAAA7K,KACL0N,EAAW1N,KAAK8I,QAAhB4E,OAEF9T,EAAU8T,QAEiB,IAAlBA,EAAO2B,aAA+C,IAAd3B,EAAO,KACxDA,EAASA,EAAO,IAGlBA,EAASvH,EAAeO,QAAQgH,GAGlC,IAAMnV,EAAcuV,+CAAwCJ,EAA9C,KAYd,OAVAvH,EAAeE,KAAK9N,EAAUmV,GAC3B7S,SAAQ,SAAAvC,GACP,IAAMgX,EAAWzW,EAAuBP,GAExCuS,EAAKyD,0BACHgB,EACA,CAAChX,OAIAoV,KAGTY,0BAAA,SAA0BhW,EAASiX,GACjC,GAAKjX,GAAYiX,EAAa3Q,OAA9B,CAIA,IAAM4Q,EAASlX,EAAQoL,UAAUE,SA5Qb,QA8QpB2L,EAAa1U,SAAQ,SAAAmT,GACfwB,EACFxB,EAAKtK,UAAUC,OA7QM,aA+QrBqK,EAAKtK,UAAU4H,IA/QM,aAkRvB0C,EAAKxJ,aAAa,gBAAiBgL,UAMhCV,kBAAP,SAAyBxW,EAASmC,GAChC,IAAIwC,EAAOK,EAAahF,EAhTX,eAiTPwQ,EAAOwB,EAAA,GACR7C,GACA3C,EAAYI,kBAAkB5M,GACX,iBAAXmC,GAAuBA,EAASA,EAAS,IAWtD,IARKwC,GAAQ6L,EAAQvE,QAA4B,iBAAX9J,GAAuB,YAAYc,KAAKd,KAC5EqO,EAAQvE,QAAS,GAGdtH,IACHA,EAAO,IAAI0Q,EAASrV,EAASwQ,IAGT,iBAAXrO,EAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAI4S,UAAJ,oBAAkC5S,EAAlC,KAGRwC,EAAKxC,SAIFsJ,gBAAP,SAAuBtJ,GACrB,OAAOuF,KAAKgE,MAAK,WACf2J,EAASmB,kBAAkB9O,KAAMvF,+CA9PnC,OAAOgN,oCAIP,MAhFa,oBAqCXkG,CAAiBjL,GAgTvBxC,EAAaQ,GAAGvI,SAnUU,6BAWG,+BAwTyC,SAAU0G,GAEjD,MAAzBA,EAAMkB,OAAOyL,SACf3M,EAAM4D,iBAGR,IAAMgN,EAAc3K,EAAYI,kBAAkBlF,MAC5CzH,EAAWI,EAAuBqH,MACfmG,EAAeE,KAAK9N,GAE5BsC,SAAQ,SAAAvC,GACvB,IACImC,EADEwC,EAAOK,EAAahF,EAhWb,eAkWT2E,GAEmB,OAAjBA,EAAKmR,SAAkD,iBAAvBqB,EAAY/B,SAC9CzQ,EAAK6L,QAAQ4E,OAAS+B,EAAY/B,OAClCzQ,EAAKmR,QAAUnR,EAAKoR,cAGtB5T,EAAS,UAETA,EAASgV,EAGX9B,GAASmB,kBAAkBxW,EAASmC,SAWxC+B,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAGoD,GAChCrB,EAAE/B,GAAGoD,GAAQ2K,GAAS5J,gBACtBpC,EAAE/B,GAAGoD,GAAMoB,YAAcuJ,GACzBhM,EAAE/B,GAAGoD,GAAMqB,WAAa,WAEtB,OADA1C,EAAE/B,GAAGoD,GAAQmB,EACNwJ,GAAS5J,qBClaf,IAAI6B,GAAM,MACN8J,GAAS,SACTC,GAAQ,QACR7J,GAAO,OAEP8J,GAAiB,CAAChK,GAAK8J,GAAQC,GAAO7J,IAOtC+J,GAAmCD,GAAeE,QAAO,SAAUC,EAAKC,GACjF,OAAOD,EAAIxJ,OAAO,CAACyJ,EAAAA,SAAyBA,EAAAA,WAC3C,IACQC,GAA0B,GAAG1J,OAAOqJ,GAAgB,CAX7C,SAWqDE,QAAO,SAAUC,EAAKC,GAC3F,OAAOD,EAAIxJ,OAAO,CAACyJ,EAAWA,EAAAA,SAAyBA,EAAAA,WACtD,IAaQE,GAAiB,CAXJ,aACN,OACK,YAEC,aACN,OACK,YAEE,cACN,QACK,cC7BT,SAASC,GAAY7X,GAClC,OAAOA,GAAWA,EAAQ8X,UAAY,IAAI/U,cAAgB,KCE7C,SAASgV,GAAUC,GAChC,GAAwB,oBAApBA,EAAKpV,WAAkC,CACzC,IAAIqV,EAAgBD,EAAKC,cACzB,OAAOA,GAAgBA,EAAcC,aAAwBxX,OAG/D,OAAOsX,ECLT,SAAS1W,GAAU0W,GAEjB,OAAOA,aADUD,GAAUC,GAAM9J,SACI8J,aAAgB9J,QAMvD,SAASiK,GAAcH,GAErB,OAAOA,aADUD,GAAUC,GAAMI,aACIJ,aAAgBI,YCgEvD,IAAAC,GAAe,CACbC,KAAM,cACNC,SAAS,EACTC,MAAO,QACPlR,GA9EF,SAAqB0G,GACnB,IAAIyK,EAAQzK,EAAKyK,MACjBpW,OAAOC,KAAKmW,EAAMC,UAAUnW,SAAQ,SAAU+V,GAC5C,IAAIjV,EAAQoV,EAAME,OAAOL,IAAS,GAC9BzL,EAAa4L,EAAM5L,WAAWyL,IAAS,GACvCtY,EAAUyY,EAAMC,SAASJ,GAExBH,GAAcnY,IAAa6X,GAAY7X,KAO5CqC,OAAOuW,OAAO5Y,EAAQqD,MAAOA,GAC7BhB,OAAOC,KAAKuK,GAAYtK,SAAQ,SAAU+V,GACxC,IAAI5V,EAAQmK,EAAWyL,IAET,IAAV5V,EACF1C,EAAQ2M,gBAAgB2L,GAExBtY,EAAQkM,aAAaoM,GAAgB,IAAV5V,EAAiB,GAAKA,WA0DvDmW,OApDF,SAAgBvK,GACd,IAAImK,EAAQnK,EAAMmK,MACdK,EAAgB,CAClBC,OAAQ,CACNrL,SAAU+K,EAAMO,QAAQC,SACxBzL,KAAM,IACNF,IAAK,IACL4L,OAAQ,KAEVC,MAAO,CACLzL,SAAU,YAEZ0L,UAAW,IAQb,OANA/W,OAAOuW,OAAOH,EAAMC,SAASK,OAAO1V,MAAOyV,EAAcC,QAErDN,EAAMC,SAASS,OACjB9W,OAAOuW,OAAOH,EAAMC,SAASS,MAAM9V,MAAOyV,EAAcK,OAGnD,WACL9W,OAAOC,KAAKmW,EAAMC,UAAUnW,SAAQ,SAAU+V,GAC5C,IAAItY,EAAUyY,EAAMC,SAASJ,GACzBzL,EAAa4L,EAAM5L,WAAWyL,IAAS,GAGvCjV,EAFkBhB,OAAOC,KAAKmW,EAAME,OAAOU,eAAef,GAAQG,EAAME,OAAOL,GAAQQ,EAAcR,IAE7Ed,QAAO,SAAUnU,EAAOb,GAElD,OADAa,EAAMb,GAAY,GACXa,IACN,IAEE8U,GAAcnY,IAAa6X,GAAY7X,KAO5CqC,OAAOuW,OAAO5Y,EAAQqD,MAAOA,GAC7BhB,OAAOC,KAAKuK,GAAYtK,SAAQ,SAAU+W,GACxCtZ,EAAQ2M,gBAAgB2M,YAa9BC,SAAU,CAAC,kBCnFE,SAASC,GAAiB9B,GACvC,OAAOA,EAAUxW,MAAM,KAAK,GCAf,SAASuY,GAAczZ,GACpC,MAAO,CACL0Z,EAAG1Z,EAAQ4N,WACX+L,EAAG3Z,EAAQ2N,UACXiM,MAAO5Z,EAAQ6Z,YACfC,OAAQ9Z,EAAQ6D,cCNL,SAASyH,GAAS8J,EAAQ7G,GACvC,IJkBoByJ,EIlBhB+B,EAAWxL,EAAMyL,aAAezL,EAAMyL,cAE1C,GAAI5E,EAAO9J,SAASiD,GAClB,OAAO,EAEJ,GAAIwL,KJaW/B,EIbc+B,aJcjBhC,GAAUC,GAAMiC,YACIjC,aAAgBiC,YIfR,CACzC,IAAIjL,EAAOT,EAEX,EAAG,CACD,GAAIS,GAAQoG,EAAO8E,WAAWlL,GAC5B,OAAO,EAITA,EAAOA,EAAK1L,YAAc0L,EAAKmL,WACxBnL,GAIb,OAAO,ECpBM,SAASrO,GAAiBX,GACvC,OAAO+X,GAAU/X,GAASW,iBAAiBX,GCD9B,SAASoa,GAAepa,GACrC,MAAO,CAAC,QAAS,KAAM,MAAMmT,QAAQ0E,GAAY7X,KAAa,ECDjD,SAASqa,GAAmBra,GAEzC,QAASsB,GAAUtB,GAAWA,EAAQiY,cAAgBjY,EAAQH,WAAaa,OAAOb,UAAUyE,gBCD/E,SAASgW,GAActa,GACpC,MAA6B,SAAzB6X,GAAY7X,GACPA,EAIPA,EAAQua,cACRva,EAAQsD,YAERtD,EAAQma,MAERE,GAAmBra,GCLvB,SAASwa,GAAoBxa,GAC3B,IAAKmY,GAAcnY,IACoB,UAAvCW,GAAiBX,GAAS0N,SACxB,OAAO,KAGT,IAAI+M,EAAeza,EAAQya,aAE3B,GAAIA,EAAc,CAChB,IAAIC,EAAOL,GAAmBI,GAE9B,GAAkC,SAA9B5C,GAAY4C,IAAwE,WAA5C9Z,GAAiB8Z,GAAc/M,UAA6D,WAApC/M,GAAiB+Z,GAAMhN,SACzH,OAAOgN,EAIX,OAAOD,EAwBM,SAASE,GAAgB3a,GAItC,IAHA,IAAIU,EAASqX,GAAU/X,GACnBya,EAAeD,GAAoBxa,GAEhCya,GAAgBL,GAAeK,IAA6D,WAA5C9Z,GAAiB8Z,GAAc/M,UACpF+M,EAAeD,GAAoBC,GAGrC,OAAIA,GAA8C,SAA9B5C,GAAY4C,IAAwE,WAA5C9Z,GAAiB8Z,GAAc/M,SAClFhN,EAGF+Z,GA/BT,SAA4Bza,GAG1B,IAFA,IAAI4a,EAAcN,GAActa,GAEzBmY,GAAcyC,IAAgB,CAAC,OAAQ,QAAQzH,QAAQ0E,GAAY+C,IAAgB,GAAG,CAC3F,IAAIC,EAAMla,GAAiBia,GAG3B,GAAsB,SAAlBC,EAAIC,WAA4C,SAApBD,EAAIE,aAA0BF,EAAIG,YAAiC,SAAnBH,EAAIG,WAClF,OAAOJ,EAEPA,EAAcA,EAAYtX,WAI9B,OAAO,KAiBgB2X,CAAmBjb,IAAYU,EC5DzC,SAASwa,GAAyBxD,GAC/C,MAAO,CAAC,MAAO,UAAUvE,QAAQuE,IAAc,EAAI,IAAM,ICD5C,SAASyD,GAAOC,EAAK1Y,EAAO2Y,GACzC,OAAO3b,KAAK2b,IAAID,EAAK1b,KAAK0b,IAAI1Y,EAAO2Y,ICAxB,SAASC,GAAmBC,GACzC,OAAOlZ,OAAOuW,OAAOvW,OAAOuW,OAAO,GCD5B,CACLtL,IAAK,EACL+J,MAAO,EACPD,OAAQ,EACR5J,KAAM,IDHsD+N,GEFjD,SAASC,GAAgB9Y,EAAOJ,GAC7C,OAAOA,EAAKkV,QAAO,SAAUiE,EAAS/W,GAEpC,OADA+W,EAAQ/W,GAAOhC,EACR+Y,IACN,ICsFL,IAAAC,GAAe,CACbpD,KAAM,QACNC,SAAS,EACTC,MAAO,OACPlR,GAnFF,SAAe0G,GACb,IAAI2N,EAEAlD,EAAQzK,EAAKyK,MACbH,EAAOtK,EAAKsK,KACZsD,EAAenD,EAAMC,SAASS,MAC9B0C,EAAgBpD,EAAMqD,cAAcD,cACpCE,EAAgBvC,GAAiBf,EAAMf,WACvCsE,EAAOd,GAAyBa,GAEhC1V,EADa,CAACmH,GAAM6J,IAAOlE,QAAQ4I,IAAkB,EAClC,SAAW,QAElC,GAAKH,GAAiBC,EAAtB,CAIA,IAAIN,EAAgB9C,EAAMqD,cAAcxD,EAAO,eAAe2D,QAC1DC,EAAYzC,GAAcmC,GAC1BO,EAAmB,MAATH,EAAe1O,GAAME,GAC/B4O,EAAmB,MAATJ,EAAe5E,GAASC,GAClCgF,EAAU5D,EAAM6D,MAAMlD,UAAU/S,GAAOoS,EAAM6D,MAAMlD,UAAU4C,GAAQH,EAAcG,GAAQvD,EAAM6D,MAAMvD,OAAO1S,GAC9GkW,EAAYV,EAAcG,GAAQvD,EAAM6D,MAAMlD,UAAU4C,GACxDQ,EAAoB7B,GAAgBiB,GACpCa,EAAaD,EAA6B,MAATR,EAAeQ,EAAkBE,cAAgB,EAAIF,EAAkBG,aAAe,EAAI,EAC3HC,EAAoBP,EAAU,EAAIE,EAAY,EAG9CnB,EAAMG,EAAcY,GACpBd,EAAMoB,EAAaP,EAAU7V,GAAOkV,EAAca,GAClDS,EAASJ,EAAa,EAAIP,EAAU7V,GAAO,EAAIuW,EAC/CzP,EAASgO,GAAOC,EAAKyB,EAAQxB,GAE7ByB,EAAWd,EACfvD,EAAMqD,cAAcxD,KAASqD,EAAwB,IAA0BmB,GAAY3P,EAAQwO,EAAsBoB,aAAe5P,EAAS0P,EAAQlB,KAmDzJ9C,OAhDF,SAAgBvK,GACd,IAAImK,EAAQnK,EAAMmK,MACdO,EAAU1K,EAAM0K,QAChBV,EAAOhK,EAAMgK,KACb0E,EAAmBhE,EAAQhZ,QAC3B4b,OAAoC,IAArBoB,EAA8B,sBAAwBA,EACrEC,EAAmBjE,EAAQiD,QAC3BA,OAA+B,IAArBgB,EAA8B,EAAIA,EAE5B,MAAhBrB,IAKwB,iBAAjBA,IACTA,EAAenD,EAAMC,SAASK,OAAOzY,cAAcsb,MAahDtQ,GAASmN,EAAMC,SAASK,OAAQ6C,KAQrCnD,EAAMC,SAASS,MAAQyC,EACvBnD,EAAMqD,cAAcxD,EAAO,eAAiB,CAC1C2D,QAASX,GAAsC,iBAAZW,EAAuBA,EAAUT,GAAgBS,EAAS3E,QAW/FiC,SAAU,CAAC,iBACX2D,iBAAkB,CAAC,oBC1FjBC,GAAa,CACf7P,IAAK,OACL+J,MAAO,OACPD,OAAQ,OACR5J,KAAM,QAgBD,SAAS4P,GAAY9O,GAC1B,IAAI+O,EAEAtE,EAASzK,EAAMyK,OACfuE,EAAahP,EAAMgP,WACnB5F,EAAYpJ,EAAMoJ,UAClB6F,EAAUjP,EAAMiP,QAChB7P,EAAWY,EAAMZ,SACjB8P,EAAkBlP,EAAMkP,gBACxBC,EAAWnP,EAAMmP,SAEjBC,EAtBN,SAAsB1P,GACpB,IAAI0L,EAAI1L,EAAK0L,EACTC,EAAI3L,EAAK2L,EAETgE,EADMjd,OACIkd,kBAAoB,EAClC,MAAO,CACLlE,EAAGha,KAAKme,MAAMnE,EAAIiE,GAAOA,GAAO,EAChChE,EAAGja,KAAKme,MAAMlE,EAAIgE,GAAOA,GAAO,GAedG,CAAaP,GAC7B7D,EAAIgE,EAAchE,EAClBC,EAAI+D,EAAc/D,EAElBoE,EAAOR,EAAQlE,eAAe,KAC9B2E,EAAOT,EAAQlE,eAAe,KAC9B4E,EAAQzQ,GACR0Q,EAAQ5Q,GACR6Q,EAAMzd,OAEV,GAAI+c,EAAU,CACZ,IAAIhD,EAAeE,GAAgB5B,GAE/B0B,IAAiB1C,GAAUgB,KAC7B0B,EAAeJ,GAAmBtB,IAMhCrB,IAAcpK,KAChB4Q,EAAQ9G,GACRuC,GAAKc,EAAaiC,aAAeY,EAAWxD,OAC5CH,GAAK6D,EAAkB,GAAK,GAG1B9F,IAAclK,KAChByQ,EAAQ5G,GACRqC,GAAKe,EAAakC,YAAcW,EAAW1D,MAC3CF,GAAK8D,EAAkB,GAAK,GAIhC,IAKMY,EALFC,EAAehc,OAAOuW,OAAO,CAC/BlL,SAAUA,GACT+P,GAAYN,IAEf,OAAIK,EAGKnb,OAAOuW,OAAOvW,OAAOuW,OAAO,GAAIyF,GAAe,KAAKD,EAAiB,IAAmBF,GAASF,EAAO,IAAM,GAAII,EAAeH,GAASF,EAAO,IAAM,GAAIK,EAAetD,WAAaqD,EAAIP,kBAAoB,GAAK,EAAI,aAAelE,EAAI,OAASC,EAAI,MAAQ,eAAiBD,EAAI,OAASC,EAAI,SAAUyE,IAG9S/b,OAAOuW,OAAOvW,OAAOuW,OAAO,GAAIyF,GAAe,KAAKhB,EAAkB,IAAoBa,GAASF,EAAOrE,EAAI,KAAO,GAAI0D,EAAgBY,GAASF,EAAOrE,EAAI,KAAO,GAAI2D,EAAgBvC,UAAY,GAAIuC,IAkDjN,IAAAiB,GAAe,CACbhG,KAAM,gBACNC,SAAS,EACTC,MAAO,cACPlR,GAnDF,SAAuBiX,GACrB,IAAI9F,EAAQ8F,EAAM9F,MACdO,EAAUuF,EAAMvF,QAChBwF,EAAwBxF,EAAQwE,gBAChCA,OAA4C,IAA1BgB,GAA0CA,EAC5DC,EAAoBzF,EAAQyE,SAC5BA,OAAiC,IAAtBgB,GAAsCA,EAYjDJ,EAAe,CACjB3G,UAAW8B,GAAiBf,EAAMf,WAClCqB,OAAQN,EAAMC,SAASK,OACvBuE,WAAY7E,EAAM6D,MAAMvD,OACxByE,gBAAiBA,GAGsB,MAArC/E,EAAMqD,cAAcD,gBACtBpD,EAAME,OAAOI,OAAS1W,OAAOuW,OAAOvW,OAAOuW,OAAO,GAAIH,EAAME,OAAOI,QAASqE,GAAY/a,OAAOuW,OAAOvW,OAAOuW,OAAO,GAAIyF,GAAe,GAAI,CACzId,QAAS9E,EAAMqD,cAAcD,cAC7BnO,SAAU+K,EAAMO,QAAQC,SACxBwE,SAAUA,OAImB,MAA7BhF,EAAMqD,cAAc3C,QACtBV,EAAME,OAAOQ,MAAQ9W,OAAOuW,OAAOvW,OAAOuW,OAAO,GAAIH,EAAME,OAAOQ,OAAQiE,GAAY/a,OAAOuW,OAAOvW,OAAOuW,OAAO,GAAIyF,GAAe,GAAI,CACvId,QAAS9E,EAAMqD,cAAc3C,MAC7BzL,SAAU,WACV+P,UAAU,OAIdhF,EAAM5L,WAAWkM,OAAS1W,OAAOuW,OAAOvW,OAAOuW,OAAO,GAAIH,EAAM5L,WAAWkM,QAAS,GAAI,CACtF2F,wBAAyBjG,EAAMf,aAUjC/S,KAAM,ICtIJga,GAAU,CACZA,SAAS,GAsCX,IAAAC,GAAe,CACbtG,KAAM,iBACNC,SAAS,EACTC,MAAO,QACPlR,GAAI,aACJuR,OAxCF,SAAgB7K,GACd,IAAIyK,EAAQzK,EAAKyK,MACbxT,EAAW+I,EAAK/I,SAChB+T,EAAUhL,EAAKgL,QACf6F,EAAkB7F,EAAQ8F,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAkB/F,EAAQgG,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7Cre,EAASqX,GAAUU,EAAMC,SAASK,QAClCkG,EAAgB,GAAGhR,OAAOwK,EAAMwG,cAAc7F,UAAWX,EAAMwG,cAAclG,QAYjF,OAVI+F,GACFG,EAAc1c,SAAQ,SAAU2c,GAC9BA,EAAard,iBAAiB,SAAUoD,EAASka,OAAQR,OAIzDK,GACFte,EAAOmB,iBAAiB,SAAUoD,EAASka,OAAQR,IAG9C,WACDG,GACFG,EAAc1c,SAAQ,SAAU2c,GAC9BA,EAAand,oBAAoB,SAAUkD,EAASka,OAAQR,OAI5DK,GACFte,EAAOqB,oBAAoB,SAAUkD,EAASka,OAAQR,MAY1Dha,KAAM,IC/CJya,GAAO,CACT5R,KAAM,QACN6J,MAAO,OACPD,OAAQ,MACR9J,IAAK,UAEQ,SAAS+R,GAAqB3H,GAC3C,OAAOA,EAAU5Q,QAAQ,0BAA0B,SAAUwY,GAC3D,OAAOF,GAAKE,MCRhB,IAAIF,GAAO,CACT5M,MAAO,MACPI,IAAK,SAEQ,SAAS2M,GAA8B7H,GACpD,OAAOA,EAAU5Q,QAAQ,cAAc,SAAUwY,GAC/C,OAAOF,GAAKE,MCND,SAASjS,GAAsBrN,GAC5C,IAAIoN,EAAOpN,EAAQqN,wBACnB,MAAO,CACLuM,MAAOxM,EAAKwM,MACZE,OAAQ1M,EAAK0M,OACbxM,IAAKF,EAAKE,IACV+J,MAAOjK,EAAKiK,MACZD,OAAQhK,EAAKgK,OACb5J,KAAMJ,EAAKI,KACXkM,EAAGtM,EAAKI,KACRmM,EAAGvM,EAAKE,KCTG,SAASkS,GAAgBxH,GACtC,IAAImG,EAAMpG,GAAUC,GAGpB,MAAO,CACLvK,WAHe0Q,EAAIsB,YAInBlS,UAHc4Q,EAAIuB,aCDP,SAASC,GAAoB3f,GAQ1C,OAAOqN,GAAsBgN,GAAmBra,IAAUwN,KAAOgS,GAAgBxf,GAASyN,WCV7E,SAASmS,GAAe5f,GAErC,IAAI6f,EAAoBlf,GAAiBX,GACrC8f,EAAWD,EAAkBC,SAC7BC,EAAYF,EAAkBE,UAC9BC,EAAYH,EAAkBG,UAElC,MAAO,6BAA6B/c,KAAK6c,EAAWE,EAAYD,GCInD,SAASE,GAAkBjgB,EAASkgB,QACpC,IAATA,IACFA,EAAO,IAGT,IAAIhB,ECbS,SAASiB,EAAgBnI,GACtC,MAAI,CAAC,OAAQ,OAAQ,aAAa7E,QAAQ0E,GAAYG,KAAU,EAEvDA,EAAKC,cAAcjU,KAGxBmU,GAAcH,IAAS4H,GAAe5H,GACjCA,EAGFmI,EAAgB7F,GAActC,IDGlBmI,CAAgBngB,GAC/BogB,EAAuC,SAA9BvI,GAAYqH,GACrBf,EAAMpG,GAAUmH,GAChBzX,EAAS2Y,EAAS,CAACjC,GAAKlQ,OAAOkQ,EAAIkC,gBAAkB,GAAIT,GAAeV,GAAgBA,EAAe,IAAMA,EAC7GoB,EAAcJ,EAAKjS,OAAOxG,GAC9B,OAAO2Y,EAASE,EAChBA,EAAYrS,OAAOgS,GAAkB3F,GAAc7S,KEvBtC,SAAS8Y,GAAiBnT,GACvC,OAAO/K,OAAOuW,OAAOvW,OAAOuW,OAAO,GAAIxL,GAAO,GAAI,CAChDI,KAAMJ,EAAKsM,EACXpM,IAAKF,EAAKuM,EACVtC,MAAOjK,EAAKsM,EAAItM,EAAKwM,MACrBxC,OAAQhK,EAAKuM,EAAIvM,EAAK0M,SCsB1B,SAAS0G,GAA2BxgB,EAASygB,GAC3C,M9BnBoB,a8BmBbA,EAA8BF,GCzBxB,SAAyBvgB,GACtC,IAAIme,EAAMpG,GAAU/X,GAChB0a,EAAOL,GAAmBra,GAC1BqgB,EAAiBlC,EAAIkC,eACrBzG,EAAQc,EAAKiC,YACb7C,EAASY,EAAKgC,aACdhD,EAAI,EACJC,EAAI,EAuBR,OAjBI0G,IACFzG,EAAQyG,EAAezG,MACvBE,EAASuG,EAAevG,OASnB,iCAAiC7W,KAAK2N,UAAU8P,aACnDhH,EAAI2G,EAAezS,WACnB+L,EAAI0G,EAAe1S,YAIhB,CACLiM,MAAOA,EACPE,OAAQA,EACRJ,EAAGA,EAAIiG,GAAoB3f,GAC3B2Z,EAAGA,GDTiDgH,CAAgB3gB,IAAYmY,GAAcsI,GAdlG,SAAoCzgB,GAClC,IAAIoN,EAAOC,GAAsBrN,GASjC,OARAoN,EAAKE,IAAMF,EAAKE,IAAMtN,EAAQ4gB,UAC9BxT,EAAKI,KAAOJ,EAAKI,KAAOxN,EAAQ6gB,WAChCzT,EAAKgK,OAAShK,EAAKE,IAAMtN,EAAQ0c,aACjCtP,EAAKiK,MAAQjK,EAAKI,KAAOxN,EAAQ2c,YACjCvP,EAAKwM,MAAQ5Z,EAAQ2c,YACrBvP,EAAK0M,OAAS9Z,EAAQ0c,aACtBtP,EAAKsM,EAAItM,EAAKI,KACdJ,EAAKuM,EAAIvM,EAAKE,IACPF,EAI2G0T,CAA2BL,GAAkBF,GEtBlJ,SAAyBvgB,GACtC,IAAI0a,EAAOL,GAAmBra,GAC1B+gB,EAAYvB,GAAgBxf,GAC5BgE,EAAOhE,EAAQiY,cAAcjU,KAC7B4V,EAAQla,KAAK2b,IAAIX,EAAKsG,YAAatG,EAAKiC,YAAa3Y,EAAOA,EAAKgd,YAAc,EAAGhd,EAAOA,EAAK2Y,YAAc,GAC5G7C,EAASpa,KAAK2b,IAAIX,EAAKuG,aAAcvG,EAAKgC,aAAc1Y,EAAOA,EAAKid,aAAe,EAAGjd,EAAOA,EAAK0Y,aAAe,GACjHhD,GAAKqH,EAAUtT,WAAakS,GAAoB3f,GAChD2Z,GAAKoH,EAAUxT,UAMnB,MAJiD,QAA7C5M,GAAiBqD,GAAQ0W,GAAM3I,YACjC2H,GAAKha,KAAK2b,IAAIX,EAAKiC,YAAa3Y,EAAOA,EAAK2Y,YAAc,GAAK/C,GAG1D,CACLA,MAAOA,EACPE,OAAQA,EACRJ,EAAGA,EACHC,EAAGA,GFK2KuH,CAAgB7G,GAAmBra,KAuBtM,SAASmhB,GAAgBnhB,EAASohB,EAAUC,GACzD,IAAIC,EAAmC,oBAAbF,EAlB5B,SAA4BphB,GAC1B,IAAIuhB,EAAkBtB,GAAkB3F,GAActa,IAElDwhB,EADoB,CAAC,WAAY,SAASrO,QAAQxS,GAAiBX,GAAS0N,WAAa,GACnDyK,GAAcnY,GAAW2a,GAAgB3a,GAAWA,EAE9F,OAAKsB,GAAUkgB,GAKRD,EAAgBxU,QAAO,SAAU0T,GACtC,OAAOnf,GAAUmf,IAAmBnV,GAASmV,EAAgBe,IAAmD,SAAhC3J,GAAY4I,MALrF,GAYkDgB,CAAmBzhB,GAAW,GAAGiO,OAAOmT,GAC/FG,EAAkB,GAAGtT,OAAOqT,EAAqB,CAACD,IAClDK,EAAsBH,EAAgB,GACtCI,EAAeJ,EAAgB/J,QAAO,SAAUoK,EAASnB,GAC3D,IAAIrT,EAAOoT,GAA2BxgB,EAASygB,GAK/C,OAJAmB,EAAQtU,IAAM5N,KAAK2b,IAAIjO,EAAKE,IAAKsU,EAAQtU,KACzCsU,EAAQvK,MAAQ3X,KAAK0b,IAAIhO,EAAKiK,MAAOuK,EAAQvK,OAC7CuK,EAAQxK,OAAS1X,KAAK0b,IAAIhO,EAAKgK,OAAQwK,EAAQxK,QAC/CwK,EAAQpU,KAAO9N,KAAK2b,IAAIjO,EAAKI,KAAMoU,EAAQpU,MACpCoU,IACNpB,GAA2BxgB,EAAS0hB,IAKvC,OAJAC,EAAa/H,MAAQ+H,EAAatK,MAAQsK,EAAanU,KACvDmU,EAAa7H,OAAS6H,EAAavK,OAASuK,EAAarU,IACzDqU,EAAajI,EAAIiI,EAAanU,KAC9BmU,EAAahI,EAAIgI,EAAarU,IACvBqU,EGnEM,SAASE,GAAanK,GACnC,OAAOA,EAAUxW,MAAM,KAAK,GCGf,SAAS4gB,GAAe9T,GACrC,IAOIuP,EAPAnE,EAAYpL,EAAKoL,UACjBpZ,EAAUgO,EAAKhO,QACf0X,EAAY1J,EAAK0J,UACjBqE,EAAgBrE,EAAY8B,GAAiB9B,GAAa,KAC1DqK,EAAYrK,EAAYmK,GAAanK,GAAa,KAClDsK,EAAU5I,EAAUM,EAAIN,EAAUQ,MAAQ,EAAI5Z,EAAQ4Z,MAAQ,EAC9DqI,EAAU7I,EAAUO,EAAIP,EAAUU,OAAS,EAAI9Z,EAAQ8Z,OAAS,EAGpE,OAAQiC,GACN,KAAKzO,GACHiQ,EAAU,CACR7D,EAAGsI,EACHrI,EAAGP,EAAUO,EAAI3Z,EAAQ8Z,QAE3B,MAEF,KAAK1C,GACHmG,EAAU,CACR7D,EAAGsI,EACHrI,EAAGP,EAAUO,EAAIP,EAAUU,QAE7B,MAEF,KAAKzC,GACHkG,EAAU,CACR7D,EAAGN,EAAUM,EAAIN,EAAUQ,MAC3BD,EAAGsI,GAEL,MAEF,KAAKzU,GACH+P,EAAU,CACR7D,EAAGN,EAAUM,EAAI1Z,EAAQ4Z,MACzBD,EAAGsI,GAEL,MAEF,QACE1E,EAAU,CACR7D,EAAGN,EAAUM,EACbC,EAAGP,EAAUO,GAInB,IAAIuI,EAAWnG,EAAgBb,GAAyBa,GAAiB,KAEzE,GAAgB,MAAZmG,EAAkB,CACpB,IAAI7b,EAAmB,MAAb6b,EAAmB,SAAW,QAExC,OAAQH,GACN,IlClDa,QkCmDXxE,EAAQ2E,GAAYxiB,KAAKC,MAAM4d,EAAQ2E,IAAaxiB,KAAKC,MAAMyZ,EAAU/S,GAAO,EAAIrG,EAAQqG,GAAO,GACnG,MAEF,IlCrDW,MkCsDTkX,EAAQ2E,GAAYxiB,KAAKC,MAAM4d,EAAQ2E,IAAaxiB,KAAKyiB,KAAK/I,EAAU/S,GAAO,EAAIrG,EAAQqG,GAAO,IAOxG,OAAOkX,EC1DM,SAAS6E,GAAe3J,EAAOO,QAC5B,IAAZA,IACFA,EAAU,IAGZ,IAAIqJ,EAAWrJ,EACXsJ,EAAqBD,EAAS3K,UAC9BA,OAAmC,IAAvB4K,EAAgC7J,EAAMf,UAAY4K,EAC9DC,EAAoBF,EAASjB,SAC7BA,OAAiC,IAAtBmB,EnCXY,kBmCWqCA,EAC5DC,EAAwBH,EAAShB,aACjCA,OAAyC,IAA1BmB,EnCZC,WmCY6CA,EAC7DC,EAAwBJ,EAASK,eACjCA,OAA2C,IAA1BD,EnCbH,SmCa+CA,EAC7DE,EAAuBN,EAASO,YAChCA,OAAuC,IAAzBD,GAA0CA,EACxD1F,EAAmBoF,EAASpG,QAC5BA,OAA+B,IAArBgB,EAA8B,EAAIA,EAC5C1B,EAAgBD,GAAsC,iBAAZW,EAAuBA,EAAUT,GAAgBS,EAAS3E,KACpGuL,EnCnBc,WmCmBDH,EnClBI,YADH,SmCoBdI,EAAmBrK,EAAMC,SAASU,UAClCkE,EAAa7E,EAAM6D,MAAMvD,OACzB/Y,EAAUyY,EAAMC,SAASkK,EAAcC,EAAaH,GACpDK,EAAqB5B,GAAgB7f,GAAUtB,GAAWA,EAAUA,EAAQgjB,gBAAkB3I,GAAmB5B,EAAMC,SAASK,QAASqI,EAAUC,GACnJ4B,EAAsB5V,GAAsByV,GAC5CjH,EAAgBiG,GAAe,CACjC1I,UAAW6J,EACXjjB,QAASsd,EACTrE,SAAU,WACVvB,UAAWA,IAETwL,EAAmB3C,GAAiBle,OAAOuW,OAAOvW,OAAOuW,OAAO,GAAI0E,GAAazB,IACjFsH,EnChCc,WmCgCMT,EAA4BQ,EAAmBD,EAGnEG,EAAkB,CACpB9V,IAAKyV,EAAmBzV,IAAM6V,EAAkB7V,IAAMiO,EAAcjO,IACpE8J,OAAQ+L,EAAkB/L,OAAS2L,EAAmB3L,OAASmE,EAAcnE,OAC7E5J,KAAMuV,EAAmBvV,KAAO2V,EAAkB3V,KAAO+N,EAAc/N,KACvE6J,MAAO8L,EAAkB9L,MAAQ0L,EAAmB1L,MAAQkE,EAAclE,OAExEgM,EAAa5K,EAAMqD,cAAc3O,OAErC,GnC3CkB,WmC2CduV,GAA6BW,EAAY,CAC3C,IAAIlW,EAASkW,EAAW3L,GACxBrV,OAAOC,KAAK8gB,GAAiB7gB,SAAQ,SAAUmC,GAC7C,IAAI4e,EAAW,CAACjM,GAAOD,IAAQjE,QAAQzO,IAAQ,EAAI,GAAK,EACpDsX,EAAO,CAAC1O,GAAK8J,IAAQjE,QAAQzO,IAAQ,EAAI,IAAM,IACnD0e,EAAgB1e,IAAQyI,EAAO6O,GAAQsH,KAI3C,OAAOF,ECtDM,SAASG,GAAqB9K,EAAOO,QAClC,IAAZA,IACFA,EAAU,IAGZ,IAAIqJ,EAAWrJ,EACXtB,EAAY2K,EAAS3K,UACrB0J,EAAWiB,EAASjB,SACpBC,EAAegB,EAAShB,aACxBpF,EAAUoG,EAASpG,QACnBuH,EAAiBnB,EAASmB,eAC1BC,EAAwBpB,EAASqB,sBACjCA,OAAkD,IAA1BD,EAAmCE,GAAgBF,EAC3E1B,EAAYF,GAAanK,GACzBC,EAAaoK,EAAYyB,EAAiBjM,GAAsBA,GAAoBxK,QAAO,SAAU2K,GACvG,OAAOmK,GAAanK,KAAeqK,KAChCzK,GAEDsM,EAAoBjM,EAAW5K,QAAO,SAAU2K,GAClD,OAAOgM,EAAsBvQ,QAAQuE,IAAc,KAGpB,IAA7BkM,EAAkBtd,SACpBsd,EAAoBjM,GAQtB,IAAIkM,EAAYD,EAAkBpM,QAAO,SAAUC,EAAKC,GAOtD,OANAD,EAAIC,GAAa0K,GAAe3J,EAAO,CACrCf,UAAWA,EACX0J,SAAUA,EACVC,aAAcA,EACdpF,QAASA,IACRzC,GAAiB9B,IACbD,IACN,IACH,OAAOpV,OAAOC,KAAKuhB,GAAWC,MAAK,SAAUC,EAAGC,GAC9C,OAAOH,EAAUE,GAAKF,EAAUG,MCwFpC,IAAAC,GAAe,CACb3L,KAAM,OACNC,SAAS,EACTC,MAAO,OACPlR,GA5HF,SAAc0G,GACZ,IAAIyK,EAAQzK,EAAKyK,MACbO,EAAUhL,EAAKgL,QACfV,EAAOtK,EAAKsK,KAEhB,IAAIG,EAAMqD,cAAcxD,GAAM4L,MAA9B,CAoCA,IAhCA,IAAIC,EAAoBnL,EAAQkJ,SAC5BkC,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmBrL,EAAQsL,QAC3BC,OAAoC,IAArBF,GAAqCA,EACpDG,EAA8BxL,EAAQyL,mBACtCxI,EAAUjD,EAAQiD,QAClBmF,EAAWpI,EAAQoI,SACnBC,EAAerI,EAAQqI,aACvBuB,EAAc5J,EAAQ4J,YACtB8B,EAAwB1L,EAAQwK,eAChCA,OAA2C,IAA1BkB,GAA0CA,EAC3DhB,EAAwB1K,EAAQ0K,sBAChCiB,EAAqBlM,EAAMO,QAAQtB,UACnCqE,EAAgBvC,GAAiBmL,GAEjCF,EAAqBD,IADHzI,IAAkB4I,IACqCnB,EAAiB,CAACnE,GAAqBsF,IAjCtH,SAAuCjN,GACrC,GrCLgB,SqCKZ8B,GAAiB9B,GACnB,MAAO,GAGT,IAAIkN,EAAoBvF,GAAqB3H,GAC7C,MAAO,CAAC6H,GAA8B7H,GAAYkN,EAAmBrF,GAA8BqF,IA2BwCC,CAA8BF,IACrKhN,EAAa,CAACgN,GAAoB1W,OAAOwW,GAAoBjN,QAAO,SAAUC,EAAKC,GACrF,OAAOD,EAAIxJ,OrCvCG,SqCuCIuL,GAAiB9B,GAAsB6L,GAAqB9K,EAAO,CACnFf,UAAWA,EACX0J,SAAUA,EACVC,aAAcA,EACdpF,QAASA,EACTuH,eAAgBA,EAChBE,sBAAuBA,IACpBhM,KACJ,IACCoN,EAAgBrM,EAAM6D,MAAMlD,UAC5BkE,EAAa7E,EAAM6D,MAAMvD,OACzBgM,EAAY,IAAIC,IAChBC,GAAqB,EACrBC,EAAwBvN,EAAW,GAE9BvR,EAAI,EAAGA,EAAIuR,EAAWrR,OAAQF,IAAK,CAC1C,IAAIsR,EAAYC,EAAWvR,GAEvB+e,EAAiB3L,GAAiB9B,GAElC0N,ErCzDW,UqCyDQvD,GAAanK,GAChC2N,EAAa,CAAC/X,GAAK8J,IAAQjE,QAAQgS,IAAmB,EACtD9e,EAAMgf,EAAa,QAAU,SAC7BvF,EAAWsC,GAAe3J,EAAO,CACnCf,UAAWA,EACX0J,SAAUA,EACVC,aAAcA,EACduB,YAAaA,EACb3G,QAASA,IAEPqJ,EAAoBD,EAAaD,EAAmB/N,GAAQ7J,GAAO4X,EAAmBhO,GAAS9J,GAE/FwX,EAAcze,GAAOiX,EAAWjX,KAClCif,EAAoBjG,GAAqBiG,IAG3C,IAAIC,EAAmBlG,GAAqBiG,GACxCE,EAAS,GAUb,GARIpB,GACFoB,EAAO5W,KAAKkR,EAASqF,IAAmB,GAGtCZ,GACFiB,EAAO5W,KAAKkR,EAASwF,IAAsB,EAAGxF,EAASyF,IAAqB,GAG1EC,EAAOC,OAAM,SAAUC,GACzB,OAAOA,KACL,CACFR,EAAwBxN,EACxBuN,GAAqB,EACrB,MAGFF,EAAUtgB,IAAIiT,EAAW8N,GAG3B,GAAIP,EAqBF,IAnBA,IAEIU,EAAQ,SAAeC,GACzB,IAAIC,EAAmBlO,EAAW5J,MAAK,SAAU2J,GAC/C,IAAI8N,EAAST,EAAUlgB,IAAI6S,GAE3B,GAAI8N,EACF,OAAOA,EAAOxc,MAAM,EAAG4c,GAAIH,OAAM,SAAUC,GACzC,OAAOA,QAKb,GAAIG,EAEF,OADAX,EAAwBW,EACjB,SAIFD,EAnBYpC,EAAiB,EAAI,EAmBZoC,EAAK,EAAGA,IAAM,CAG1C,GAAa,UAFFD,EAAMC,GAEK,MAItBnN,EAAMf,YAAcwN,IACtBzM,EAAMqD,cAAcxD,GAAM4L,OAAQ,EAClCzL,EAAMf,UAAYwN,EAClBzM,EAAMqN,OAAQ,KAUhB5I,iBAAkB,CAAC,UACnBvY,KAAM,CACJuf,OAAO,IC7IX,SAAS6B,GAAejG,EAAU1S,EAAM4Y,GAQtC,YAPyB,IAArBA,IACFA,EAAmB,CACjBtM,EAAG,EACHC,EAAG,IAIA,CACLrM,IAAKwS,EAASxS,IAAMF,EAAK0M,OAASkM,EAAiBrM,EACnDtC,MAAOyI,EAASzI,MAAQjK,EAAKwM,MAAQoM,EAAiBtM,EACtDtC,OAAQ0I,EAAS1I,OAAShK,EAAK0M,OAASkM,EAAiBrM,EACzDnM,KAAMsS,EAAStS,KAAOJ,EAAKwM,MAAQoM,EAAiBtM,GAIxD,SAASuM,GAAsBnG,GAC7B,MAAO,CAACxS,GAAK+J,GAAOD,GAAQ5J,IAAM0Y,MAAK,SAAUC,GAC/C,OAAOrG,EAASqG,IAAS,KAiC7B,IAAAC,GAAe,CACb9N,KAAM,OACNC,SAAS,EACTC,MAAO,OACP0E,iBAAkB,CAAC,mBACnB5V,GAlCF,SAAc0G,GACZ,IAAIyK,EAAQzK,EAAKyK,MACbH,EAAOtK,EAAKsK,KACZwM,EAAgBrM,EAAM6D,MAAMlD,UAC5BkE,EAAa7E,EAAM6D,MAAMvD,OACzBiN,EAAmBvN,EAAMqD,cAAcuK,gBACvCC,EAAoBlE,GAAe3J,EAAO,CAC5CiK,eAAgB,cAEd6D,EAAoBnE,GAAe3J,EAAO,CAC5CmK,aAAa,IAEX4D,EAA2BT,GAAeO,EAAmBxB,GAC7D2B,EAAsBV,GAAeQ,EAAmBjJ,EAAY0I,GACpEU,EAAoBT,GAAsBO,GAC1CG,EAAmBV,GAAsBQ,GAC7ChO,EAAMqD,cAAcxD,GAAQ,CAC1BkO,yBAA0BA,EAC1BC,oBAAqBA,EACrBC,kBAAmBA,EACnBC,iBAAkBA,GAEpBlO,EAAM5L,WAAWkM,OAAS1W,OAAOuW,OAAOvW,OAAOuW,OAAO,GAAIH,EAAM5L,WAAWkM,QAAS,GAAI,CACtF6N,+BAAgCF,EAChCG,sBAAuBF,MCH3B,IAAAG,GAAe,CACbxO,KAAM,SACNC,SAAS,EACTC,MAAO,OACPe,SAAU,CAAC,iBACXjS,GA5BF,SAAgBgH,GACd,IAAImK,EAAQnK,EAAMmK,MACdO,EAAU1K,EAAM0K,QAChBV,EAAOhK,EAAMgK,KACbyO,EAAkB/N,EAAQ7L,OAC1BA,OAA6B,IAApB4Z,EAA6B,CAAC,EAAG,GAAKA,EAC/CpiB,EAAOgT,GAAWH,QAAO,SAAUC,EAAKC,GAE1C,OADAD,EAAIC,GA5BD,SAAiCA,EAAW4E,EAAOnP,GACxD,IAAI4O,EAAgBvC,GAAiB9B,GACjCsP,EAAiB,CAACxZ,GAAMF,IAAK6F,QAAQ4I,IAAkB,GAAK,EAAI,EAEhE/N,EAAyB,mBAAXb,EAAwBA,EAAO9K,OAAOuW,OAAOvW,OAAOuW,OAAO,GAAI0D,GAAQ,GAAI,CAC3F5E,UAAWA,KACPvK,EACF8Z,EAAWjZ,EAAK,GAChBkZ,EAAWlZ,EAAK,GAIpB,OAFAiZ,EAAWA,GAAY,EACvBC,GAAYA,GAAY,GAAKF,EACtB,CAACxZ,GAAM6J,IAAOlE,QAAQ4I,IAAkB,EAAI,CACjDrC,EAAGwN,EACHvN,EAAGsN,GACD,CACFvN,EAAGuN,EACHtN,EAAGuN,GAWcC,CAAwBzP,EAAWe,EAAM6D,MAAOnP,GAC1DsK,IACN,IACC2P,EAAwBziB,EAAK8T,EAAMf,WACnCgC,EAAI0N,EAAsB1N,EAC1BC,EAAIyN,EAAsBzN,EAEW,MAArClB,EAAMqD,cAAcD,gBACtBpD,EAAMqD,cAAcD,cAAcnC,GAAKA,EACvCjB,EAAMqD,cAAcD,cAAclC,GAAKA,GAGzClB,EAAMqD,cAAcxD,GAAQ3T,ICxB9B,IAAA0iB,GAAe,CACb/O,KAAM,gBACNC,SAAS,EACTC,MAAO,OACPlR,GApBF,SAAuB0G,GACrB,IAAIyK,EAAQzK,EAAKyK,MACbH,EAAOtK,EAAKsK,KAKhBG,EAAMqD,cAAcxD,GAAQwJ,GAAe,CACzC1I,UAAWX,EAAM6D,MAAMlD,UACvBpZ,QAASyY,EAAM6D,MAAMvD,OACrBE,SAAU,WACVvB,UAAWe,EAAMf,aAUnB/S,KAAM,ICyFR,IAAA2iB,GAAe,CACbhP,KAAM,kBACNC,SAAS,EACTC,MAAO,OACPlR,GAzGF,SAAyB0G,GACvB,IAAIyK,EAAQzK,EAAKyK,MACbO,EAAUhL,EAAKgL,QACfV,EAAOtK,EAAKsK,KACZ6L,EAAoBnL,EAAQkJ,SAC5BkC,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmBrL,EAAQsL,QAC3BC,OAAoC,IAArBF,GAAsCA,EACrDjD,EAAWpI,EAAQoI,SACnBC,EAAerI,EAAQqI,aACvBuB,EAAc5J,EAAQ4J,YACtB3G,EAAUjD,EAAQiD,QAClBsL,EAAkBvO,EAAQwO,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAwBzO,EAAQ0O,aAChCA,OAAyC,IAA1BD,EAAmC,EAAIA,EACtD3H,EAAWsC,GAAe3J,EAAO,CACnC2I,SAAUA,EACVC,aAAcA,EACdpF,QAASA,EACT2G,YAAaA,IAEX7G,EAAgBvC,GAAiBf,EAAMf,WACvCqK,EAAYF,GAAapJ,EAAMf,WAC/BiQ,GAAmB5F,EACnBG,EAAWhH,GAAyBa,GACpCuI,ECpCY,MDoCSpC,ECpCH,IAAM,IDqCxBrG,EAAgBpD,EAAMqD,cAAcD,cACpCiJ,EAAgBrM,EAAM6D,MAAMlD,UAC5BkE,EAAa7E,EAAM6D,MAAMvD,OACzB6O,EAA4C,mBAAjBF,EAA8BA,EAAarlB,OAAOuW,OAAOvW,OAAOuW,OAAO,GAAIH,EAAM6D,OAAQ,GAAI,CAC1H5E,UAAWe,EAAMf,aACbgQ,EACF/iB,EAAO,CACT+U,EAAG,EACHC,EAAG,GAGL,GAAKkC,EAAL,CAIA,GAAIuI,EAAe,CACjB,IAAIyD,EAAwB,MAAb3F,EAAmB5U,GAAME,GACpCsa,EAAuB,MAAb5F,EAAmB9K,GAASC,GACtChR,EAAmB,MAAb6b,EAAmB,SAAW,QACpC/U,EAAS0O,EAAcqG,GACvB9G,EAAMS,EAAcqG,GAAYpC,EAAS+H,GACzCxM,EAAMQ,EAAcqG,GAAYpC,EAASgI,GACzCC,EAAWP,GAAUlK,EAAWjX,GAAO,EAAI,EAC3C2hB,EzCvDW,UyCuDFjG,EAAsB+C,EAAcze,GAAOiX,EAAWjX,GAC/D4hB,EzCxDW,UyCwDFlG,GAAuBzE,EAAWjX,IAAQye,EAAcze,GAGjEuV,EAAenD,EAAMC,SAASS,MAC9B+C,EAAYsL,GAAU5L,EAAenC,GAAcmC,GAAgB,CACrEhC,MAAO,EACPE,OAAQ,GAENoO,EAAqBzP,EAAMqD,cAAc,oBAAsBrD,EAAMqD,cAAc,oBAAoBG,QzBrEtG,CACL3O,IAAK,EACL+J,MAAO,EACPD,OAAQ,EACR5J,KAAM,GyBkEF2a,EAAkBD,EAAmBL,GACrCO,EAAkBF,EAAmBJ,GAMrCO,EAAWlN,GAAO,EAAG2J,EAAcze,GAAM6V,EAAU7V,IACnDiiB,EAAYX,EAAkB7C,EAAcze,GAAO,EAAI0hB,EAAWM,EAAWF,EAAkBP,EAAoBI,EAASK,EAAWF,EAAkBP,EACzJW,EAAYZ,GAAmB7C,EAAcze,GAAO,EAAI0hB,EAAWM,EAAWD,EAAkBR,EAAoBK,EAASI,EAAWD,EAAkBR,EAC1JpL,EAAoB/D,EAAMC,SAASS,OAASwB,GAAgBlC,EAAMC,SAASS,OAC3EqP,EAAehM,EAAiC,MAAb0F,EAAmB1F,EAAkBoE,WAAa,EAAIpE,EAAkBqE,YAAc,EAAI,EAC7H4H,EAAsBhQ,EAAMqD,cAAc3O,OAASsL,EAAMqD,cAAc3O,OAAOsL,EAAMf,WAAWwK,GAAY,EAC3GwG,EAAY7M,EAAcqG,GAAYoG,EAAYG,EAAsBD,EACxEG,EAAY9M,EAAcqG,GAAYqG,EAAYE,EAClDG,EAAkBzN,GAAOqM,EAAS9nB,KAAK0b,IAAIA,EAAKsN,GAAatN,EAAKjO,EAAQqa,EAAS9nB,KAAK2b,IAAIA,EAAKsN,GAAatN,GAClHQ,EAAcqG,GAAY0G,EAC1BjkB,EAAKud,GAAY0G,EAAkBzb,EAGrC,GAAIoX,EAAc,CAChB,IAAIsE,EAAyB,MAAb3G,EAAmB5U,GAAME,GAErCsb,EAAwB,MAAb5G,EAAmB9K,GAASC,GAEvC0R,EAAUlN,EAAcyI,GAMxB0E,EAAmB7N,GAJZ4N,EAAUjJ,EAAS+I,GAIME,EAFzBA,EAAUjJ,EAASgJ,IAI9BjN,EAAcyI,GAAW0E,EACzBrkB,EAAK2f,GAAW0E,EAAmBD,EAGrCtQ,EAAMqD,cAAcxD,GAAQ3T,IAS5BuY,iBAAkB,CAAC,WE5GN,SAAS+L,GAAiBC,EAAyBzO,EAAc0O,QAC9D,IAAZA,IACFA,GAAU,GAGZ,ICVoCnR,ECJOhY,EFcvCsE,EAAkB+V,GAAmBI,GACrCrN,EAAOC,GAAsB6b,GAC7BE,EAA0BjR,GAAcsC,GACxCqE,EAAS,CACXrR,WAAY,EACZF,UAAW,GAETgQ,EAAU,CACZ7D,EAAG,EACHC,EAAG,GAkBL,OAfIyP,IAA4BA,IAA4BD,MACxB,SAA9BtR,GAAY4C,IAChBmF,GAAetb,MACbwa,GCzBgC9G,EDyBTyC,KCxBd1C,GAAUC,IAAUG,GAAcH,GCJxC,CACLvK,YAFyCzN,EDQbgY,GCNRvK,WACpBF,UAAWvN,EAAQuN,WDGZiS,GAAgBxH,ID0BnBG,GAAcsC,KAChB8C,EAAUlQ,GAAsBoN,IACxBf,GAAKe,EAAaoG,WAC1BtD,EAAQ5D,GAAKc,EAAamG,WACjBtc,IACTiZ,EAAQ7D,EAAIiG,GAAoBrb,KAI7B,CACLoV,EAAGtM,EAAKI,KAAOsR,EAAOrR,WAAa8P,EAAQ7D,EAC3CC,EAAGvM,EAAKE,IAAMwR,EAAOvR,UAAYgQ,EAAQ5D,EACzCC,MAAOxM,EAAKwM,MACZE,OAAQ1M,EAAK0M,QG3CjB,SAASuP,GAAMC,GACb,IAAIC,EAAM,IAAIvE,IACVwE,EAAU,IAAI7jB,IACd8jB,EAAS,GA0Bb,OAzBAH,EAAU/mB,SAAQ,SAAUmnB,GAC1BH,EAAI9kB,IAAIilB,EAASpR,KAAMoR,MAkBzBJ,EAAU/mB,SAAQ,SAAUmnB,GACrBF,EAAQxiB,IAAI0iB,EAASpR,OAhB5B,SAASwL,EAAK4F,GACZF,EAAQxW,IAAI0W,EAASpR,MACN,GAAGrK,OAAOyb,EAASnQ,UAAY,GAAImQ,EAASxM,kBAAoB,IACtE3a,SAAQ,SAAUonB,GACzB,IAAKH,EAAQxiB,IAAI2iB,GAAM,CACrB,IAAIC,EAAcL,EAAI1kB,IAAI8kB,GAEtBC,GACF9F,EAAK8F,OAIXH,EAAO7a,KAAK8a,GAMV5F,CAAK4F,MAGFD,ECfT,IAAII,GAAkB,CACpBnS,UAAW,SACX4R,UAAW,GACXrQ,SAAU,YAGZ,SAAS6Q,KACP,IAAK,IAAIC,EAAOC,UAAU1jB,OAAQ6C,EAAO,IAAI8gB,MAAMF,GAAOG,EAAO,EAAGA,EAAOH,EAAMG,IAC/E/gB,EAAK+gB,GAAQF,UAAUE,GAGzB,OAAQ/gB,EAAK+c,MAAK,SAAUlmB,GAC1B,QAASA,GAAoD,mBAAlCA,EAAQqN,0BAIhC,SAAS8c,GAAgBC,QACL,IAArBA,IACFA,EAAmB,IAGrB,IAAIC,EAAoBD,EACpBE,EAAwBD,EAAkBE,iBAC1CA,OAA6C,IAA1BD,EAAmC,GAAKA,EAC3DE,EAAyBH,EAAkBI,eAC3CA,OAA4C,IAA3BD,EAAoCX,GAAkBW,EAC3E,OAAO,SAAsBpR,EAAWL,EAAQC,QAC9B,IAAZA,IACFA,EAAUyR,GAGZ,IC/C6BnjB,EAC3BojB,ED8CEjS,EAAQ,CACVf,UAAW,SACXiT,iBAAkB,GAClB3R,QAAS3W,OAAOuW,OAAOvW,OAAOuW,OAAO,GAAIiR,IAAkBY,GAC3D3O,cAAe,GACfpD,SAAU,CACRU,UAAWA,EACXL,OAAQA,GAEVlM,WAAY,GACZ8L,OAAQ,IAENiS,EAAmB,GACnBC,GAAc,EACd5lB,EAAW,CACbwT,MAAOA,EACPqS,WAAY,SAAoB9R,GAC9B+R,IACAtS,EAAMO,QAAU3W,OAAOuW,OAAOvW,OAAOuW,OAAOvW,OAAOuW,OAAO,GAAI6R,GAAiBhS,EAAMO,SAAUA,GAC/FP,EAAMwG,cAAgB,CACpB7F,UAAW9X,GAAU8X,GAAa6G,GAAkB7G,GAAaA,EAAU4J,eAAiB/C,GAAkB7G,EAAU4J,gBAAkB,GAC1IjK,OAAQkH,GAAkBlH,IAI5B,IExE4BuQ,EAC9B0B,EFuEML,EDtCG,SAAwBrB,GAErC,IAAIqB,EAAmBtB,GAAMC,GAE7B,OAAO1R,GAAeJ,QAAO,SAAUC,EAAKe,GAC1C,OAAOf,EAAIxJ,OAAO0c,EAAiB5d,QAAO,SAAU2c,GAClD,OAAOA,EAASlR,QAAUA,QAE3B,IC8B0ByS,EExEK3B,EFwEsB,GAAGrb,OAAOsc,EAAkB9R,EAAMO,QAAQsQ,WEvE9F0B,EAAS1B,EAAU9R,QAAO,SAAUwT,EAAQE,GAC9C,IAAIC,EAAWH,EAAOE,EAAQ5S,MAK9B,OAJA0S,EAAOE,EAAQ5S,MAAQ6S,EAAW9oB,OAAOuW,OAAOvW,OAAOuW,OAAOvW,OAAOuW,OAAO,GAAIuS,GAAWD,GAAU,GAAI,CACvGlS,QAAS3W,OAAOuW,OAAOvW,OAAOuW,OAAO,GAAIuS,EAASnS,SAAUkS,EAAQlS,SACpErU,KAAMtC,OAAOuW,OAAOvW,OAAOuW,OAAO,GAAIuS,EAASxmB,MAAOumB,EAAQvmB,QAC3DumB,EACEF,IACN,IAEI3oB,OAAOC,KAAK0oB,GAAQzB,KAAI,SAAU7kB,GACvC,OAAOsmB,EAAOtmB,QFsGV,OAvCA+T,EAAMkS,iBAAmBA,EAAiB5d,QAAO,SAAUqe,GACzD,OAAOA,EAAE7S,WAqJbE,EAAMkS,iBAAiBpoB,SAAQ,SAAUgc,GACvC,IAAIjG,EAAOiG,EAAMjG,KACb+S,EAAgB9M,EAAMvF,QACtBA,OAA4B,IAAlBqS,EAA2B,GAAKA,EAC1CxS,EAAS0F,EAAM1F,OAEnB,GAAsB,mBAAXA,EAAuB,CAChC,IAAIyS,EAAYzS,EAAO,CACrBJ,MAAOA,EACPH,KAAMA,EACNrT,SAAUA,EACV+T,QAASA,IAGPuS,EAAS,aAEbX,EAAiBhc,KAAK0c,GAAaC,OA/H9BtmB,EAASka,UAOlBqM,YAAa,WACX,IAAIX,EAAJ,CAIA,IAAIY,EAAkBhT,EAAMC,SACxBU,EAAYqS,EAAgBrS,UAC5BL,EAAS0S,EAAgB1S,OAG7B,GAAK+Q,GAAiB1Q,EAAWL,GAAjC,CASAN,EAAM6D,MAAQ,CACZlD,UAAW6P,GAAiB7P,EAAWuB,GAAgB5B,GAAoC,UAA3BN,EAAMO,QAAQC,UAC9EF,OAAQU,GAAcV,IAOxBN,EAAMqN,OAAQ,EACdrN,EAAMf,UAAYe,EAAMO,QAAQtB,UAKhCe,EAAMkS,iBAAiBpoB,SAAQ,SAAUmnB,GACvC,OAAOjR,EAAMqD,cAAc4N,EAASpR,MAAQjW,OAAOuW,OAAO,GAAI8Q,EAAS/kB,SAIzE,IAAK,IAAIgN,EAAQ,EAAGA,EAAQ8G,EAAMkS,iBAAiBrkB,OAAQqL,IAUzD,IAAoB,IAAhB8G,EAAMqN,MAAV,CAMA,IAAI4F,EAAwBjT,EAAMkS,iBAAiBhZ,GAC/CrK,EAAKokB,EAAsBpkB,GAC3BqkB,EAAyBD,EAAsB1S,QAC/CqJ,OAAsC,IAA3BsJ,EAAoC,GAAKA,EACpDrT,EAAOoT,EAAsBpT,KAEf,mBAAPhR,IACTmR,EAAQnR,EAAG,CACTmR,MAAOA,EACPO,QAASqJ,EACT/J,KAAMA,EACNrT,SAAUA,KACNwT,QAjBNA,EAAMqN,OAAQ,EACdnU,GAAS,KAsBfwN,QCjM2B7X,EDiMV,WACf,OAAO,IAAIskB,SAAQ,SAAUC,GAC3B5mB,EAASumB,cACTK,EAAQpT,OClMT,WAUL,OATKiS,IACHA,EAAU,IAAIkB,SAAQ,SAAUC,GAC9BD,QAAQC,UAAUC,MAAK,WACrBpB,OAAUqB,EACVF,EAAQvkB,YAKPojB,ID2LLsB,QAAS,WACPjB,IACAF,GAAc,IAIlB,IAAKf,GAAiB1Q,EAAWL,GAK/B,OAAO9T,EAmCT,SAAS8lB,IACPH,EAAiBroB,SAAQ,SAAU+E,GACjC,OAAOA,OAETsjB,EAAmB,GAGrB,OAvCA3lB,EAAS6lB,WAAW9R,GAAS8S,MAAK,SAAUrT,IACrCoS,GAAe7R,EAAQiT,eAC1BjT,EAAQiT,cAAcxT,MAqCnBxT,GAGJ,IAAIinB,GAA4B/B,KGzPnC+B,GAA4B/B,GAAgB,CAC9CI,iBAFqB,CAAC3L,GAAgB/C,GAAesQ,GAAeC,MCMlEF,GAA4B/B,GAAgB,CAC9CI,iBAFqB,CAAC3L,GAAgB/C,GAAesQ,GAAeC,GAAajf,GAAQkf,GAAMhG,GAAiBlN,GAAOlD,2KnDNvG,+BAEC,YACF,sBACY,2BACP,kBACF,mBACG,4DAQC,kBACN,iBACK,uBAEC,kBACN,iBACK,wBAEE,oBACN,mBACK,0JoDElBvL,GAAO,WAYP4hB,GAAiB,IAAItpB,OAAUupB,4BAwB/BC,GAAgBnoB,EAAQ,UAAY,YACpCooB,GAAmBpoB,EAAQ,YAAc,UACzCqoB,GAAmBroB,EAAQ,aAAe,eAC1CsoB,GAAsBtoB,EAAQ,eAAiB,aAC/CuoB,GAAkBvoB,EAAQ,aAAe,cACzCwoB,GAAiBxoB,EAAQ,cAAgB,aAEzC8K,GAAU,CACdhC,OAAQ,EACRkf,MAAM,EACNjL,SAAU,kBACVhI,UAAW,SACX3V,QAAS,UACTqpB,aAAc,MAGVpd,GAAc,CAClBvC,OAAQ,2BACRkf,KAAM,UACNjL,SAAU,mBACVhI,UAAW,mBACX3V,QAAS,SACTqpB,aAAc,iBASVC,GAAAA,SAAAA,GACJ,SAAAA,EAAY/sB,EAASmC,GAAQ,IAAAgJ,EAAA,OAC3BA,EAAA4E,EAAAlN,KAAA6E,KAAM1H,IAAN0H,MAEKslB,QAAU,KACf7hB,EAAKqF,QAAUrF,EAAKsF,WAAWtO,GAC/BgJ,EAAK8hB,MAAQ9hB,EAAK+hB,kBAClB/hB,EAAKgiB,UAAYhiB,EAAKiiB,gBAEtBjiB,EAAK6F,qBARsB7F,oCA2B7Bc,OAAA,WACE,IAAIvE,KAAK2C,SAASgjB,WAAY3lB,KAAK2C,SAASe,UAAUE,SAzE9B,YAyExB,CAIA,IAAMgiB,EAAW5lB,KAAK2C,SAASe,UAAUE,SA5ErB,QA8EpByhB,EAASQ,aAELD,GAIJ5lB,KAAKwO,WAGPA,KAAA,WACE,KAAIxO,KAAK2C,SAASgjB,UAAY3lB,KAAK2C,SAASe,UAAUE,SAzF9B,aAyF+D5D,KAAKulB,MAAM7hB,UAAUE,SAxFxF,SAwFpB,CAIA,IAAM8J,EAAS2X,EAASS,qBAAqB9lB,KAAK2C,UAC5CsJ,EAAgB,CACpBA,cAAejM,KAAK2C,UAKtB,IAFkBzC,EAAasB,QAAQxB,KAAK2C,SAzGhC,mBAyGsDsJ,GAEpDlK,iBAAd,CAKA,IAAK/B,KAAKylB,UAAW,CACnB,QAAsB,IAAXM,GACT,MAAM,IAAI1Y,UAAU,gEAGtB,IAAI+N,EAAmBpb,KAAK2C,SAEG,WAA3B3C,KAAK8I,QAAQ4I,UACf0J,EAAmB1N,EACV9T,EAAUoG,KAAK8I,QAAQ4I,aAChC0J,EAAmBpb,KAAK8I,QAAQ4I,eAGa,IAAlC1R,KAAK8I,QAAQ4I,UAAUrC,SAChC+L,EAAmBpb,KAAK8I,QAAQ4I,UAAU,KAI9C1R,KAAKslB,QAAUS,GAAoB3K,EAAkBpb,KAAKulB,MAAOvlB,KAAKgmB,oBAQhC,IAAA1f,EADxC,GAAI,iBAAkBnO,SAASyE,kBAC5B8Q,EAAOlK,QAzHc,gBA0HtB8C,EAAA,IAAGC,OAAHlG,MAAAiG,EAAanO,SAASmE,KAAKqK,UACxB9L,SAAQ,SAAAmT,GAAI,OAAI9N,EAAaQ,GAAGsN,EAAM,YAAa,M9DrBzC,kB8DwBfhO,KAAK2C,SAASsjB,QACdjmB,KAAK2C,SAAS6B,aAAa,iBAAiB,GAE5CxE,KAAKulB,MAAM7hB,UAAUa,OA1ID,QA2IpBvE,KAAK2C,SAASe,UAAUa,OA3IJ,QA4IpBrE,EAAasB,QAAQkM,EAnJR,oBAmJ6BzB,QAG5CsC,KAAA,WACE,IAAIvO,KAAK2C,SAASgjB,WAAY3lB,KAAK2C,SAASe,UAAUE,SAjJ9B,aAiJgE5D,KAAKulB,MAAM7hB,UAAUE,SAhJzF,QAgJpB,CAIA,IAAM8J,EAAS2X,EAASS,qBAAqB9lB,KAAK2C,UAC5CsJ,EAAgB,CACpBA,cAAejM,KAAK2C,UAGJzC,EAAasB,QAAQkM,EAnK3B,mBAmK+CzB,GAE7ClK,mBAIV/B,KAAKslB,SACPtlB,KAAKslB,QAAQhB,UAGftkB,KAAKulB,MAAM7hB,UAAUa,OAnKD,QAoKpBvE,KAAK2C,SAASe,UAAUa,OApKJ,QAqKpBrE,EAAasB,QAAQkM,EA9KP,qBA8K6BzB,QAG7CnJ,QAAA,WACEuF,EAAA5B,UAAM3D,QAAN3H,KAAA6E,MACAE,EAAaC,IAAIH,KAAK2C,SAhMX,gBAiMX3C,KAAKulB,MAAQ,KAETvlB,KAAKslB,UACPtlB,KAAKslB,QAAQhB,UACbtkB,KAAKslB,QAAU,SAInB7N,OAAA,WACEzX,KAAKylB,UAAYzlB,KAAK0lB,gBAClB1lB,KAAKslB,SACPtlB,KAAKslB,QAAQ7N,YAMjBnO,mBAAA,WAAqB,IAAAY,EAAAlK,KACnBE,EAAaQ,GAAGV,KAAK2C,SAnMR,qBAmM+B,SAAA9D,GAC1CA,EAAM4D,iBACN5D,EAAMqnB,kBACNhc,EAAK3F,eAITwE,WAAA,SAAWtO,GAST,OARAA,EAAM6P,EAAA,GACDtK,KAAK4C,YAAY6E,QACjB3C,EAAYI,kBAAkBlF,KAAK2C,UACnClI,GAGLF,EAAgByI,GAAMvI,EAAQuF,KAAK4C,YAAYoF,aAExCvN,KAGT+qB,gBAAA,WACE,OAAOrf,EAAemB,KAAKtH,KAAK2C,SAzMd,kBAyMuC,MAG3DwjB,cAAA,WACE,IAAMC,EAAiBpmB,KAAK2C,SAAS/G,WAErC,GAAIwqB,EAAe1iB,UAAUE,SArNN,WAsNrB,OAAOshB,GAGT,GAAIkB,EAAe1iB,UAAUE,SAxNJ,aAyNvB,OAAOuhB,GAIT,IAAMkB,EAAkF,QAA1EptB,iBAAiB+G,KAAKulB,OAAOe,iBAAiB,iBAAiB5tB,OAE7E,OAAI0tB,EAAe1iB,UAAUE,SAjOP,UAkObyiB,EAAQtB,GAAmBD,GAG7BuB,EAAQpB,GAAsBD,MAGvCU,cAAA,WACE,OAA0D,OAAnD1lB,KAAK2C,SAASa,QAAd,cAGTwiB,iBAAA,WACE,IAAMZ,EAAe,CACnBpV,UAAWhQ,KAAKmmB,gBAChBvE,UAAW,CAAC,CACVhR,KAAM,kBACNU,QAAS,CACP4J,YAAalb,KAAK8I,QAAQ6b,KAC1BhL,aAAc3Z,KAAK8I,QAAQ4Q,aAajC,MAP6B,WAAzB1Z,KAAK8I,QAAQ/M,UACfqpB,EAAaxD,UAAY,CAAC,CACxBhR,KAAM,cACNC,SAAS,KAIbvG,EAAA,GACK8a,EACAplB,KAAK8I,QAAQsc,iBAMbmB,kBAAP,SAAyBjuB,EAASmC,GAChC,IAAIwC,EAAOK,EAAahF,EAjSX,eAwSb,GAJK2E,IACHA,EAAO,IAAIooB,EAAS/sB,EAHY,iBAAXmC,EAAsBA,EAAS,OAMhC,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAI4S,UAAJ,oBAAkC5S,EAAlC,KAGRwC,EAAKxC,SAIFsJ,gBAAP,SAAuBtJ,GACrB,OAAOuF,KAAKgE,MAAK,WACfqhB,EAASkB,kBAAkBvmB,KAAMvF,SAI9BorB,WAAP,SAAkBhnB,GAChB,IAAIA,GA/SmB,IA+STA,EAAMgG,SAAiD,UAAfhG,EAAMuB,MAlThD,QAkToEvB,EAAM7B,KAMtF,IAFA,IAAMwpB,EAAUrgB,EAAeE,KA/RN,+BAiShB3H,EAAI,EAAGC,EAAM6nB,EAAQ5nB,OAAQF,EAAIC,EAAKD,IAAK,CAClD,IAAMgP,EAAS2X,EAASS,qBAAqBU,EAAQ9nB,IAC/C+nB,EAAUnpB,EAAakpB,EAAQ9nB,GAhU1B,eAiULuN,EAAgB,CACpBA,cAAeua,EAAQ9nB,IAOzB,GAJIG,GAAwB,UAAfA,EAAMuB,OACjB6L,EAAcya,WAAa7nB,GAGxB4nB,EAAL,CAIA,IAAME,EAAeF,EAAQlB,MAC7B,GAAKiB,EAAQ9nB,GAAGgF,UAAUE,SAvTR,QA2TlB,KAAI/E,IAA0B,UAAfA,EAAMuB,MACjB,kBAAkB7E,KAAKsD,EAAMkB,OAAOyL,UACpB,UAAf3M,EAAMuB,MA9UD,QA8UqBvB,EAAM7B,MACjC2pB,EAAa/iB,SAAS/E,EAAMkB,SAKhC,IADkBG,EAAasB,QAAQkM,EA5U7B,mBA4UiDzB,GAC7ClK,iBAAd,CAMgD,IAAA6E,EAAhD,GAAI,iBAAkBzO,SAASyE,iBAC7BgK,EAAA,IAAGL,OAAHlG,MAAAuG,EAAazO,SAASmE,KAAKqK,UACxB9L,SAAQ,SAAAmT,GAAI,OAAI9N,EAAaC,IAAI6N,EAAM,YAAa,M9D5N5C,kB8D+NbwY,EAAQ9nB,GAAG8F,aAAa,gBAAiB,SAErCiiB,EAAQnB,SACVmB,EAAQnB,QAAQhB,UAGlBqC,EAAajjB,UAAUC,OApVL,QAqVlB6iB,EAAQ9nB,GAAGgF,UAAUC,OArVH,QAsVlBzD,EAAasB,QAAQkM,EA/VT,qBA+V+BzB,SAIxC6Z,qBAAP,SAA4BxtB,GAC1B,OAAOO,EAAuBP,IAAYA,EAAQsD,cAG7CgrB,sBAAP,SAA6B/nB,GAQ3B,KAAI,kBAAkBtD,KAAKsD,EAAMkB,OAAOyL,SAxX1B,UAyXZ3M,EAAM7B,KA1XO,WA0Xe6B,EAAM7B,MAtXjB,cAuXf6B,EAAM7B,KAxXO,YAwXmB6B,EAAM7B,KACtC6B,EAAMkB,OAAOyD,QAjWC,oBAkWfohB,GAAerpB,KAAKsD,EAAM7B,QAI7B6B,EAAM4D,iBACN5D,EAAMqnB,mBAEFlmB,KAAK2lB,WAAY3lB,KAAK0D,UAAUE,SAlXZ,aAkXxB,CAIA,IAAM8J,EAAS2X,EAASS,qBAAqB9lB,MACvC4lB,EAAW5lB,KAAK0D,UAAUE,SAtXZ,QAwXpB,GA3Ye,WA2YX/E,EAAM7B,IAIR,OAHegD,KAAKoG,QAnXG,+BAmX6BpG,KAAOmG,EAAegB,KAAKnH,KAnXxD,+BAmXoF,IACpGimB,aACPZ,EAASQ,aAIX,GAAKD,GAjZS,UAiZG/mB,EAAM7B,IAAvB,CAKA,IAAM6pB,EAAQ1gB,EAAeE,KA1XF,8DA0X+BqH,GAAQrI,OAAO3J,GAEzE,GAAKmrB,EAAMjoB,OAAX,CAIA,IAAIqL,EAAQ4c,EAAMpb,QAAQ5M,EAAMkB,QA1Zf,YA6ZblB,EAAM7B,KAAwBiN,EAAQ,GACxCA,IA7ZiB,cAiafpL,EAAM7B,KAA0BiN,EAAQ4c,EAAMjoB,OAAS,GACzDqL,IAMF4c,EAFA5c,GAAmB,IAAXA,EAAe,EAAIA,GAEdgc,cAzBXZ,EAASQ,uDAtUX,OAAOpe,uCAIP,OAAOO,oCAIP,MAzFa,oBAkEXqd,CAAiB3iB,GAwXvBxC,EAAaQ,GAAGvI,SAvaY,+BAUC,8BA6Z2CktB,GAASuB,uBACjF1mB,EAAaQ,GAAGvI,SAxaY,+BAYN,iBA4Z2CktB,GAASuB,uBAC1E1mB,EAAaQ,GAAGvI,SA1aU,6BA0asBktB,GAASQ,YACzD3lB,EAAaQ,GAAGvI,SAzaU,6BAyasBktB,GAASQ,YACzD3lB,EAAaQ,GAAGvI,SA5aU,6BAWG,+BAiayC,SAAU0G,GAC9EA,EAAM4D,iBACN5D,EAAMqnB,kBACNb,GAASkB,kBAAkBvmB,KAAM,aAEnCE,EAAaQ,GAAGvI,SAjbU,6BAYE,kBAqayC,SAAAkT,GAAC,OAAIA,EAAE6a,qBAS5E1pB,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAGoD,IAChCrB,EAAE/B,GAAGoD,IAAQqiB,GAASthB,gBACtBpC,EAAE/B,GAAGoD,IAAMoB,YAAcihB,GACzB1jB,EAAE/B,GAAGoD,IAAMqB,WAAa,WAEtB,OADA1C,EAAE/B,GAAGoD,IAAQmB,EACNkhB,GAASthB,qBCtdtB,IAMM0D,GAAU,CACdqf,UAAU,EACVnf,UAAU,EACVse,OAAO,GAGHje,GAAc,CAClB8e,SAAU,mBACVnf,SAAU,UACVse,MAAO,WAoCHc,GAAAA,SAAAA,GACJ,SAAAA,EAAYzuB,EAASmC,GAAQ,IAAAgJ,EAAA,OAC3BA,EAAA4E,EAAAlN,KAAA6E,KAAM1H,IAAN0H,MAEK8I,QAAUrF,EAAKsF,WAAWtO,GAC/BgJ,EAAKujB,QAAU7gB,EAAeO,QAlBV,gBAkBmCpO,GACvDmL,EAAKwjB,UAAY,KACjBxjB,EAAKyjB,UAAW,EAChBzjB,EAAK0jB,oBAAqB,EAC1B1jB,EAAK2jB,sBAAuB,EAC5B3jB,EAAKmK,kBAAmB,EACxBnK,EAAK4jB,gBAAkB,EAVI5jB,oCAyB7Bc,OAAA,SAAO0H,GACL,OAAOjM,KAAKknB,SAAWlnB,KAAKuO,OAASvO,KAAKwO,KAAKvC,MAGjDuC,KAAA,SAAKvC,GAAe,IAAA/B,EAAAlK,KAClB,IAAIA,KAAKknB,WAAYlnB,KAAK4N,iBAA1B,CAII5N,KAAK2C,SAASe,UAAUE,SApDR,UAqDlB5D,KAAK4N,kBAAmB,GAG1B,IAAM0Z,EAAYpnB,EAAasB,QAAQxB,KAAK2C,SArEhC,gBAqEsD,CAChEsJ,cAAAA,IAGEjM,KAAKknB,UAAYI,EAAUvlB,mBAI/B/B,KAAKknB,UAAW,EAEhBlnB,KAAKunB,kBACLvnB,KAAKwnB,gBAELxnB,KAAKynB,gBAELznB,KAAK0nB,kBACL1nB,KAAK2nB,kBAELznB,EAAaQ,GAAGV,KAAK2C,SAnFA,yBAgBK,6BAmEiD,SAAA9D,GAAK,OAAIqL,EAAKqE,KAAK1P,MAE9FqB,EAAaQ,GAAGV,KAAKgnB,QAlFI,8BAkF8B,WACrD9mB,EAAaS,IAAIuJ,EAAKvH,SApFD,4BAoFkC,SAAA9D,GACjDA,EAAMkB,SAAWmK,EAAKvH,WACxBuH,EAAKkd,sBAAuB,SAKlCpnB,KAAK4nB,eAAc,WAAA,OAAM1d,EAAK2d,aAAa5b,WAG7CsC,KAAA,SAAK1P,GAAO,IAAA6L,EAAA1K,KAKV,IAJInB,GACFA,EAAM4D,iBAGHzC,KAAKknB,WAAYlnB,KAAK4N,oBAIT1N,EAAasB,QAAQxB,KAAK2C,SAhHhC,iBAkHEZ,iBAAd,CAIA/B,KAAKknB,UAAW,EAChB,IAAMY,EAAa9nB,KAAK2C,SAASe,UAAUE,SAvGvB,QAuHpB,GAdIkkB,IACF9nB,KAAK4N,kBAAmB,GAG1B5N,KAAK0nB,kBACL1nB,KAAK2nB,kBAELznB,EAAaC,IAAIhI,SA3HF,oBA6Hf6H,KAAK2C,SAASe,UAAUC,OAjHJ,QAmHpBzD,EAAaC,IAAIH,KAAK2C,SA7HD,0BA8HrBzC,EAAaC,IAAIH,KAAKgnB,QA3HG,8BA6HrBc,EAAY,CACd,IAAM5uB,EAAqBJ,EAAiCkH,KAAK2C,UAEjEzC,EAAaS,IAAIX,KAAK2C,S/DlLL,iB+DkL+B,SAAA9D,GAAK,OAAI6L,EAAKqd,WAAWlpB,MACzE9E,EAAqBiG,KAAK2C,SAAUzJ,QAEpC8G,KAAK+nB,iBAITjlB,QAAA,WACE,CAAC9J,OAAQgH,KAAK2C,SAAU3C,KAAKgnB,SAC1BnsB,SAAQ,SAAAmtB,GAAW,OAAI9nB,EAAaC,IAAI6nB,EAnKhC,gBAqKX3f,EAAA5B,UAAM3D,QAAN3H,KAAA6E,MAOAE,EAAaC,IAAIhI,SAvJF,oBAyJf6H,KAAK8I,QAAU,KACf9I,KAAKgnB,QAAU,KACfhnB,KAAKinB,UAAY,KACjBjnB,KAAKknB,SAAW,KAChBlnB,KAAKmnB,mBAAqB,KAC1BnnB,KAAKonB,qBAAuB,KAC5BpnB,KAAK4N,iBAAmB,KACxB5N,KAAKqnB,gBAAkB,QAGzBY,aAAA,WACEjoB,KAAKynB,mBAKP1e,WAAA,SAAWtO,GAMT,OALAA,EAAM6P,EAAA,GACD7C,GACAhN,GAELF,EArMS,QAqMaE,EAAQuN,IACvBvN,KAGTotB,aAAA,SAAa5b,GAAe,IAAApB,EAAA7K,KACpB8nB,EAAa9nB,KAAK2C,SAASe,UAAUE,SAxKvB,QAyKdskB,EAAY/hB,EAAeO,QApKT,cAoKsC1G,KAAKgnB,SAE9DhnB,KAAK2C,SAAS/G,YAAcoE,KAAK2C,SAAS/G,WAAW9B,WAAakN,KAAKC,cAE1E9O,SAASmE,KAAK6rB,YAAYnoB,KAAK2C,UAGjC3C,KAAK2C,SAAShH,MAAMI,QAAU,QAC9BiE,KAAK2C,SAASsC,gBAAgB,eAC9BjF,KAAK2C,SAAS6B,aAAa,cAAc,GACzCxE,KAAK2C,SAAS6B,aAAa,OAAQ,UACnCxE,KAAK2C,SAASkD,UAAY,EAEtBqiB,IACFA,EAAUriB,UAAY,GAGpBiiB,GACF5rB,EAAO8D,KAAK2C,UAGd3C,KAAK2C,SAASe,UAAU4H,IA7LJ,QA+LhBtL,KAAK8I,QAAQmd,OACfjmB,KAAKooB,gBAGP,IAAMC,EAAqB,WACrBxd,EAAK/B,QAAQmd,OACfpb,EAAKlI,SAASsjB,QAGhBpb,EAAK+C,kBAAmB,EACxB1N,EAAasB,QAAQqJ,EAAKlI,SAtNf,iBAsNsC,CAC/CsJ,cAAAA,KAIJ,GAAI6b,EAAY,CACd,IAAM5uB,EAAqBJ,EAAiCkH,KAAKgnB,SAEjE9mB,EAAaS,IAAIX,KAAKgnB,Q/D1QL,gB+D0Q8BqB,GAC/CtuB,EAAqBiG,KAAKgnB,QAAS9tB,QAEnCmvB,OAIJD,cAAA,WAAgB,IAAAtb,EAAA9M,KACdE,EAAaC,IAAIhI,SArOF,oBAsOf+H,EAAaQ,GAAGvI,SAtOD,oBAsO0B,SAAA0G,GACnC1G,WAAa0G,EAAMkB,QACnB+M,EAAKnK,WAAa9D,EAAMkB,QACvB+M,EAAKnK,SAASiB,SAAS/E,EAAMkB,SAChC+M,EAAKnK,SAASsjB,cAKpByB,gBAAA,WAAkB,IAAAY,EAAAtoB,KACZA,KAAKknB,SACPhnB,EAAaQ,GAAGV,KAAK2C,SA9OA,4BA8OiC,SAAA9D,GAChDypB,EAAKxf,QAAQnB,UArQN,WAqQkB9I,EAAM7B,KACjC6B,EAAM4D,iBACN6lB,EAAK/Z,QACK+Z,EAAKxf,QAAQnB,UAxQd,WAwQ0B9I,EAAM7B,KACzCsrB,EAAKC,gCAITroB,EAAaC,IAAIH,KAAK2C,SAvPD,+BA2PzBglB,gBAAA,WAAkB,IAAAa,EAAAxoB,KACZA,KAAKknB,SACPhnB,EAAaQ,GAAG1H,OA/PJ,mBA+P0B,WAAA,OAAMwvB,EAAKf,mBAEjDvnB,EAAaC,IAAInH,OAjQL,sBAqQhB+uB,WAAA,WAAa,IAAAU,EAAAzoB,KACXA,KAAK2C,SAAShH,MAAMI,QAAU,OAC9BiE,KAAK2C,SAAS6B,aAAa,eAAe,GAC1CxE,KAAK2C,SAASsC,gBAAgB,cAC9BjF,KAAK2C,SAASsC,gBAAgB,QAC9BjF,KAAK4N,kBAAmB,EACxB5N,KAAK4nB,eAAc,WACjBzvB,SAASmE,KAAKoH,UAAUC,OAnQN,cAoQlB8kB,EAAKC,oBACLD,EAAKE,kBACLzoB,EAAasB,QAAQinB,EAAK9lB,SAnRd,yBAuRhBimB,gBAAA,WACE5oB,KAAKinB,UAAUrrB,WAAWkI,YAAY9D,KAAKinB,WAC3CjnB,KAAKinB,UAAY,QAGnBW,cAAA,SAAcnrB,GAAU,IAAAosB,EAAA7oB,KAChB8oB,EAAU9oB,KAAK2C,SAASe,UAAUE,SA/QpB,QAAA,OAiRlB,GAEF,GAAI5D,KAAKknB,UAAYlnB,KAAK8I,QAAQge,SAAU,CAiC1C,GAhCA9mB,KAAKinB,UAAY9uB,SAAS4wB,cAAc,OACxC/oB,KAAKinB,UAAU+B,UAvRO,iBAyRlBF,GACF9oB,KAAKinB,UAAUvjB,UAAU4H,IAAIwd,GAG/B3wB,SAASmE,KAAK6rB,YAAYnoB,KAAKinB,WAE/B/mB,EAAaQ,GAAGV,KAAK2C,SAtSF,0BAsSiC,SAAA9D,GAC9CgqB,EAAKzB,qBACPyB,EAAKzB,sBAAuB,EAI1BvoB,EAAMkB,SAAWlB,EAAMoqB,gBAIG,WAA1BJ,EAAK/f,QAAQge,SACf+B,EAAKN,6BAELM,EAAKta,WAILua,GACF5sB,EAAO8D,KAAKinB,WAGdjnB,KAAKinB,UAAUvjB,UAAU4H,IAjTP,SAmTbwd,EAEH,YADArsB,IAIF,IAAMysB,EAA6BpwB,EAAiCkH,KAAKinB,WAEzE/mB,EAAaS,IAAIX,KAAKinB,U/DnXL,gB+DmXgCxqB,GACjD1C,EAAqBiG,KAAKinB,UAAWiC,QAChC,IAAKlpB,KAAKknB,UAAYlnB,KAAKinB,UAAW,CAC3CjnB,KAAKinB,UAAUvjB,UAAUC,OA7TP,QA+TlB,IAAMwlB,EAAiB,WACrBN,EAAKD,kBACLnsB,KAGF,GAAIuD,KAAK2C,SAASe,UAAUE,SArUV,QAqUqC,CACrD,IAAMslB,EAA6BpwB,EAAiCkH,KAAKinB,WACzE/mB,EAAaS,IAAIX,KAAKinB,U/D/XP,gB+D+XkCkC,GACjDpvB,EAAqBiG,KAAKinB,UAAWiC,QAErCC,SAGF1sB,OAIJ8rB,2BAAA,WAA6B,IAAAa,EAAAppB,KAE3B,IADkBE,EAAasB,QAAQxB,KAAK2C,SAjWtB,0BAkWRZ,iBAAd,CAIA,IAAMsnB,EAAqBrpB,KAAK2C,SAAS4W,aAAephB,SAASyE,gBAAgBoY,aAE5EqU,IACHrpB,KAAK2C,SAAShH,MAAM2c,UAAY,UAGlCtY,KAAK2C,SAASe,UAAU4H,IA3VF,gBA4VtB,IAAMge,EAA0BxwB,EAAiCkH,KAAKgnB,SACtE9mB,EAAaC,IAAIH,KAAK2C,S/DvZH,iB+DwZnBzC,EAAaS,IAAIX,KAAK2C,S/DxZH,iB+DwZ6B,WAC9CymB,EAAKzmB,SAASe,UAAUC,OA/VJ,gBAgWf0lB,IACHnpB,EAAaS,IAAIyoB,EAAKzmB,S/D3ZP,iB+D2ZiC,WAC9CymB,EAAKzmB,SAAShH,MAAM2c,UAAY,MAElCve,EAAqBqvB,EAAKzmB,SAAU2mB,OAGxCvvB,EAAqBiG,KAAK2C,SAAU2mB,GACpCtpB,KAAK2C,SAASsjB,YAOhBwB,cAAA,WACE,IAAM4B,EACJrpB,KAAK2C,SAAS4W,aAAephB,SAASyE,gBAAgBoY,eAElDhV,KAAKmnB,oBAAsBkC,IAAuB1sB,GAAWqD,KAAKmnB,qBAAuBkC,GAAsB1sB,KACnHqD,KAAK2C,SAAShH,MAAM4tB,YAAiBvpB,KAAKqnB,gBAA1C,OAGGrnB,KAAKmnB,qBAAuBkC,IAAuB1sB,IAAYqD,KAAKmnB,oBAAsBkC,GAAsB1sB,KACnHqD,KAAK2C,SAAShH,MAAM6tB,aAAkBxpB,KAAKqnB,gBAA3C,SAIJqB,kBAAA,WACE1oB,KAAK2C,SAAShH,MAAM4tB,YAAc,GAClCvpB,KAAK2C,SAAShH,MAAM6tB,aAAe,MAGrCjC,gBAAA,WACE,IAAM7hB,EAAOvN,SAASmE,KAAKqJ,wBAC3B3F,KAAKmnB,mBAAqBnvB,KAAKme,MAAMzQ,EAAKI,KAAOJ,EAAKiK,OAAS3W,OAAOywB,WACtEzpB,KAAKqnB,gBAAkBrnB,KAAK0pB,wBAG9BlC,cAAA,WAAgB,IAAAmC,EAAA3pB,KACd,GAAIA,KAAKmnB,mBAAoB,CAK3BhhB,EAAeE,KAvYU,qDAwYtBxL,SAAQ,SAAAvC,GACP,IAAMsxB,EAAgBtxB,EAAQqD,MAAM6tB,aAC9BK,EAAoB7wB,OAAOC,iBAAiBX,GAAS,iBAC3DwM,EAAYC,iBAAiBzM,EAAS,gBAAiBsxB,GACvDtxB,EAAQqD,MAAM6tB,aAAkBnwB,OAAOC,WAAWuwB,GAAqBF,EAAKtC,gBAA5E,QAIJlhB,EAAeE,KA/YW,eAgZvBxL,SAAQ,SAAAvC,GACP,IAAMwxB,EAAexxB,EAAQqD,MAAMouB,YAC7BC,EAAmBhxB,OAAOC,iBAAiBX,GAAS,gBAC1DwM,EAAYC,iBAAiBzM,EAAS,eAAgBwxB,GACtDxxB,EAAQqD,MAAMouB,YAAiB1wB,OAAOC,WAAW0wB,GAAoBL,EAAKtC,gBAA1E,QAIJ,IAAMuC,EAAgBzxB,SAASmE,KAAKX,MAAM6tB,aACpCK,EAAoB7wB,OAAOC,iBAAiBd,SAASmE,MAAM,iBAEjEwI,EAAYC,iBAAiB5M,SAASmE,KAAM,gBAAiBstB,GAC7DzxB,SAASmE,KAAKX,MAAM6tB,aAAkBnwB,OAAOC,WAAWuwB,GAAqB7pB,KAAKqnB,gBAAlF,KAGFlvB,SAASmE,KAAKoH,UAAU4H,IAzaJ,iBA4atBqd,gBAAA,WAEExiB,EAAeE,KAraY,qDAsaxBxL,SAAQ,SAAAvC,GACP,IAAMic,EAAUzP,EAAYU,iBAAiBlN,EAAS,sBAC/B,IAAZic,IACTzP,EAAYE,oBAAoB1M,EAAS,iBACzCA,EAAQqD,MAAM6tB,aAAejV,MAKnCpO,EAAeE,KA9aa,eA+azBxL,SAAQ,SAAAvC,GACP,IAAMkZ,EAAS1M,EAAYU,iBAAiBlN,EAAS,qBAC/B,IAAXkZ,IACT1M,EAAYE,oBAAoB1M,EAAS,gBACzCA,EAAQqD,MAAMouB,YAAcvY,MAKlC,IAAM+C,EAAUzP,EAAYU,iBAAiBrN,SAASmE,KAAM,sBACrC,IAAZiY,EACTpc,SAASmE,KAAKX,MAAM6tB,aAAe,IAEnC1kB,EAAYE,oBAAoB7M,SAASmE,KAAM,iBAC/CnE,SAASmE,KAAKX,MAAM6tB,aAAejV,MAIvCmV,mBAAA,WACE,IAAMO,EAAY9xB,SAAS4wB,cAAc,OACzCkB,EAAUjB,UA/cwB,0BAgdlC7wB,SAASmE,KAAK6rB,YAAY8B,GAC1B,IAAMC,EAAiBD,EAAUtkB,wBAAwBuM,MAAQ+X,EAAUhV,YAE3E,OADA9c,SAASmE,KAAKwH,YAAYmmB,GACnBC,KAKFnmB,gBAAP,SAAuBtJ,EAAQwR,GAC7B,OAAOjM,KAAKgE,MAAK,WACf,IAAI/G,EAAOK,EAAa0C,KAxfb,YAyfL8I,EAAOwB,EAAA,GACR7C,GACA3C,EAAYI,kBAAkBlF,MACX,iBAAXvF,GAAuBA,EAASA,EAAS,IAOtD,GAJKwC,IACHA,EAAO,IAAI8pB,EAAM/mB,KAAM8I,IAGH,iBAAXrO,EAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAI4S,UAAJ,oBAAkC5S,EAAlC,KAGRwC,EAAKxC,GAAQwR,gDArcjB,OAAOxE,oCAIP,MAvEa,iBAkDXsf,CAAcrkB,GAkepBxC,EAAaQ,GAAGvI,SAxfU,0BAWG,4BA6eyC,SAAU0G,GAAO,IAAAsrB,EAAAnqB,KAC/ED,EAASlH,EAAuBmH,MAEjB,MAAjBA,KAAKwL,SAAoC,SAAjBxL,KAAKwL,SAC/B3M,EAAM4D,iBAGRvC,EAAaS,IAAIZ,EAvgBH,iBAugBuB,SAAAunB,GAC/BA,EAAUvlB,kBAKd7B,EAAaS,IAAIZ,EA9gBH,mBA8gByB,WACjCrE,EAAUyuB,IACZA,EAAKlE,cAKX,IAAIhpB,EAAOK,EAAayC,EAxiBT,YAyiBf,IAAK9C,EAAM,CACT,IAAMxC,EAAM6P,EAAA,GACPxF,EAAYI,kBAAkBnF,GAC9B+E,EAAYI,kBAAkBlF,OAGnC/C,EAAO,IAAI8pB,GAAMhnB,EAAQtF,GAG3BwC,EAAKuR,KAAKxO,SAUZxD,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAF,MAC3B+B,EAAE/B,GAAF,MAAamnB,GAAMhjB,gBACnBpC,EAAE/B,GAAF,MAAWwE,YAAc2iB,GACzBplB,EAAE/B,GAAF,MAAWyE,WAAa,WAEtB,OADA1C,EAAE/B,GAAF,MAAauE,EACN4iB,GAAMhjB,qBC9lBnB,IAAMqmB,GAAW,IAAInsB,IAAI,CACvB,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eAUIosB,GAAmB,8DAOnBC,GAAmB,qIAyBZC,GAAmB,CAE9BC,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAzCP,kBA0C7BnO,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BoO,KAAM,GACNnO,EAAG,GACHoO,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJ3sB,EAAG,GACH4sB,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,IAGC,SAASC,GAAaC,EAAYC,EAAWC,GAAY,IAAAhmB,EAC9D,IAAK8lB,EAAWxtB,OACd,OAAOwtB,EAGT,GAAIE,GAAoC,mBAAfA,EACvB,OAAOA,EAAWF,GAQpB,IALA,IACMG,GADY,IAAIvzB,OAAOwzB,WACKC,gBAAgBL,EAAY,aACxDM,EAAgB/xB,OAAOC,KAAKyxB,GAC5Brb,GAAW1K,EAAA,IAAGC,OAAHlG,MAAAiG,EAAaimB,EAAgBjwB,KAAKwD,iBAAiB,MAZNme,EAAA,SAcrDvf,EAAOC,GAd8C,IAAAiI,EAetD+lB,EAAK3b,EAAStS,GACdkuB,EAASD,EAAGvc,SAAS/U,cAE3B,IAAKqxB,EAActrB,SAASwrB,GAG1B,OAFAD,EAAG/wB,WAAWkI,YAAY6oB,GAE1B,WAGF,IAAME,GAAgBjmB,EAAA,IAAGL,OAAHlG,MAAAuG,EAAa+lB,EAAGxnB,YAChC2nB,EAAoB,GAAGvmB,OAAO8lB,EAAU,MAAQ,GAAIA,EAAUO,IAAW,IAE/EC,EAAchyB,SAAQ,SAAAkyB,IApFD,SAACA,EAAMC,GAC9B,IAAMC,EAAWF,EAAK3c,SAAS/U,cAE/B,GAAI2xB,EAAqB5rB,SAAS6rB,GAChC,OAAI7C,GAAS9qB,IAAI2tB,IACRxsB,QAAQssB,EAAKG,UAAU9xB,MAAMivB,KAAqB0C,EAAKG,UAAU9xB,MAAMkvB,KASlF,IAHA,IAAM6C,EAASH,EAAqB3nB,QAAO,SAAA+nB,GAAS,OAAIA,aAAqB9xB,UAGpEoD,EAAI,EAAGC,EAAMwuB,EAAOvuB,OAAQF,EAAIC,EAAKD,IAC5C,GAAIuuB,EAAS7xB,MAAM+xB,EAAOzuB,IACxB,OAAO,EAIX,OAAO,GAiEE2uB,CAAiBN,EAAMD,IAC1BH,EAAG1nB,gBAAgB8nB,EAAK3c,cAfrB1R,EAAI,EAAGC,EAAMqS,EAASpS,OAAQF,EAAIC,EAAKD,IAAKuf,EAA5Cvf,GAoBT,OAAO6tB,EAAgBjwB,KAAKgxB,UCvF9B,IAAMtqB,GAAO,UAIPuqB,GAAqB,IAAIjyB,OAAJ,wBAAyC,KAC9DkyB,GAAwB,IAAIvvB,IAAI,CAAC,WAAY,YAAa,eAE1D+J,GAAc,CAClBylB,UAAW,UACXC,SAAU,SACVC,MAAO,4BACPnsB,QAAS,SACTosB,MAAO,kBACP5a,KAAM,UACNza,SAAU,mBACVyX,UAAW,oBACXrB,UAAW,2BACXoO,mBAAoB,eACpBrD,SAAU,mBACVmU,YAAa,oBACbC,SAAU,UACVxB,WAAY,kBACZD,UAAW,SACXjH,aAAc,iBAGV2I,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAOvxB,EAAQ,OAAS,QACxBwxB,OAAQ,SACRC,KAAMzxB,EAAQ,QAAU,QAGpB8K,GAAU,CACdgmB,WAAW,EACXC,SAAU,+GAIVlsB,QAAS,cACTmsB,MAAO,GACPC,MAAO,EACP5a,MAAM,EACNza,UAAU,EACVyX,UAAW,MACXrB,WAAW,EACXoO,mBAAoB,KACpBrD,SAAU,kBACVmU,YAAa,GACbC,UAAU,EACVxB,WAAY,KACZD,UAAW9B,GACXnF,aAAc,MAGVzrB,GAAQ,CACZ00B,KAAI,kBACJC,OAAM,oBACNC,KAAI,kBACJC,MAAK,mBACLC,SAAQ,sBACRC,MAAK,mBACLC,QAAO,qBACPC,SAAQ,sBACRC,WAAU,wBACVC,WAAU,yBAuBNC,GAAAA,SAAAA,GACJ,SAAAA,EAAYz2B,EAASmC,GAAQ,IAAAgJ,EAC3B,QAAsB,IAAXsiB,GACT,MAAM,IAAI1Y,UAAU,+DAFK,OAK3B5J,EAAA4E,EAAAlN,KAAA6E,KAAM1H,IAAN0H,MAGKgvB,YAAa,EAClBvrB,EAAKwrB,SAAW,EAChBxrB,EAAKyrB,YAAc,GACnBzrB,EAAK0rB,eAAiB,GACtB1rB,EAAK6hB,QAAU,KAGf7hB,EAAKhJ,OAASgJ,EAAKsF,WAAWtO,GAC9BgJ,EAAK2rB,IAAM,KAEX3rB,EAAK4rB,gBAlBsB5rB,oCAiD7B6rB,OAAA,WACEtvB,KAAKgvB,YAAa,KAGpBO,QAAA,WACEvvB,KAAKgvB,YAAa,KAGpBQ,cAAA,WACExvB,KAAKgvB,YAAchvB,KAAKgvB,cAG1BzqB,OAAA,SAAO1F,GACL,GAAKmB,KAAKgvB,WAIV,GAAInwB,EAAO,CACT,IAAM4wB,EAAUzvB,KAAK4C,YAAYC,SAC7B4jB,EAAUnpB,EAAauB,EAAMoB,eAAgBwvB,GAE5ChJ,IACHA,EAAU,IAAIzmB,KAAK4C,YAAY/D,EAAMoB,eAAgBD,KAAK0vB,sBAC1DpyB,EAAauB,EAAMoB,eAAgBwvB,EAAShJ,IAG9CA,EAAQ0I,eAAeQ,OAASlJ,EAAQ0I,eAAeQ,MAEnDlJ,EAAQmJ,uBACVnJ,EAAQoJ,OAAO,KAAMpJ,GAErBA,EAAQqJ,OAAO,KAAMrJ,OAElB,CACL,GAAIzmB,KAAK+vB,gBAAgBrsB,UAAUE,SAtGjB,QAwGhB,YADA5D,KAAK8vB,OAAO,KAAM9vB,MAIpBA,KAAK6vB,OAAO,KAAM7vB,UAItB8C,QAAA,WACEqI,aAAanL,KAAKivB,UAElB/uB,EAAaC,IAAIH,KAAK2C,SAAU3C,KAAK4C,YAAY4E,WACjDtH,EAAaC,IAAIH,KAAK2C,SAASa,QAAd,UAA+C,gBAAiBxD,KAAKgwB,mBAElFhwB,KAAKovB,KACPpvB,KAAKovB,IAAIxzB,WAAWkI,YAAY9D,KAAKovB,KAGvCpvB,KAAKgvB,WAAa,KAClBhvB,KAAKivB,SAAW,KAChBjvB,KAAKkvB,YAAc,KACnBlvB,KAAKmvB,eAAiB,KAClBnvB,KAAKslB,SACPtlB,KAAKslB,QAAQhB,UAGftkB,KAAKslB,QAAU,KACftlB,KAAKvF,OAAS,KACduF,KAAKovB,IAAM,KACX/mB,EAAA5B,UAAM3D,QAAN3H,KAAA6E,SAGFwO,KAAA,WAAO,IAAAtE,EAAAlK,KACL,GAAoC,SAAhCA,KAAK2C,SAAShH,MAAMI,QACtB,MAAM,IAAIP,MAAM,uCAGlB,GAAIwE,KAAKiwB,iBAAmBjwB,KAAKgvB,WAAY,CAC3C,IAAM1H,EAAYpnB,EAAasB,QAAQxB,KAAK2C,SAAU3C,KAAK4C,YAAYjJ,MAAM40B,MACvE2B,EjE5GW,SAAjBC,EAAiB73B,GACrB,IAAKH,SAASyE,gBAAgBwzB,aAC5B,OAAO,KAIT,GAAmC,mBAAxB93B,EAAQga,YAA4B,CAC7C,IAAM+d,EAAO/3B,EAAQga,cACrB,OAAO+d,aAAgB9d,WAAa8d,EAAO,KAG7C,OAAI/3B,aAAmBia,WACdja,EAIJA,EAAQsD,WAINu0B,EAAe73B,EAAQsD,YAHrB,KiE2Fcu0B,CAAenwB,KAAK2C,UACjC2tB,EAA4B,OAAfJ,EACjBlwB,KAAK2C,SAAS4N,cAAc3T,gBAAgBgH,SAAS5D,KAAK2C,UAC1DutB,EAAWtsB,SAAS5D,KAAK2C,UAE3B,GAAI2kB,EAAUvlB,mBAAqBuuB,EACjC,OAGF,IAAMlB,EAAMpvB,KAAK+vB,gBACXQ,EAAQz4B,EAAOkI,KAAK4C,YAAYI,MAEtCosB,EAAI5qB,aAAa,KAAM+rB,GACvBvwB,KAAK2C,SAAS6B,aAAa,mBAAoB+rB,GAE/CvwB,KAAKwwB,aAEDxwB,KAAKvF,OAAOgzB,WACd2B,EAAI1rB,UAAU4H,IAlKE,QAqKlB,IAAM0E,EAA6C,mBAA1BhQ,KAAKvF,OAAOuV,UACnChQ,KAAKvF,OAAOuV,UAAU7U,KAAK6E,KAAMovB,EAAKpvB,KAAK2C,UAC3C3C,KAAKvF,OAAOuV,UAERygB,EAAazwB,KAAK0wB,eAAe1gB,GACvChQ,KAAK2wB,oBAAoBF,GAEzB,IAAM9hB,EAAY3O,KAAK4wB,gBACvBtzB,EAAa8xB,EAAKpvB,KAAK4C,YAAYC,SAAU7C,MAExCA,KAAK2C,SAAS4N,cAAc3T,gBAAgBgH,SAAS5D,KAAKovB,MAC7DzgB,EAAUwZ,YAAYiH,GAGxBlvB,EAAasB,QAAQxB,KAAK2C,SAAU3C,KAAK4C,YAAYjJ,MAAM80B,UAE3DzuB,KAAKslB,QAAUS,GAAoB/lB,KAAK2C,SAAUysB,EAAKpvB,KAAKgmB,iBAAiByK,IAE7ErB,EAAI1rB,UAAU4H,IArLI,QAuLlB,IACiBulB,EAQ+BvqB,EAT1CunB,EAAiD,mBAA5B7tB,KAAKvF,OAAOozB,YAA6B7tB,KAAKvF,OAAOozB,cAAgB7tB,KAAKvF,OAAOozB,YAC5G,GAAIA,GACFgD,EAAAzB,EAAI1rB,WAAU4H,IAAdjL,MAAAwwB,EAAqBhD,EAAYr0B,MAAM,MAOzC,GAAI,iBAAkBrB,SAASyE,iBAC7B0J,EAAA,IAAGC,OAAHlG,MAAAiG,EAAanO,SAASmE,KAAKqK,UAAU9L,SAAQ,SAAAvC,GAC3C4H,EAAaQ,GAAGpI,EAAS,ajEzIhB,kBiE6Ib,IAAMw4B,EAAW,WACf,IAAMC,EAAiB7mB,EAAKglB,YAE5BhlB,EAAKglB,YAAc,KACnBhvB,EAAasB,QAAQ0I,EAAKvH,SAAUuH,EAAKtH,YAAYjJ,MAAM60B,OAvM3C,QAyMZuC,GACF7mB,EAAK4lB,OAAO,KAAM5lB,IAItB,GAAIlK,KAAKovB,IAAI1rB,UAAUE,SAnNL,QAmNgC,CAChD,IAAM1K,EAAqBJ,EAAiCkH,KAAKovB,KACjElvB,EAAaS,IAAIX,KAAKovB,IjEvTP,gBiEuT4B0B,GAC3C/2B,EAAqBiG,KAAKovB,IAAKl2B,QAE/B43B,QAKNviB,KAAA,WAAO,IAAA7D,EAAA1K,KACL,GAAKA,KAAKslB,QAAV,CAIA,IAAM8J,EAAMpvB,KAAK+vB,gBACXe,EAAW,WA/NI,SAgOfpmB,EAAKwkB,aAAoCE,EAAIxzB,YAC/CwzB,EAAIxzB,WAAWkI,YAAYsrB,GAG7B1kB,EAAKsmB,iBACLtmB,EAAK/H,SAASsC,gBAAgB,oBAC9B/E,EAAasB,QAAQkJ,EAAK/H,SAAU+H,EAAK9H,YAAYjJ,MAAM20B,QAEvD5jB,EAAK4a,UACP5a,EAAK4a,QAAQhB,UACb5Z,EAAK4a,QAAU,OAKnB,IADkBplB,EAAasB,QAAQxB,KAAK2C,SAAU3C,KAAK4C,YAAYjJ,MAAM00B,MAC/DtsB,iBAAd,CAQgD,IAAA6E,EAAhD,GAJAwoB,EAAI1rB,UAAUC,OArPM,QAyPhB,iBAAkBxL,SAASyE,iBAC7BgK,EAAA,IAAGL,OAAHlG,MAAAuG,EAAazO,SAASmE,KAAKqK,UACxB9L,SAAQ,SAAAvC,GAAO,OAAI4H,EAAaC,IAAI7H,EAAS,YAAa2D,MAO/D,GAJA+D,KAAKmvB,eAAL,OAAqC,EACrCnvB,KAAKmvB,eAAL,OAAqC,EACrCnvB,KAAKmvB,eAAL,OAAqC,EAEjCnvB,KAAKovB,IAAI1rB,UAAUE,SApQH,QAoQ8B,CAChD,IAAM1K,EAAqBJ,EAAiCs2B,GAE5DlvB,EAAaS,IAAIyuB,EjEzWA,gBiEyWqB0B,GACtC/2B,EAAqBq1B,EAAKl2B,QAE1B43B,IAGF9wB,KAAKkvB,YAAc,QAGrBzX,OAAA,WACuB,OAAjBzX,KAAKslB,SACPtlB,KAAKslB,QAAQ7N,YAMjBwY,cAAA,WACE,OAAOxvB,QAAQT,KAAKixB,eAGtBlB,cAAA,WACE,GAAI/vB,KAAKovB,IACP,OAAOpvB,KAAKovB,IAGd,IAAM92B,EAAUH,SAAS4wB,cAAc,OAIvC,OAHAzwB,EAAQg1B,UAAYttB,KAAKvF,OAAOizB,SAEhC1tB,KAAKovB,IAAM92B,EAAQqO,SAAS,GACrB3G,KAAKovB,OAGdoB,WAAA,WACE,IAAMpB,EAAMpvB,KAAK+vB,gBACjB/vB,KAAKkxB,kBAAkB/qB,EAAeO,QAnSX,iBAmS2C0oB,GAAMpvB,KAAKixB,YACjF7B,EAAI1rB,UAAUC,OA3SM,OAEA,WA4StButB,kBAAA,SAAkB54B,EAAS64B,GACzB,GAAgB,OAAZ74B,EAIJ,MAAuB,iBAAZ64B,GAAwBv3B,EAAUu3B,IACvCA,EAAQ9hB,SACV8hB,EAAUA,EAAQ,SAIhBnxB,KAAKvF,OAAOuY,KACVme,EAAQv1B,aAAetD,IACzBA,EAAQg1B,UAAY,GACpBh1B,EAAQ6vB,YAAYgJ,IAGtB74B,EAAQ84B,YAAcD,EAAQC,mBAM9BpxB,KAAKvF,OAAOuY,MACVhT,KAAKvF,OAAOqzB,WACdqD,EAAUhF,GAAagF,EAASnxB,KAAKvF,OAAO4xB,UAAWrsB,KAAKvF,OAAO6xB,aAGrEh0B,EAAQg1B,UAAY6D,GAEpB74B,EAAQ84B,YAAcD,MAI1BF,SAAA,WACE,IAAItD,EAAQ3tB,KAAK2C,SAASnK,aAAa,0BAQvC,OANKm1B,IACHA,EAAqC,mBAAtB3tB,KAAKvF,OAAOkzB,MACzB3tB,KAAKvF,OAAOkzB,MAAMxyB,KAAK6E,KAAK2C,UAC5B3C,KAAKvF,OAAOkzB,OAGTA,KAGT0D,iBAAA,SAAiBZ,GACf,MAAmB,UAAfA,EACK,MAGU,SAAfA,EACK,QAGFA,KAKTzK,iBAAA,SAAiByK,GAAY,IAAA5lB,EAAA7K,KACrBsxB,EAAe,CACnB1gB,KAAM,OACNU,QAAS,CACP4J,aAAa,IAsCjB,OAlCIlb,KAAKvF,OAAOsiB,qBACduU,EAAahgB,QAAQyL,mBAAqB/c,KAAKvF,OAAOsiB,oBAiCxDzS,EAAA,GA9BwB,CACtB0F,UAAWygB,EACX7O,UAAW,CACT0P,EACA,CACE1gB,KAAM,kBACNU,QAAS,CACPqI,aAAc3Z,KAAKvF,OAAOif,WAG9B,CACE9I,KAAM,QACNU,QAAS,CACPhZ,QAAO,IAAM0H,KAAK4C,YAAYI,KAAvB,WAGX,CACE4N,KAAM,WACNC,SAAS,EACTC,MAAO,aACPlR,GAAI,SAAA3C,GAAI,OAAI4N,EAAK0mB,6BAA6Bt0B,MAGlDsnB,cAAe,SAAAtnB,GACTA,EAAKqU,QAAQtB,YAAc/S,EAAK+S,WAClCnF,EAAK0mB,6BAA6Bt0B,KAOnC+C,KAAKvF,OAAO2qB,iBAInBuL,oBAAA,SAAoBF,GAClBzwB,KAAK+vB,gBAAgBrsB,UAAU4H,IAAOkmB,cAAgBxxB,KAAKqxB,iBAAiBZ,OAG9EG,cAAA,WACE,OAA8B,IAA1B5wB,KAAKvF,OAAOkU,UACPxW,SAASmE,KAGd1C,EAAUoG,KAAKvF,OAAOkU,WACjB3O,KAAKvF,OAAOkU,UAGdxI,EAAeO,QAAQ1G,KAAKvF,OAAOkU,cAG5C+hB,eAAA,SAAe1gB,GACb,OAAO+d,GAAc/d,EAAUvU,kBAGjC4zB,cAAA,WAAgB,IAAAviB,EAAA9M,KACGA,KAAKvF,OAAO+G,QAAQhI,MAAM,KAElCqB,SAAQ,SAAA2G,GACf,GAAgB,UAAZA,EACFtB,EAAaQ,GAAGoM,EAAKnK,SAAUmK,EAAKlK,YAAYjJ,MAAM+0B,MAAO5hB,EAAKrS,OAAOlC,UAAU,SAAAsG,GAAK,OAAIiO,EAAKvI,OAAO1F,WAEnG,GAzaU,WAyaN2C,EAA4B,CACrC,IAAMiwB,EA7aQ,UA6aEjwB,EACdsL,EAAKlK,YAAYjJ,MAAMk1B,WACvB/hB,EAAKlK,YAAYjJ,MAAMg1B,QACnB+C,EAhbQ,UAgbGlwB,EACfsL,EAAKlK,YAAYjJ,MAAMm1B,WACvBhiB,EAAKlK,YAAYjJ,MAAMi1B,SAEzB1uB,EAAaQ,GAAGoM,EAAKnK,SAAU8uB,EAAS3kB,EAAKrS,OAAOlC,UAAU,SAAAsG,GAAK,OAAIiO,EAAK+iB,OAAOhxB,MACnFqB,EAAaQ,GAAGoM,EAAKnK,SAAU+uB,EAAU5kB,EAAKrS,OAAOlC,UAAU,SAAAsG,GAAK,OAAIiO,EAAKgjB,OAAOjxB,UAIxFmB,KAAKgwB,kBAAoB,WACnBljB,EAAKnK,UACPmK,EAAKyB,QAITrO,EAAaQ,GAAGV,KAAK2C,SAASa,QAAd,UAA+C,gBAAiBxD,KAAKgwB,mBAEjFhwB,KAAKvF,OAAOlC,SACdyH,KAAKvF,OAAL6P,EAAA,GACKtK,KAAKvF,OADV,CAEE+G,QAAS,SACTjJ,SAAU,KAGZyH,KAAK2xB,eAITA,UAAA,WACE,IAAMhE,EAAQ3tB,KAAK2C,SAASnK,aAAa,SACnCo5B,SAA2B5xB,KAAK2C,SAASnK,aAAa,2BAExDm1B,GAA+B,WAAtBiE,KACX5xB,KAAK2C,SAAS6B,aAAa,yBAA0BmpB,GAAS,KAC1DA,GAAU3tB,KAAK2C,SAASnK,aAAa,eAAkBwH,KAAK2C,SAASyuB,aACvEpxB,KAAK2C,SAAS6B,aAAa,aAAcmpB,GAG3C3tB,KAAK2C,SAAS6B,aAAa,QAAS,QAIxCqrB,OAAA,SAAOhxB,EAAO4nB,GACZ,IAAMgJ,EAAUzvB,KAAK4C,YAAYC,UACjC4jB,EAAUA,GAAWnpB,EAAauB,EAAMoB,eAAgBwvB,MAGtDhJ,EAAU,IAAIzmB,KAAK4C,YACjB/D,EAAMoB,eACND,KAAK0vB,sBAEPpyB,EAAauB,EAAMoB,eAAgBwvB,EAAShJ,IAG1C5nB,IACF4nB,EAAQ0I,eACS,YAAftwB,EAAMuB,KAveQ,QADA,UAyeZ,GAGFqmB,EAAQsJ,gBAAgBrsB,UAAUE,SAnflB,SAEC,SAif8C6iB,EAAQyI,YACzEzI,EAAQyI,YAlfW,QAsfrB/jB,aAAasb,EAAQwI,UAErBxI,EAAQyI,YAxfa,OA0fhBzI,EAAQhsB,OAAOmzB,OAAUnH,EAAQhsB,OAAOmzB,MAAMpf,KAKnDiY,EAAQwI,SAAW30B,YAAW,WA/fT,SAggBfmsB,EAAQyI,aACVzI,EAAQjY,SAETiY,EAAQhsB,OAAOmzB,MAAMpf,MARtBiY,EAAQjY,WAWZshB,OAAA,SAAOjxB,EAAO4nB,GACZ,IAAMgJ,EAAUzvB,KAAK4C,YAAYC,UACjC4jB,EAAUA,GAAWnpB,EAAauB,EAAMoB,eAAgBwvB,MAGtDhJ,EAAU,IAAIzmB,KAAK4C,YACjB/D,EAAMoB,eACND,KAAK0vB,sBAEPpyB,EAAauB,EAAMoB,eAAgBwvB,EAAShJ,IAG1C5nB,IACF4nB,EAAQ0I,eACS,aAAftwB,EAAMuB,KA9gBQ,QADA,UAghBZ,GAGFqmB,EAAQmJ,yBAIZzkB,aAAasb,EAAQwI,UAErBxI,EAAQyI,YA7hBY,MA+hBfzI,EAAQhsB,OAAOmzB,OAAUnH,EAAQhsB,OAAOmzB,MAAMrf,KAKnDkY,EAAQwI,SAAW30B,YAAW,WApiBV,QAqiBdmsB,EAAQyI,aACVzI,EAAQlY,SAETkY,EAAQhsB,OAAOmzB,MAAMrf,MARtBkY,EAAQlY,WAWZqhB,qBAAA,WACE,IAAK,IAAMpuB,KAAWxB,KAAKmvB,eACzB,GAAInvB,KAAKmvB,eAAe3tB,GACtB,OAAO,EAIX,OAAO,KAGTuH,WAAA,SAAWtO,GACT,IAAMo3B,EAAiB/sB,EAAYI,kBAAkBlF,KAAK2C,UAuC1D,OArCAhI,OAAOC,KAAKi3B,GAAgBh3B,SAAQ,SAAAi3B,GAC9BtE,GAAsBluB,IAAIwyB,WACrBD,EAAeC,MAItBr3B,GAAsC,iBAArBA,EAAOkU,WAA0BlU,EAAOkU,UAAUU,SACrE5U,EAAOkU,UAAYlU,EAAOkU,UAAU,IASV,iBAN5BlU,EAAM6P,EAAA,GACDtK,KAAK4C,YAAY6E,QACjBoqB,EACmB,iBAAXp3B,GAAuBA,EAASA,EAAS,KAGpCmzB,QAChBnzB,EAAOmzB,MAAQ,CACbpf,KAAM/T,EAAOmzB,MACbrf,KAAM9T,EAAOmzB,QAIW,iBAAjBnzB,EAAOkzB,QAChBlzB,EAAOkzB,MAAQlzB,EAAOkzB,MAAMzyB,YAGA,iBAAnBT,EAAO02B,UAChB12B,EAAO02B,QAAU12B,EAAO02B,QAAQj2B,YAGlCX,EAAgByI,GAAMvI,EAAQuF,KAAK4C,YAAYoF,aAE3CvN,EAAOqzB,WACTrzB,EAAOizB,SAAWvB,GAAa1xB,EAAOizB,SAAUjzB,EAAO4xB,UAAW5xB,EAAO6xB,aAGpE7xB,KAGTi1B,mBAAA,WACE,IAAMj1B,EAAS,GAEf,GAAIuF,KAAKvF,OACP,IAAK,IAAMuC,KAAOgD,KAAKvF,OACjBuF,KAAK4C,YAAY6E,QAAQzK,KAASgD,KAAKvF,OAAOuC,KAChDvC,EAAOuC,GAAOgD,KAAKvF,OAAOuC,IAKhC,OAAOvC,KAGTu2B,eAAA,WACE,IAAM5B,EAAMpvB,KAAK+vB,gBACXgC,EAAW3C,EAAI52B,aAAa,SAAS4C,MAAMmyB,IAChC,OAAbwE,GAAqBA,EAASnzB,OAAS,GACzCmzB,EAASlQ,KAAI,SAAAmQ,GAAK,OAAIA,EAAMt5B,UACzBmC,SAAQ,SAAAo3B,GAAM,OAAI7C,EAAI1rB,UAAUC,OAAOsuB,SAI9CV,6BAAA,SAA6BW,GAAY,IAC/BnhB,EAAUmhB,EAAVnhB,MAEHA,IAIL/Q,KAAKovB,IAAMre,EAAMC,SAASK,OAC1BrR,KAAKgxB,iBACLhxB,KAAK2wB,oBAAoB3wB,KAAK0wB,eAAe3f,EAAMf,gBAK9CjM,gBAAP,SAAuBtJ,GACrB,OAAOuF,KAAKgE,MAAK,WACf,IAAI/G,EAAOK,EAAa0C,KAhtBb,cAitBL8I,EAA4B,iBAAXrO,GAAuBA,EAE9C,IAAKwC,IAAQ,eAAe1B,KAAKd,MAI5BwC,IACHA,EAAO,IAAI8xB,EAAQ/uB,KAAM8I,IAGL,iBAAXrO,GAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAI4S,UAAJ,oBAAkC5S,EAAlC,KAGRwC,EAAKxC,kDA/mBT,OAAOgN,gCAIP,OAAOzE,oCAIP,MAzHa,2CA6Hb,OAAOrJ,qCAIP,MAhIW,kDAoIX,OAAOqO,SA7CL+mB,CAAgBrsB,GAqpBtBlG,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAGoD,IAChCrB,EAAE/B,GAAGoD,IAAQ+rB,GAAQhrB,gBACrBpC,EAAE/B,GAAGoD,IAAMoB,YAAc2qB,GACzBptB,EAAE/B,GAAGoD,IAAMqB,WAAa,WAEtB,OADA1C,EAAE/B,GAAGoD,IAAQmB,EACN4qB,GAAQhrB,qBC3wBrB,IAAMf,GAAO,UAIPuqB,GAAqB,IAAIjyB,OAAJ,wBAAyC,KAE9DmM,GAAO6C,EAAA,GACRykB,GAAQtnB,QADA,CAEXuI,UAAW,QACXxO,QAAS,QACT2vB,QAAS,GACTzD,SAAU,gJAON1lB,GAAWsC,EAAA,GACZykB,GAAQ/mB,YADI,CAEfmpB,QAAS,8BAGLx3B,GAAQ,CACZ00B,KAAI,kBACJC,OAAM,oBACNC,KAAI,kBACJC,MAAK,mBACLC,SAAQ,sBACRC,MAAK,mBACLC,QAAO,qBACPC,SAAQ,sBACRC,WAAU,wBACVC,WAAU,yBAeNqD,GAAAA,SAAAA,uFA6BJlC,cAAA,WACE,OAAOjwB,KAAKixB,YAAcjxB,KAAKoyB,iBAGjC5B,WAAA,WACE,IAAMpB,EAAMpvB,KAAK+vB,gBAGjB/vB,KAAKkxB,kBAAkB/qB,EAAeO,QA9CnB,kBA8C2C0oB,GAAMpvB,KAAKixB,YACzE,IAAIE,EAAUnxB,KAAKoyB,cACI,mBAAZjB,IACTA,EAAUA,EAAQh2B,KAAK6E,KAAK2C,WAG9B3C,KAAKkxB,kBAAkB/qB,EAAeO,QAnDjB,gBAmD2C0oB,GAAM+B,GAEtE/B,EAAI1rB,UAAUC,OAzDM,OACA,WA6DtBgtB,oBAAA,SAAoBF,GAClBzwB,KAAK+vB,gBAAgBrsB,UAAU4H,IAAOkmB,cAAgBxxB,KAAKqxB,iBAAiBZ,OAG9E2B,YAAA,WACE,OAAOpyB,KAAK2C,SAASnK,aAAa,oBAAsBwH,KAAKvF,OAAO02B,WAGtEH,eAAA,WACE,IAAM5B,EAAMpvB,KAAK+vB,gBACXgC,EAAW3C,EAAI52B,aAAa,SAAS4C,MAAMmyB,IAChC,OAAbwE,GAAqBA,EAASnzB,OAAS,GACzCmzB,EAASlQ,KAAI,SAAAmQ,GAAK,OAAIA,EAAMt5B,UACzBmC,SAAQ,SAAAo3B,GAAM,OAAI7C,EAAI1rB,UAAUC,OAAOsuB,SAMvCluB,gBAAP,SAAuBtJ,GACrB,OAAOuF,KAAKgE,MAAK,WACf,IAAI/G,EAAOK,EAAa0C,KAtHb,cAuHL8I,EAA4B,iBAAXrO,EAAsBA,EAAS,KAEtD,IAAKwC,IAAQ,eAAe1B,KAAKd,MAI5BwC,IACHA,EAAO,IAAIk1B,EAAQnyB,KAAM8I,GACzBxL,EAAa0C,KA/HJ,aA+HoB/C,IAGT,iBAAXxC,GAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAI4S,UAAJ,oBAAkC5S,EAAlC,KAGRwC,EAAKxC,kDApFT,OAAOgN,gCAIP,OAAOzE,oCAIP,MA3Da,2CA+Db,OAAOrJ,qCAIP,MAlEW,kDAsEX,OAAOqO,SAxBLmqB,CAAgBpD,IAqGtBvyB,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAGoD,IAChCrB,EAAE/B,GAAGoD,IAAQmvB,GAAQpuB,gBACrBpC,EAAE/B,GAAGoD,IAAMoB,YAAc+tB,GACzBxwB,EAAE/B,GAAGoD,IAAMqB,WAAa,WAEtB,OADA1C,EAAE/B,GAAGoD,IAAQmB,EACNguB,GAAQpuB,qBCrJrB,IAAMf,GAAO,YAKPyE,GAAU,CACdhC,OAAQ,GACR4sB,OAAQ,OACRtyB,OAAQ,IAGJiI,GAAc,CAClBvC,OAAQ,SACR4sB,OAAQ,SACRtyB,OAAQ,oBA2BJuyB,GAAAA,SAAAA,GACJ,SAAAA,EAAYh6B,EAASmC,GAAQ,IAAAgJ,EAAA,OAC3BA,EAAA4E,EAAAlN,KAAA6E,KAAM1H,IAAN0H,MACKuyB,eAAqC,SAApBj6B,EAAQkT,QAAqBxS,OAASV,EAC5DmL,EAAKqF,QAAUrF,EAAKsF,WAAWtO,GAC/BgJ,EAAK0K,UAAe1K,EAAKqF,QAAQ/I,OAAb0D,eAA8CA,EAAKqF,QAAQ/I,OAA3D0D,sBAA6FA,EAAKqF,QAAQ/I,OAA1G0D,kBACpBA,EAAK+uB,SAAW,GAChB/uB,EAAKgvB,SAAW,GAChBhvB,EAAKivB,cAAgB,KACrBjvB,EAAKkvB,cAAgB,EAErBzyB,EAAaQ,GAAG+C,EAAK8uB,eAlCP,uBAkCqC,SAAA1zB,GAAK,OAAI4E,EAAKmvB,SAAS/zB,MAE1E4E,EAAKovB,UACLpvB,EAAKmvB,WAbsBnvB,oCA4B7BovB,QAAA,WAAU,IAAA3oB,EAAAlK,KACF8yB,EAAa9yB,KAAKuyB,iBAAmBvyB,KAAKuyB,eAAev5B,OAvC7C,SACE,WA0Cd+5B,EAAuC,SAAxB/yB,KAAK8I,QAAQupB,OAChCS,EACA9yB,KAAK8I,QAAQupB,OAETW,EA9Cc,aA8CDD,EACjB/yB,KAAKizB,gBACL,EAEFjzB,KAAKwyB,SAAW,GAChBxyB,KAAKyyB,SAAW,GAChBzyB,KAAK2yB,cAAgB3yB,KAAKkzB,mBAEV/sB,EAAeE,KAAKrG,KAAKmO,WAEjC0T,KAAI,SAAAvpB,GACV,IAAM66B,EAAiBx6B,EAAuBL,GACxCyH,EAASozB,EAAiBhtB,EAAeO,QAAQysB,GAAkB,KAEzE,GAAIpzB,EAAQ,CACV,IAAMqzB,EAAYrzB,EAAO4F,wBACzB,GAAIytB,EAAUlhB,OAASkhB,EAAUhhB,OAC/B,MAAO,CACLtN,EAAYiuB,GAAchzB,GAAQ6F,IAAMotB,EACxCG,GAKN,OAAO,QAEN9tB,QAAO,SAAAguB,GAAI,OAAIA,KACfjX,MAAK,SAACC,EAAGC,GAAJ,OAAUD,EAAE,GAAKC,EAAE,MACxBzhB,SAAQ,SAAAw4B,GACPnpB,EAAKsoB,SAAStrB,KAAKmsB,EAAK,IACxBnpB,EAAKuoB,SAASvrB,KAAKmsB,EAAK,UAI9BvwB,QAAA,WACEuF,EAAA5B,UAAM3D,QAAN3H,KAAA6E,MACAE,EAAaC,IAAIH,KAAKuyB,eAjHX,iBAmHXvyB,KAAKuyB,eAAiB,KACtBvyB,KAAK8I,QAAU,KACf9I,KAAKmO,UAAY,KACjBnO,KAAKwyB,SAAW,KAChBxyB,KAAKyyB,SAAW,KAChBzyB,KAAK0yB,cAAgB,KACrB1yB,KAAK2yB,cAAgB,QAKvB5pB,WAAA,SAAWtO,GAMT,GAA6B,iBAL7BA,EAAM6P,EAAA,GACD7C,GACmB,iBAAXhN,GAAuBA,EAASA,EAAS,KAGpCsF,QAAuBnG,EAAUa,EAAOsF,QAAS,CAAA,IAC3DlI,EAAO4C,EAAOsF,OAAdlI,GACDA,IACHA,EAAKC,EAAOkL,IACZvI,EAAOsF,OAAOlI,GAAKA,GAGrB4C,EAAOsF,OAAP,IAAoBlI,EAKtB,OAFA0C,EAAgByI,GAAMvI,EAAQuN,IAEvBvN,KAGTw4B,cAAA,WACE,OAAOjzB,KAAKuyB,iBAAmBv5B,OAC7BgH,KAAKuyB,eAAeva,YACpBhY,KAAKuyB,eAAe1sB,aAGxBqtB,iBAAA,WACE,OAAOlzB,KAAKuyB,eAAehZ,cAAgBvhB,KAAK2b,IAC9Cxb,SAASmE,KAAKid,aACdphB,SAASyE,gBAAgB2c,iBAI7B+Z,iBAAA,WACE,OAAOtzB,KAAKuyB,iBAAmBv5B,OAC7BA,OAAOu6B,YACPvzB,KAAKuyB,eAAe5sB,wBAAwByM,UAGhDwgB,SAAA,WACE,IAAM/sB,EAAY7F,KAAKizB,gBAAkBjzB,KAAK8I,QAAQrD,OAChD8T,EAAevZ,KAAKkzB,mBACpBM,EAAYxzB,KAAK8I,QAAQrD,OAAS8T,EAAevZ,KAAKszB,mBAM5D,GAJItzB,KAAK2yB,gBAAkBpZ,GACzBvZ,KAAK6yB,UAGHhtB,GAAa2tB,EAAjB,CACE,IAAMzzB,EAASC,KAAKyyB,SAASzyB,KAAKyyB,SAAS7zB,OAAS,GAEhDoB,KAAK0yB,gBAAkB3yB,GACzBC,KAAKyzB,UAAU1zB,OAJnB,CAUA,GAAIC,KAAK0yB,eAAiB7sB,EAAY7F,KAAKwyB,SAAS,IAAMxyB,KAAKwyB,SAAS,GAAK,EAG3E,OAFAxyB,KAAK0yB,cAAgB,UACrB1yB,KAAK0zB,SAIP,IAAK,IAAIh1B,EAAIsB,KAAKwyB,SAAS5zB,OAAQF,KAAM,CAChBsB,KAAK0yB,gBAAkB1yB,KAAKyyB,SAAS/zB,IACxDmH,GAAa7F,KAAKwyB,SAAS9zB,UACM,IAAzBsB,KAAKwyB,SAAS9zB,EAAI,IAAsBmH,EAAY7F,KAAKwyB,SAAS9zB,EAAI,KAGhFsB,KAAKyzB,UAAUzzB,KAAKyyB,SAAS/zB,SAKnC+0B,UAAA,SAAU1zB,GACRC,KAAK0yB,cAAgB3yB,EAErBC,KAAK0zB,SAEL,IAAMC,EAAU3zB,KAAKmO,UAAU3U,MAAM,KAClCqoB,KAAI,SAAAtpB,GAAQ,OAAOA,EAAP,oBAAmCwH,EAAnC,MAA+CxH,EAA/C,UAAiEwH,EAAjE,QAET6zB,EAAOztB,EAAeO,QAAQitB,EAAQE,KAAK,MAE7CD,EAAKlwB,UAAUE,SAjMU,kBAkM3BuC,EAAeO,QAzLY,mBAyLsBktB,EAAKpwB,QA1LlC,cA2LjBE,UAAU4H,IAlMO,UAoMpBsoB,EAAKlwB,UAAU4H,IApMK,YAuMpBsoB,EAAKlwB,UAAU4H,IAvMK,UAyMpBnF,EAAeW,QAAQ8sB,EAtMG,qBAuMvB/4B,SAAQ,SAAAi5B,GAGP3tB,EAAegB,KAAK2sB,EAAcC,+BAC/Bl5B,SAAQ,SAAAw4B,GAAI,OAAIA,EAAK3vB,UAAU4H,IA9MlB,aAiNhBnF,EAAegB,KAAK2sB,EA5MH,aA6Mdj5B,SAAQ,SAAAm5B,GACP7tB,EAAeQ,SAASqtB,EA/MX,aAgNVn5B,SAAQ,SAAAw4B,GAAI,OAAIA,EAAK3vB,UAAU4H,IApNtB,oBAyNtBpL,EAAasB,QAAQxB,KAAKuyB,eA9NV,wBA8N0C,CACxDtmB,cAAelM,OAInB2zB,OAAA,WACEvtB,EAAeE,KAAKrG,KAAKmO,WACtB9I,QAAO,SAAAiL,GAAI,OAAIA,EAAK5M,UAAUE,SAhOX,aAiOnB/I,SAAQ,SAAAyV,GAAI,OAAIA,EAAK5M,UAAUC,OAjOZ,gBAsOjBI,gBAAP,SAAuBtJ,GACrB,OAAOuF,KAAKgE,MAAK,WACf,IAAI/G,EAAOK,EAAa0C,KA7Pb,gBAoQX,GAJK/C,IACHA,EAAO,IAAIq1B,EAAUtyB,KAHW,iBAAXvF,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAI4S,UAAJ,oBAAkC5S,EAAlC,KAGRwC,EAAKxC,kDA7MT,OAAOgN,oCAIP,MAhEa,qBAwCX6qB,CAAkB5vB,GA6OxBxC,EAAaQ,GAAG1H,OAnQS,8BAmQoB,WAC3CmN,EAAeE,KA/PS,0BAgQrBxL,SAAQ,SAAAo5B,GAAG,OAAI,IAAI3B,GAAU2B,EAAKnvB,EAAYI,kBAAkB+uB,UAUrEz3B,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAGoD,IAChCrB,EAAE/B,GAAGoD,IAAQsvB,GAAUvuB,gBACvBpC,EAAE/B,GAAGoD,IAAMoB,YAAckuB,GACzB3wB,EAAE/B,GAAGoD,IAAMqB,WAAa,WAEtB,OADA1C,EAAE/B,GAAGoD,IAAQmB,EACNmuB,GAAUvuB,qBC3SvB,IA+BMmwB,GAAAA,SAAAA,uFASJ1lB,KAAA,WAAO,IAAA/K,EAAAzD,KACL,KAAKA,KAAK2C,SAAS/G,YACjBoE,KAAK2C,SAAS/G,WAAW9B,WAAakN,KAAKC,cAC3CjH,KAAK2C,SAASe,UAAUE,SA/BJ,WAgCpB5D,KAAK2C,SAASe,UAAUE,SA/BF,aA4BxB,CAOA,IAAIwD,EACErH,EAASlH,EAAuBmH,KAAK2C,UACrCwxB,EAAcn0B,KAAK2C,SAASa,QAhCN,qBAkC5B,GAAI2wB,EAAa,CACf,IAAMC,EAAwC,OAAzBD,EAAY/jB,UAA8C,OAAzB+jB,EAAY/jB,SAjC7C,wBADH,UAoClBhJ,GADAA,EAAWjB,EAAeE,KAAK+tB,EAAcD,IACzB/sB,EAASxI,OAAS,GAGxC,IAAIy1B,EAAY,KAYhB,GAVIjtB,IACFitB,EAAYn0B,EAAasB,QAAQ4F,EAxDvB,cAwD6C,CACrD6E,cAAejM,KAAK2C,cAINzC,EAAasB,QAAQxB,KAAK2C,SA3DhC,cA2DsD,CAChEsJ,cAAe7E,IAGHrF,kBAAmC,OAAdsyB,GAAsBA,EAAUtyB,kBAAnE,CAIA/B,KAAKyzB,UAAUzzB,KAAK2C,SAAUwxB,GAE9B,IAAMrD,EAAW,WACf5wB,EAAasB,QAAQ4F,EAvET,gBAuEiC,CAC3C6E,cAAexI,EAAKd,WAEtBzC,EAAasB,QAAQiC,EAAKd,SAxEf,eAwEsC,CAC/CsJ,cAAe7E,KAIfrH,EACFC,KAAKyzB,UAAU1zB,EAAQA,EAAOnE,WAAYk1B,GAE1CA,SAMJ2C,UAAA,SAAUn7B,EAASqW,EAAWlS,GAAU,IAAAyN,EAAAlK,KAKhCs0B,IAJiB3lB,GAAqC,OAAvBA,EAAUyB,UAA4C,OAAvBzB,EAAUyB,SAE5EjK,EAAeQ,SAASgI,EA9EN,WA6ElBxI,EAAeE,KA5EM,wBA4EmBsI,IAGZ,GACxBS,EAAkB3S,GAAa63B,GAAUA,EAAO5wB,UAAUE,SAtF5C,QAwFdktB,EAAW,WAAA,OAAM5mB,EAAKqqB,oBAAoBj8B,EAASg8B,EAAQ73B,IAEjE,GAAI63B,GAAUllB,EAAiB,CAC7B,IAAMlW,EAAqBJ,EAAiCw7B,GAC5DA,EAAO5wB,UAAUC,OA3FC,QA6FlBzD,EAAaS,IAAI2zB,EpE9HA,gBoE8HwBxD,GACzC/2B,EAAqBu6B,EAAQp7B,QAE7B43B,OAIJyD,oBAAA,SAAoBj8B,EAASg8B,EAAQ73B,GACnC,GAAI63B,EAAQ,CACVA,EAAO5wB,UAAUC,OAzGG,UA2GpB,IAAM6wB,EAAgBruB,EAAeO,QAhGJ,kCAgG4C4tB,EAAO14B,YAEhF44B,GACFA,EAAc9wB,UAAUC,OA9GN,UAiHgB,QAAhC2wB,EAAO97B,aAAa,SACtB87B,EAAO9vB,aAAa,iBAAiB,IAIzClM,EAAQoL,UAAU4H,IAtHI,UAuHe,QAAjChT,EAAQE,aAAa,SACvBF,EAAQkM,aAAa,iBAAiB,GAGxCtI,EAAO5D,GAEHA,EAAQoL,UAAUE,SA3HF,SA4HlBtL,EAAQoL,UAAU4H,IA3HA,QA8HhBhT,EAAQsD,YAActD,EAAQsD,WAAW8H,UAAUE,SAlI1B,oBAmIHtL,EAAQkL,QA7HZ,cAgIlB2C,EAAeE,KA3HU,oBA4HtBxL,SAAQ,SAAA45B,GAAQ,OAAIA,EAAS/wB,UAAU4H,IAtIxB,aAyIpBhT,EAAQkM,aAAa,iBAAiB,IAGpC/H,GACFA,OAMGsH,gBAAP,SAAuBtJ,GACrB,OAAOuF,KAAKgE,MAAK,WACf,IAAM/G,EAAOK,EAAa0C,KAhKf,WAgKkC,IAAIk0B,EAAIl0B,MAErD,GAAsB,iBAAXvF,EAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAI4S,UAAJ,oBAAkC5S,EAAlC,KAGRwC,EAAKxC,mDArIT,MAlCa,eA8BXy5B,CAAYxxB,GAqJlBxC,EAAaQ,GAAGvI,SA3KU,wBAYG,4EA+JyC,SAAU0G,GAC9EA,EAAM4D,kBAEOnF,EAAa0C,KAtLX,WAsL8B,IAAIk0B,GAAIl0B,OAChDwO,UAUPhS,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAF,IAC3B+B,EAAE/B,GAAF,IAAas0B,GAAInwB,gBACjBpC,EAAE/B,GAAF,IAAWwE,YAAc8vB,GACzBvyB,EAAE/B,GAAF,IAAWyE,WAAa,WAEtB,OADA1C,EAAE/B,GAAF,IAAauE,EACN+vB,GAAInwB,qBC3MjB,IAeMiE,GAAc,CAClBylB,UAAW,UACXiH,SAAU,UACV9G,MAAO,UAGHnmB,GAAU,CACdgmB,WAAW,EACXiH,UAAU,EACV9G,MAAO,KAWH+G,GAAAA,SAAAA,GACJ,SAAAA,EAAYr8B,EAASmC,GAAQ,IAAAgJ,EAAA,OAC3BA,EAAA4E,EAAAlN,KAAA6E,KAAM1H,IAAN0H,MAEK8I,QAAUrF,EAAKsF,WAAWtO,GAC/BgJ,EAAKwrB,SAAW,KAChBxrB,EAAK4rB,gBALsB5rB,oCAwB7B+K,KAAA,WAAO,IAAAtE,EAAAlK,KAGL,IAFkBE,EAAasB,QAAQxB,KAAK2C,SAtDhC,iBAwDEZ,iBAAd,CAIA/B,KAAK40B,gBAED50B,KAAK8I,QAAQ2kB,WACfztB,KAAK2C,SAASe,UAAU4H,IA5DN,QA+DpB,IAAMwlB,EAAW,WACf5mB,EAAKvH,SAASe,UAAUC,OA7DH,WA8DrBuG,EAAKvH,SAASe,UAAU4H,IA/DN,QAiElBpL,EAAasB,QAAQ0I,EAAKvH,SArEf,kBAuEPuH,EAAKpB,QAAQ4rB,WACfxqB,EAAK+kB,SAAW30B,YAAW,WACzB4P,EAAKqE,SACJrE,EAAKpB,QAAQ8kB,SAOpB,GAHA5tB,KAAK2C,SAASe,UAAUC,OA3EJ,QA4EpBzH,EAAO8D,KAAK2C,UACZ3C,KAAK2C,SAASe,UAAU4H,IA3ED,WA4EnBtL,KAAK8I,QAAQ2kB,UAAW,CAC1B,IAAMv0B,EAAqBJ,EAAiCkH,KAAK2C,UAEjEzC,EAAaS,IAAIX,KAAK2C,SrE9GL,gBqE8G+BmuB,GAChD/2B,EAAqBiG,KAAK2C,SAAUzJ,QAEpC43B,QAIJviB,KAAA,WAAO,IAAA7D,EAAA1K,KACL,GAAKA,KAAK2C,SAASe,UAAUE,SAxFT,UA4FF1D,EAAasB,QAAQxB,KAAK2C,SAnGhC,iBAqGEZ,iBAAd,CAIA,IAAM+uB,EAAW,WACfpmB,EAAK/H,SAASe,UAAU4H,IApGN,QAqGlBpL,EAAasB,QAAQkJ,EAAK/H,SA1Gd,oBA8Gd,GADA3C,KAAK2C,SAASe,UAAUC,OAvGJ,QAwGhB3D,KAAK8I,QAAQ2kB,UAAW,CAC1B,IAAMv0B,EAAqBJ,EAAiCkH,KAAK2C,UAEjEzC,EAAaS,IAAIX,KAAK2C,SrEzIL,gBqEyI+BmuB,GAChD/2B,EAAqBiG,KAAK2C,SAAUzJ,QAEpC43B,QAIJhuB,QAAA,WACE9C,KAAK40B,gBAED50B,KAAK2C,SAASe,UAAUE,SArHR,SAsHlB5D,KAAK2C,SAASe,UAAUC,OAtHN,QAyHpBzD,EAAaC,IAAIH,KAAK2C,SAjID,0BAmIrB0F,EAAA5B,UAAM3D,QAAN3H,KAAA6E,MACAA,KAAK8I,QAAU,QAKjBC,WAAA,SAAWtO,GAST,OARAA,EAAM6P,EAAA,GACD7C,GACA3C,EAAYI,kBAAkBlF,KAAK2C,UAChB,iBAAXlI,GAAuBA,EAASA,EAAS,IAGtDF,EApJS,QAoJaE,EAAQuF,KAAK4C,YAAYoF,aAExCvN,KAGT40B,cAAA,WAAgB,IAAAxkB,EAAA7K,KACdE,EAAaQ,GAAGV,KAAK2C,SAtJA,yBAuBK,6BA+HiD,WAAA,OAAMkI,EAAK0D,aAGxFqmB,cAAA,WACEzpB,aAAanL,KAAKivB,UAClBjvB,KAAKivB,SAAW,QAKXlrB,gBAAP,SAAuBtJ,GACrB,OAAOuF,KAAKgE,MAAK,WACf,IAAI/G,EAAOK,EAAa0C,KArKb,YA4KX,GAJK/C,IACHA,EAAO,IAAI03B,EAAM30B,KAHe,iBAAXvF,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAI4S,UAAJ,oBAAkC5S,EAAlC,KAGRwC,EAAKxC,GAAQuF,uDAnIjB,OAAOgI,mCAIP,OAAOP,oCAIP,MAtDa,iBAkCXktB,CAAcjyB,UA4JpBlG,GAAmB,WACjB,IAAMmF,EAAIvF,IAEV,GAAIuF,EAAG,CACL,IAAMwC,EAAqBxC,EAAE/B,GAAF,MAC3B+B,EAAE/B,GAAF,MAAa+0B,GAAM5wB,gBACnBpC,EAAE/B,GAAF,MAAWwE,YAAcuwB,GACzBhzB,EAAE/B,GAAF,MAAWyE,WAAa,WAEtB,OADA1C,EAAE/B,GAAF,MAAauE,EACNwwB,GAAM5wB,qBChNJ,CACbd,MAAAA,EACAqB,OAAAA,EACA8D,SAAAA,EACAuF,SAAAA,GACA0X,SAAAA,GACA0B,MAAAA,GACAoL,QAAAA,GACAG,UAAAA,GACA4B,IAAAA,GACAS,MAAAA,GACA5F,QAAAA", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    const hrefAttr = element.getAttribute('href')\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => (obj[0] || obj).nodeType\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ?\n      'element' :\n      toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new Error(\n        `${componentName.toUpperCase()}: ` +\n        `Option \"${property}\" provided type \"${valueType}\" ` +\n        `but expected type \"${expectedTypes}\".`)\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => function () {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = document.documentElement.dir === 'rtl'\n\nexport {\n  TRANSITION_END,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  findShadowRoot,\n  noop,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst mapData = (() => {\n  const storeData = {}\n  let id = 1\n  return {\n    set(element, key, data) {\n      if (typeof element.bsKey === 'undefined') {\n        element.bsKey = {\n          key,\n          id\n        }\n        id++\n      }\n\n      storeData[element.bsKey.id] = data\n    },\n    get(element, key) {\n      if (!element || typeof element.bsKey === 'undefined') {\n        return null\n      }\n\n      const keyProperties = element.bsKey\n      if (keyProperties.key === key) {\n        return storeData[keyProperties.id]\n      }\n\n      return null\n    },\n    delete(element, key) {\n      if (typeof element.bsKey === 'undefined') {\n        return\n      }\n\n      const keyProperties = element.bsKey\n      if (keyProperties.key === key) {\n        delete storeData[keyProperties.id]\n        delete element.bsKey\n      }\n    }\n  }\n})()\n\nconst Data = {\n  setData(instance, key, data) {\n    mapData.set(instance, key, data)\n  },\n  getData(instance, key) {\n    return mapData.get(instance, key)\n  },\n  removeData(instance, key) {\n    mapData.delete(instance, key)\n  }\n}\n\nexport default Data\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            EventHandler.off(element, event.type, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  let typeEvent = originalTypeEvent.replace(stripNameRegex, '')\n  const custom = customEvents[typeEvent]\n\n  if (custom) {\n    typeEvent = custom\n  }\n\n  const isNative = nativeEvents.has(typeEvent)\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = event.replace(stripNameRegex, '')\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.has(typeEvent)\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst VERSION = '5.0.0-beta1'\n\nclass BaseComponent {\n  constructor(element) {\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    Data.setData(element, this.constructor.DATA_KEY, this)\n  }\n\n  dispose() {\n    Data.removeData(this._element, this.constructor.DATA_KEY)\n    this._element = null\n  }\n\n  /** Static */\n\n  static getInstance(element) {\n    return Data.getData(element, this.DATA_KEY)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-bs-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASSNAME_ALERT = 'alert'\nconst CLASSNAME_FADE = 'fade'\nconst CLASSNAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  close(element) {\n    const rootElement = element ? this._getRootElement(element) : this._element\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASSNAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASSNAME_SHOW)\n\n    if (!element.classList.contains(CLASSNAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n\n    EventHandler.one(element, TRANSITION_END, () => this._destroyElement(element))\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Alert.jQueryInterface\n    $.fn[NAME].Constructor = Alert\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Alert.jQueryInterface\n    }\n  }\n})\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery, onDOMContentLoaded } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.getData(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Button.jQueryInterface\n    $.fn[NAME].Constructor = Button\n\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Button.jQueryInterface\n    }\n  }\n})\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.to<PERSON><PERSON><PERSON><PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n\n    Object.keys(element.dataset)\n      .filter(key => key.startsWith('bs'))\n      .forEach(key => {\n        let pureKey = key.replace(/^bs/, '')\n        pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n        attributes[pureKey] = normalizeData(element.dataset[key])\n      })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  matches(element, selector) {\n    return element.matches(selector)\n  },\n\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    const children = [].concat(...element.children)\n\n    return children.filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (this.matches(ancestor, selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (this.matches(next, selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst DIRECTION_NEXT = 'next'\nconst DIRECTION_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst PointerType = {\n  TOUCH: 'touch',\n  PEN: 'pen'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const direction = index > activeIndex ?\n      DIRECTION_NEXT :\n      DIRECTION_PREV\n\n    this._slide(direction, this._items[index])\n  }\n\n  dispose() {\n    super.dispose()\n    EventHandler.off(this._element, EVENT_KEY)\n\n    this._items = null\n    this._config = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    // swipe left\n    if (direction > 0) {\n      this.prev()\n    }\n\n    // swipe right\n    if (direction < 0) {\n      this.next()\n    }\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      if (event.touches && event.touches.length > 1) {\n        this.touchDeltaX = 0\n      } else {\n        this.touchDeltaX = event.touches[0].clientX - this.touchStartX\n      }\n    }\n\n    const end = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    switch (event.key) {\n      case ARROW_LEFT_KEY:\n        event.preventDefault()\n        this.prev()\n        break\n      case ARROW_RIGHT_KEY:\n        event.preventDefault()\n        this.next()\n        break\n      default:\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByDirection(direction, activeElement) {\n    const isNextDirection = direction === DIRECTION_NEXT\n    const isPrevDirection = direction === DIRECTION_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrevDirection && activeIndex === 0) ||\n                            (isNextDirection && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = direction === DIRECTION_PREV ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const indicators = SelectorEngine.find(SELECTOR_ACTIVE, this._indicatorsElement)\n\n      for (let i = 0; i < indicators.length; i++) {\n        indicators[i].classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      const nextIndicator = this._indicatorsElement.children[\n        this._getItemIndex(element)\n      ]\n\n      if (nextIndicator) {\n        nextIndicator.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(direction, element) {\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || (activeElement && this._getItemByDirection(direction, activeElement))\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    let directionalClassName\n    let orderClassName\n    let eventDirectionName\n\n    if (direction === DIRECTION_NEXT) {\n      directionalClassName = CLASS_NAME_START\n      orderClassName = CLASS_NAME_NEXT\n      eventDirectionName = DIRECTION_LEFT\n    } else {\n      directionalClassName = CLASS_NAME_END\n      orderClassName = CLASS_NAME_PREV\n      eventDirectionName = DIRECTION_RIGHT\n    }\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const transitionDuration = getTransitionDurationFromElement(activeElement)\n\n      EventHandler.one(activeElement, TRANSITION_END, () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(() => {\n          EventHandler.trigger(this._element, EVENT_SLID, {\n            relatedTarget: nextElement,\n            direction: eventDirectionName,\n            from: activeElementIndex,\n            to: nextElementIndex\n          })\n        }, 0)\n      })\n\n      emulateTransitionEnd(activeElement, transitionDuration)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-bs-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.getData(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.getData(carousels[i], DATA_KEY))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Carousel.jQueryInterface\n    $.fn[NAME].Constructor = Carousel\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Carousel.jQueryInterface\n    }\n  }\n})\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._isTransitioning = false\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-bs-target=\"#${element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-bs-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.find(elem => container !== elem)\n      activesData = tempActiveData ? Data.getData(tempActiveData, DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.setData(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n\n    emulateTransitionEnd(this._element, transitionDuration)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n    emulateTransitionEnd(this._element, transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    super.dispose()\n    this._config = null\n    this._parent = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(WIDTH) ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    if (isElement(parent)) {\n      // it's a jQuery object\n      if (typeof parent.jquery !== 'undefined' || typeof parent[0] !== 'undefined') {\n        parent = parent[0]\n      }\n    } else {\n      parent = SelectorEngine.findOne(parent)\n    }\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-bs-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (!element || !triggerArray.length) {\n      return\n    }\n\n    const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A') {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.getData(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Collapse.jQueryInterface\n    $.fn[NAME].Constructor = Collapse\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Collapse.jQueryInterface\n    }\n  }\n})\n\nexport default Collapse\n", "export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "/*:: import type { Window } from '../types'; */\n\n/*:: declare function getWindow(node: Node | Window): Window; */\nexport default function getWindow(node) {\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}", "import getWindow from \"./getWindow.js\";\n/*:: declare function isElement(node: mixed): boolean %checks(node instanceof\n  Element); */\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n/*:: declare function isHTMLElement(node: mixed): boolean %checks(node instanceof\n  HTMLElement); */\n\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n/*:: declare function isShadowRoot(node: mixed): boolean %checks(node instanceof\n  ShadowRoot); */\n\n\nfunction isShadowRoot(node) {\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      } // Flow doesn't support to extend this property, but it's the most\n      // effective way to apply styles to an HTMLElement\n      // $FlowFixMe\n\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "// Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\nexport default function getLayoutRect(element) {\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: element.offsetWidth,\n    height: element.offsetHeight\n  };\n}", "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : element.document) || window.document).documentElement;\n}", "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// $FlowFixMe: this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || // DOM Element detected\n    // $FlowFixMe: need a better way to handle this...\n    element.host || // ShadowRoot detected\n    // $FlowFixMe: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  var offsetParent = element.offsetParent;\n\n  if (offsetParent) {\n    var html = getDocumentElement(offsetParent);\n\n    if (getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static' && getComputedStyle(html).position !== 'static') {\n      return html;\n    }\n  }\n\n  return offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var currentNode = getParentNode(element);\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.willChange && css.willChange !== 'auto') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static') {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "export default function within(min, value, max) {\n  return Math.max(min, Math.min(value, max));\n}", "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign(Object.assign({}, getFreshSideObject()), paddingObject);\n}", "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport within from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = state.modifiersData[name + \"#persistent\"].padding;\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element,\n      _options$padding = options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!isHTMLElement(arrowElement)) {\n      console.error(['Popper: \"arrow\" element must be an HTMLElement (not an SVGElement).', 'To use an SVG arrow, wrap it in an HTMLElement that will be used as', 'the arrow.'].join(' '));\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: \"arrow\" modifier\\'s `element` must be a child of the popper', 'element.'].join(' '));\n    }\n\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n  state.modifiersData[name + \"#persistent\"] = {\n    padding: mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements))\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "import { top, left, right, bottom } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsets(_ref) {\n  var x = _ref.x,\n      y = _ref.y;\n  var win = window;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: Math.round(x * dpr) / dpr || 0,\n    y: Math.round(y * dpr) / dpr || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive;\n\n  var _roundOffsets = roundOffsets(offsets),\n      x = _roundOffsets.x,\n      y = _roundOffsets.y;\n\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n    } // $FlowFixMe: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n    /*:: offsetParent = (offsetParent: Element); */\n\n\n    if (placement === top) {\n      sideY = bottom;\n      y -= offsetParent.clientHeight - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left) {\n      sideX = right;\n      x -= offsetParent.clientWidth - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign(Object.assign({}, commonStyles), {}, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) < 2 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign(Object.assign({}, commonStyles), {}, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref3) {\n  var state = _ref3.state,\n      options = _ref3.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive;\n\n  if (process.env.NODE_ENV !== \"production\") {\n    var transitionProperty = getComputedStyle(state.elements.popper).transitionProperty || '';\n\n    if (adaptive && ['transform', 'top', 'right', 'bottom', 'left'].some(function (property) {\n      return transitionProperty.indexOf(property) >= 0;\n    })) {\n      console.warn(['Popper: Detected CSS transitions on at least one of the following', 'CSS properties: \"transform\", \"top\", \"right\", \"bottom\", \"left\".', '\\n\\n', 'Disable the \"computeStyles\" modifier\\'s `adaptive` option to allow', 'for smooth transitions, or remove these properties from the CSS', 'transition declaration on the popper element if only transitioning', 'opacity or background-color for example.', '\\n\\n', 'We recommend using the popper element as a wrapper around an inner', 'element that can have any CSS property transitioned for animations.'].join(' '));\n    }\n  }\n\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign(Object.assign({}, state.styles.popper), mapToStyles(Object.assign(Object.assign({}, commonStyles), {}, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign(Object.assign({}, state.styles.arrow), mapToStyles(Object.assign(Object.assign({}, commonStyles), {}, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false\n    })));\n  }\n\n  state.attributes.popper = Object.assign(Object.assign({}, state.attributes.popper), {}, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "export default function getBoundingClientRect(element) {\n  var rect = element.getBoundingClientRect();\n  return {\n    width: rect.width,\n    height: rect.height,\n    top: rect.top,\n    right: rect.right,\n    bottom: rect.bottom,\n    left: rect.left,\n    x: rect.left,\n    y: rect.top\n  };\n}", "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the \nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = getNodeName(scrollParent) === 'body';\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "export default function rectToClientRect(rect) {\n  return Object.assign(Object.assign({}, rect), {}, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\n\nfunction getInnerBoundingClientRect(element) {\n  var rect = getBoundingClientRect(element);\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element)) : isHTMLElement(clippingParent) ? getInnerBoundingClientRect(clippingParent) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent);\n    accRect.top = Math.max(rect.top, accRect.top);\n    accRect.right = Math.min(rect.right, accRect.right);\n    accRect.bottom = Math.min(rect.bottom, accRect.bottom);\n    accRect.left = Math.max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nexport default function getViewportRect(element) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0; // NB: This isn't supported on iOS <= 12. If the keyboard is open, the popper\n  // can be obscured underneath it.\n  // Also, `html.clientHeight` adds the bottom bar height in Safari iOS, even\n  // if it isn't open, so if this isn't available, the popper will be detected\n  // to overflow the bottom of the screen too early.\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height; // Uses Layout Viewport (like Chrome; Safari does not currently)\n    // In Chrome, it returns a value very close to 0 (+/-) but contains rounding\n    // errors due to floating point numbers, so we need to check precision.\n    // Safari returns a number <= 0, usually < -1 when pinch-zoomed\n    // Feature detection fails in mobile emulation mode in Chrome.\n    // Math.abs(win.innerWidth / visualViewport.scale - visualViewport.width) <\n    // 0.001\n    // Fallback here: \"Not Safari\" userAgent\n\n    if (!/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = element.ownerDocument.body;\n  var width = Math.max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = Math.max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += Math.max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = Math.floor(offsets[mainAxis]) - Math.floor(reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = Math.floor(offsets[mainAxis]) + Math.ceil(reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "import getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var referenceElement = state.elements.reference;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary);\n  var referenceClientRect = getBoundingClientRect(referenceElement);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign(Object.assign({}, popperRect), popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}", "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\n\n/*:: type OverflowsMap = { [ComputedPlacement]: number }; */\n\n/*;; type OverflowsMap = { [key in ComputedPlacement]: number }; */\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements; // $FlowFixMe\n\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: The `allowedAutoPlacements` option did not allow any', 'placements. Ensure the `placement` option matches the variation', 'of the allowed placements.', 'For example, \"auto\" cannot be used to allow \"bottom-start\".', 'Use \"auto-start\" instead.'].join(' '));\n    }\n  } // $FlowFixMe: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign(Object.assign({}, state.attributes.popper), {}, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\";\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign(Object.assign({}, rects), {}, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport within from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign(Object.assign({}, state.rects), {}, {\n    placement: state.placement\n  })) : tetherOffset;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = popperOffsets[mainAxis] + overflow[mainSide];\n    var max = popperOffsets[mainAxis] - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - tetherOffsetValue : minLen - arrowLen - arrowPaddingMin - tetherOffsetValue;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + tetherOffsetValue : maxLen + arrowLen + arrowPaddingMax + tetherOffsetValue;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = state.modifiersData.offset ? state.modifiersData.offset[state.placement][mainAxis] : 0;\n    var tetherMin = popperOffsets[mainAxis] + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = popperOffsets[mainAxis] + maxOffset - offsetModifierValue;\n    var preventedOffset = within(tether ? Math.min(min, tetherMin) : min, offset, tether ? Math.max(max, tetherMax) : max);\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    var _mainSide = mainAxis === 'x' ? top : left;\n\n    var _altSide = mainAxis === 'x' ? bottom : right;\n\n    var _offset = popperOffsets[altAxis];\n\n    var _min = _offset + overflow[_mainSide];\n\n    var _max = _offset - overflow[_altSide];\n\n    var _preventedOffset = within(_min, _offset, _max);\n\n    popperOffsets[altAxis] = _preventedOffset;\n    data[altAxis] = _preventedOffset - _offset;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\"; // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement);\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport getComputedStyle from \"./dom-utils/getComputedStyle.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport validateModifiers from \"./utils/validateModifiers.js\";\nimport uniqueBy from \"./utils/uniqueBy.js\";\nimport getBasePlacement from \"./utils/getBasePlacement.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nimport { auto } from \"./enums.js\";\nvar INVALID_ELEMENT_ERROR = 'Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.';\nvar INFINITE_LOOP_ERROR = 'Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.';\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign(Object.assign({}, DEFAULT_OPTIONS), defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(options) {\n        cleanupModifierEffects();\n        state.options = Object.assign(Object.assign(Object.assign({}, defaultOptions), state.options), options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        }); // Validate the provided modifiers so that the consumer will get warned\n        // if one of the modifiers is invalid for any reason\n\n        if (process.env.NODE_ENV !== \"production\") {\n          var modifiers = uniqueBy([].concat(orderedModifiers, state.options.modifiers), function (_ref) {\n            var name = _ref.name;\n            return name;\n          });\n          validateModifiers(modifiers);\n\n          if (getBasePlacement(state.options.placement) === auto) {\n            var flipModifier = state.orderedModifiers.find(function (_ref2) {\n              var name = _ref2.name;\n              return name === 'flip';\n            });\n\n            if (!flipModifier) {\n              console.error(['Popper: \"auto\" placements require the \"flip\" modifier be', 'present and enabled to work.'].join(' '));\n            }\n          }\n\n          var _getComputedStyle = getComputedStyle(popper),\n              marginTop = _getComputedStyle.marginTop,\n              marginRight = _getComputedStyle.marginRight,\n              marginBottom = _getComputedStyle.marginBottom,\n              marginLeft = _getComputedStyle.marginLeft; // We no longer take into account `margins` on the popper, and it can\n          // cause bugs with positioning, so we'll warn the consumer\n\n\n          if ([marginTop, marginRight, marginBottom, marginLeft].some(function (margin) {\n            return parseFloat(margin);\n          })) {\n            console.warn(['Popper: CSS \"margin\" styles cannot be used to apply padding', 'between the popper and its reference element or boundary.', 'To replicate margin, use the `offset` modifier, as well as', 'the `padding` option in the `preventOverflow` and `flip`', 'modifiers.'].join(' '));\n          }\n        }\n\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(INVALID_ELEMENT_ERROR);\n          }\n\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n        var __debug_loops__ = 0;\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (process.env.NODE_ENV !== \"production\") {\n            __debug_loops__ += 1;\n\n            if (__debug_loops__ > 100) {\n              console.error(INFINITE_LOOP_ERROR);\n              break;\n            }\n          }\n\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(INVALID_ELEMENT_ERROR);\n      }\n\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref3) {\n        var name = _ref3.name,\n            _ref3$options = _ref3.options,\n            options = _ref3$options === void 0 ? {} : _ref3$options,\n            effect = _ref3.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign(Object.assign(Object.assign({}, existing), current), {}, {\n      options: Object.assign(Object.assign({}, existing.options), current.options),\n      data: Object.assign(Object.assign({}, existing.data), current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  getElementFromSelector,\n  isElement,\n  isVisible,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_NAVBAR = 'navbar'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]'\nconst SELECTOR_FORM_CHILD = '.dropdown form'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL ? 'right-start' : 'left-start'\n\nconst Default = {\n  offset: 0,\n  flip: true,\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(number|string|function)',\n  flip: 'boolean',\n  boundary: '(string|element)',\n  reference: '(string|element)',\n  display: 'string',\n  popperConfig: '(null|object)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    Dropdown.clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Totally disable Popper for Dropdowns in Navbar\n    if (!this._inNavbar) {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      this._popper = Popper.createPopper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', null, noop()))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n  }\n\n  dispose() {\n    super.dispose()\n    EventHandler.off(this._element, EVENT_KEY)\n    this._menu = null\n\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(`.${CLASS_NAME_NAVBAR}`) !== null\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          altBoundary: this._config.flip,\n          rootBoundary: this._config.boundary\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...popperConfig,\n      ...this._config.popperConfig\n    }\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown.getParentFromElement(toggles[i])\n      const context = Data.getData(toggles[i], DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!toggles[i].classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event && ((event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName)) ||\n          (event.type === 'keyup' && event.key === TAB_KEY)) &&\n          dropdownMenu.contains(event.target)) {\n        continue\n      }\n\n      const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n      if (hideEvent.defaultPrevented) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children)\n          .forEach(elem => EventHandler.off(elem, 'mouseover', null, noop()))\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      dropdownMenu.classList.remove(CLASS_NAME_SHOW)\n      toggles[i].classList.remove(CLASS_NAME_SHOW)\n      EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || this.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this)\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (event.key === ESCAPE_KEY) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, parent).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    // Up\n    if (event.key === ARROW_UP_KEY && index > 0) {\n      index--\n    }\n\n    // Down\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) {\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  event.stopPropagation()\n  Dropdown.dropdownInterface(this, 'toggle')\n})\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_FORM_CHILD, e => e.stopPropagation())\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Dropdown.jQueryInterface\n    $.fn[NAME].Constructor = Dropdown\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Dropdown.jQueryInterface\n    }\n  }\n})\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  isRTL,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, element)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_FADE)) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, event => this.hide(event))\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n\n    if (transition) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, event => this._hideModal(event))\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    super.dispose()\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._config = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode || this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (transition) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._dialog)\n\n      EventHandler.one(this._dialog, TRANSITION_END, transitionComplete)\n      emulateTransitionEnd(this._dialog, transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    this._backdrop.parentNode.removeChild(this._backdrop)\n    this._backdrop = null\n  }\n\n  _showBackdrop(callback) {\n    const animate = this._element.classList.contains(CLASS_NAME_FADE) ?\n      CLASS_NAME_FADE :\n      ''\n\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (animate) {\n        this._backdrop.classList.add(animate)\n      }\n\n      document.body.appendChild(this._backdrop)\n\n      EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n        } else {\n          this.hide()\n        }\n      })\n\n      if (animate) {\n        reflow(this._backdrop)\n      }\n\n      this._backdrop.classList.add(CLASS_NAME_SHOW)\n\n      if (!animate) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n\n      EventHandler.one(this._backdrop, TRANSITION_END, callback)\n      emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      this._backdrop.classList.remove(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        callback()\n      }\n\n      if (this._element.classList.contains(CLASS_NAME_FADE)) {\n        const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n        EventHandler.one(this._backdrop, TRANSITION_END, callbackRemove)\n        emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else {\n      callback()\n    }\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    const modalTransitionDuration = getTransitionDurationFromElement(this._dialog)\n    EventHandler.off(this._element, TRANSITION_END)\n    EventHandler.one(this._element, TRANSITION_END, () => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      if (!isModalOverflowing) {\n        EventHandler.one(this._element, TRANSITION_END, () => {\n          this._element.style.overflowY = ''\n        })\n        emulateTransitionEnd(this._element, modalTransitionDuration)\n      }\n    })\n    emulateTransitionEnd(this._element, modalTransitionDuration)\n    this._element.focus()\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing =\n      this._element.scrollHeight > document.documentElement.clientHeight\n\n    if ((!this._isBodyOverflowing && isModalOverflowing && !isRTL) || (this._isBodyOverflowing && !isModalOverflowing && isRTL)) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if ((this._isBodyOverflowing && !isModalOverflowing && !isRTL) || (!this._isBodyOverflowing && isModalOverflowing && isRTL)) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n      //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n\n      // Adjust fixed content padding\n      SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n        .forEach(element => {\n          const actualPadding = element.style.paddingRight\n          const calculatedPadding = window.getComputedStyle(element)['padding-right']\n          Manipulator.setDataAttribute(element, 'padding-right', actualPadding)\n          element.style.paddingRight = `${Number.parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n        })\n\n      // Adjust sticky content margin\n      SelectorEngine.find(SELECTOR_STICKY_CONTENT)\n        .forEach(element => {\n          const actualMargin = element.style.marginRight\n          const calculatedMargin = window.getComputedStyle(element)['margin-right']\n          Manipulator.setDataAttribute(element, 'margin-right', actualMargin)\n          element.style.marginRight = `${Number.parseFloat(calculatedMargin) - this._scrollbarWidth}px`\n        })\n\n      // Adjust body padding\n      const actualPadding = document.body.style.paddingRight\n      const calculatedPadding = window.getComputedStyle(document.body)['padding-right']\n\n      Manipulator.setDataAttribute(document.body, 'padding-right', actualPadding)\n      document.body.style.paddingRight = `${Number.parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n    }\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n  }\n\n  _resetScrollbar() {\n    // Restore fixed content padding\n    SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n      .forEach(element => {\n        const padding = Manipulator.getDataAttribute(element, 'padding-right')\n        if (typeof padding !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'padding-right')\n          element.style.paddingRight = padding\n        }\n      })\n\n    // Restore sticky content and navbar-toggler margin\n    SelectorEngine.find(`${SELECTOR_STICKY_CONTENT}`)\n      .forEach(element => {\n        const margin = Manipulator.getDataAttribute(element, 'margin-right')\n        if (typeof margin !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'margin-right')\n          element.style.marginRight = margin\n        }\n      })\n\n    // Restore body padding\n    const padding = Manipulator.getDataAttribute(document.body, 'padding-right')\n    if (typeof padding === 'undefined') {\n      document.body.style.paddingRight = ''\n    } else {\n      Manipulator.removeDataAttribute(document.body, 'padding-right')\n      document.body.style.paddingRight = padding\n    }\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = {\n        ...Default,\n        ...Manipulator.getDataAttributes(this),\n        ...(typeof config === 'object' && config ? config : {})\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  let data = Data.getData(target, DATA_KEY)\n  if (!data) {\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n\n    data = new Modal(target, config)\n  }\n\n  data.show(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Modal.jQueryInterface\n    $.fn[NAME].Constructor = Modal\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Modal.jQueryInterface\n    }\n  }\n})\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attrName)) {\n    if (uriAttrs.has(attrName)) {\n      return Boolean(attr.nodeValue.match(SAFE_URL_PATTERN) || attr.nodeValue.match(DATA_URL_PATTERN))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (attrName.match(regExp[i])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (!allowlistKeys.includes(elName)) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  findShadowRoot,\n  getTransitionDurationFromElement,\n  getUID,\n  isElement,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultAllowlist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: '(null|array)',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  container: false,\n  fallbackPlacements: null,\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element)\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const dataKey = this.constructor.DATA_KEY\n      let context = Data.getData(event.delegateTarget, dataKey)\n\n      if (!context) {\n        context = new this.constructor(event.delegateTarget, this._getDelegateConfig())\n        Data.setData(event.delegateTarget, dataKey, context)\n      }\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n    EventHandler.off(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.config = null\n    this.tip = null\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (this.isWithContent() && this._isEnabled) {\n      const showEvent = EventHandler.trigger(this._element, this.constructor.Event.SHOW)\n      const shadowRoot = findShadowRoot(this._element)\n      const isInTheDom = shadowRoot === null ?\n        this._element.ownerDocument.documentElement.contains(this._element) :\n        shadowRoot.contains(this._element)\n\n      if (showEvent.defaultPrevented || !isInTheDom) {\n        return\n      }\n\n      const tip = this.getTipElement()\n      const tipId = getUID(this.constructor.NAME)\n\n      tip.setAttribute('id', tipId)\n      this._element.setAttribute('aria-describedby', tipId)\n\n      this.setContent()\n\n      if (this.config.animation) {\n        tip.classList.add(CLASS_NAME_FADE)\n      }\n\n      const placement = typeof this.config.placement === 'function' ?\n        this.config.placement.call(this, tip, this._element) :\n        this.config.placement\n\n      const attachment = this._getAttachment(placement)\n      this._addAttachmentClass(attachment)\n\n      const container = this._getContainer()\n      Data.setData(tip, this.constructor.DATA_KEY, this)\n\n      if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n        container.appendChild(tip)\n      }\n\n      EventHandler.trigger(this._element, this.constructor.Event.INSERTED)\n\n      this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n\n      tip.classList.add(CLASS_NAME_SHOW)\n\n      const customClass = typeof this.config.customClass === 'function' ? this.config.customClass() : this.config.customClass\n      if (customClass) {\n        tip.classList.add(...customClass.split(' '))\n      }\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children).forEach(element => {\n          EventHandler.on(element, 'mouseover', noop())\n        })\n      }\n\n      const complete = () => {\n        const prevHoverState = this._hoverState\n\n        this._hoverState = null\n        EventHandler.trigger(this._element, this.constructor.Event.SHOWN)\n\n        if (prevHoverState === HOVER_STATE_OUT) {\n          this._leave(null, this)\n        }\n      }\n\n      if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n        const transitionDuration = getTransitionDurationFromElement(this.tip)\n        EventHandler.one(this.tip, TRANSITION_END, complete)\n        emulateTransitionEnd(this.tip, transitionDuration)\n      } else {\n        complete()\n      }\n    }\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.Event.HIDDEN)\n\n      if (this._popper) {\n        this._popper.destroy()\n        this._popper = null\n      }\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(tip)\n\n      EventHandler.one(tip, TRANSITION_END, complete)\n      emulateTransitionEnd(tip, transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this.config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (typeof content === 'object' && isElement(content)) {\n      if (content.jquery) {\n        content = content[0]\n      }\n\n      // content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.allowList, this.config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this._element.getAttribute('data-bs-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this._element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  updateAttachment(attachment) {\n    if (attachment === 'right') {\n      return 'end'\n    }\n\n    if (attachment === 'left') {\n      return 'start'\n    }\n\n    return attachment\n  }\n\n  // Private\n\n  _getPopperConfig(attachment) {\n    const flipModifier = {\n      name: 'flip',\n      options: {\n        altBoundary: true\n      }\n    }\n\n    if (this.config.fallbackPlacements) {\n      flipModifier.options.fallbackPlacements = this.config.fallbackPlacements\n    }\n\n    const defaultBsConfig = {\n      placement: attachment,\n      modifiers: [\n        flipModifier,\n        {\n          name: 'preventOverflow',\n          options: {\n            rootBoundary: this.config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'onChange',\n          enabled: true,\n          phase: 'afterWrite',\n          fn: data => this._handlePopperPlacementChange(data)\n        }\n      ],\n      onFirstUpdate: data => {\n        if (data.options.placement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      }\n    }\n\n    return {\n      ...defaultBsConfig,\n      ...this.config.popperConfig\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (isElement(this.config.container)) {\n      return this.config.container\n    }\n\n    return SelectorEngine.findOne(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.Event.CLICK, this.config.selector, event => this.toggle(event)\n        )\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this._element, eventIn, this.config.selector, event => this._enter(event))\n        EventHandler.on(this._element, eventOut, this.config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n    const originalTitleType = typeof this._element.getAttribute('data-bs-original-title')\n\n    if (title || originalTitleType !== 'string') {\n      this._element.setAttribute('data-bs-original-title', title || '')\n      if (title && !this._element.getAttribute('aria-label') && !this._element.textContent) {\n        this._element.setAttribute('aria-label', title)\n      }\n\n      this._element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.delegateTarget,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.delegateTarget, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.delegateTarget,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.delegateTarget, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = false\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttr)) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    if (config && typeof config.container === 'object' && config.container.jquery) {\n      config.container = config.container[0]\n    }\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const { state } = popperData\n\n    if (!state) {\n      return\n    }\n\n    this.tip = state.elements.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(state.placement))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Tooltip.jQueryInterface\n    $.fn[NAME].Constructor = Tooltip\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Tooltip.jQueryInterface\n    }\n  }\n})\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery, onDOMContentLoaded } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n                '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this._element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContent() {\n    return this._element.getAttribute('data-bs-content') || this.config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.setData(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Popover.jQueryInterface\n    $.fn[NAME].Constructor = Popover\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Popover.jQueryInterface\n    }\n  }\n})\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n    this._scrollElement = element.tagName === 'BODY' ? window : element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS}, ${this._config.target} ${SELECTOR_LIST_ITEMS}, ${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, event => this._process(event))\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    super.dispose()\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' || scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-bs-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy, Manipulator.getDataAttributes(spy)))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = ScrollSpy.jQueryInterface\n    $.fn[NAME].Constructor = ScrollSpy\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return ScrollSpy.jQueryInterface\n    }\n  }\n})\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE)) ||\n      this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    let hideEvent = null\n\n    if (previous) {\n      hideEvent = EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      })\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented || (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(this._element, listElement)\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(element, active, callback)\n\n    if (active && isTransitioning) {\n      const transitionDuration = getTransitionDurationFromElement(active)\n      active.classList.remove(CLASS_NAME_SHOW)\n\n      EventHandler.one(active, TRANSITION_END, complete)\n      emulateTransitionEnd(active, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    if (element.parentNode && element.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.getData(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n\n  const data = Data.getData(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Tab.jQueryInterface\n    $.fn[NAME].Constructor = Tab\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Tab.jQueryInterface\n    }\n  }\n})\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getTransitionDurationFromElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n\n    super.dispose()\n    this._config = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Toast.jQueryInterface\n    $.fn[NAME].Constructor = Toast\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Toast.jQueryInterface\n    }\n  }\n})\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta1): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"]}