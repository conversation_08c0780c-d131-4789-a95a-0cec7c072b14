function cc_ajax_handle_ajax_action(action_name, data){

    // Unpack
    var selector = "selector" in data ? data.selector : "";
    var selectElem = selector ? jQuery(selector) : null;
    var value = "value" in data ? data.value : "";

    // Debug
    //console.log('/' + new Date().toUTCString() + '/', "cc_ajax_handle_ajax_action [action_name=" + action_name + ", selector='" + selector + "', value_length=" + value.length + "]");

    // Handle actions
    if(action_name === "html"){
        // Set selected element content
        selectElem.html(value);
    }
    else if(action_name === "append_html"){
        // Append selected element
        selectElem.append(value);
    }
    else if(action_name === "html_replace"){
        // Replace selected element
        selectElem.replaceWith(value);
    }
	else if(action_name === "remove_errors"){
        // Replace selected element
		jQuery(selector+" .form-error").removeClass('d-block');
        jQuery(selector+" .is-invalid").removeClass('is-invalid');
    }
	else if(action_name === "show_error") {
        // Replace selected element
		var error_text = "error_text" in data ? data.error_text : "";
		jQuery(selector+" input[name="+value+"], "+selector+" select[name="+value+"]").addClass('is-invalid');

		if(error_text)  {
			jQuery(selector+" input[name="+value+"], "+selector+" select[name="+value+"]").parents(".form-group:first").find(".invalid-feedback:first").html(error_text).removeClass('d-none');
		}
    }
    else if(action_name === "show"){
        // Show element
        selectElem.show();
    }
    else if(action_name === "hide"){
        // Hide element
        selectElem.hide();
    }
    else if(action_name === "modal_open"){
        // Show element
        selectElem.modal('show');
    }
    else if(action_name === "tab_open"){
        // Show element
        selectElem.tab('show');;
    }
    else if(action_name === "add_class"){
        // Add classes
        selectElem.addClass(value);
    }
    else if(action_name === "remove_class"){
        // Remove classes
        selectElem.removeClass(value);
    }
	else if(action_name === "remove"){
        // Remove classes
        selectElem.remove();
    }
	else if(action_name === "update_attr"){
        // Replace selected element
        selectElem.attr(data.attr, value);
    }
    else if(action_name === "val"){
        // Remove classes
        selectElem.val(value);
    }
	else if(action_name === "html_class"){
		var class_name = "class_name" in data ? data.class_name : "";
		selectElem.html(value);
		selectElem.addClass(class_name);
	}
    else if(action_name === "log"){
        console.log("DEBUG MODE", value);
    }
	else if(action_name === "login_user"){
		window.location = SITE_URL;
	}
	else if(action_name === "initialize-copy-btn"){
		//copy to clipboard
		if(jQuery(value).length> 0)  {

			var clipboard = new ClipboardJS(value);
			clipboard.on('success', function(e) {
				var copiedText = jQuery(e.trigger).attr('data-copied-text');
				jQuery(e.trigger).find('span').html(copiedText);

				e.clearSelection();
			});
		}
	}
    else if(action_name === "set_storage"){
        var name = "name" in data ? data.name : "";
		window.localStorage.setItem(name, value);
	}
    else if(action_name === "show-temp-msg"){
		selectElem.removeClass('d-none');
		setTimeout(function() {
			selectElem.addClass('d-none');
		}, 3000);
	}
	else if(action_name === "update-tickets")  {

        value = numberWithDots(parseInt(value));

        jQuery('.cc-user-tickets').html(value);
        jQuery(".cc-buy-ticket-form input[name=num_ticktes]").attr('data-max', value);

        jQuery(".transaction-list-link").attr('data-reload', 'yes').addClass('with-dot');

        /*
        var new_points = "new_points" in data ? data.new_points : "";
        if(new_points)  {
            //console.log(new_points);
            updatePoint(new_points);
        }

        reset_page('buy-ticket');
        */
	}
	else if(action_name === "display_answer"){

		var correct_answer = "correct_answer" in data ? data.correct_answer : "";
		var given_answer = "given_answer" in data ? data.given_answer : "";
		var is_correct = "is_correct" in data ? data.is_correct : "";

		selectElem.find('input[value="'+correct_answer+'"]').parent().attr('data-answer','correct');

        if(!is_correct)  {
			selectElem.find('input[value="'+given_answer+'"]').parent().attr('data-answer','wrong');
		}
	}
	else if(action_name === "logout"){
		window.location = SITE_URL+"/logout";
	}
    else if(action_name === "function_call"){

        window[value]();
	}
}


function cc_ajax(action_name, data, callback){
    if(!is_function(callback)) callback = function(obj){
    };
    if(is_function(data)){
        callback = data;
        data = {};
    }
    if(!is_a(data)) data = {};

	//TODO : remove this test calendar date
	if(jQuery("#test_calendar_date").length > 0)  {
		data.test_calendar_date = jQuery("#test_calendar_date").val();
	}

	var action_url = action_name.indexOf(SITE_URL) > -1 ? action_name : SITE_URL + "/" + action_name;

    jQuery.ajax({
		type      : 'POST',
		url       : action_url,
		headers   : {
			'X-CSRF-TOKEN' : jQuery('meta[name="csrf_token"]').attr('content')
		},
		data      : data,
		dataType  : "json",
		xhrFields : {withCredentials : true},
		success   : function(mdata){
            if(typeof mdata === 'string') mdata = JSON.parse(mdata);
            if(typeof(mdata.logout_now) != "undefined" && mdata.logout_now){
                console.log("Logout now");
                window.location = SITE_URL;
            }
            //console.log("cc_ajax", action_name, data, mdata);
			cc_ajax_callback(mdata, callback);

        },
		error: function(xhr, error){
			if(typeof(_page_loader) != "undefined")
				_page_loader.stop(true, true).fadeOut('fast');

			if(typeof(_sidebar_loader) != "undefined")
				_sidebar_loader.stop(true, true).fadeOut('fast');
		}
	});
    return false;
}

function cc_ajax_callback(mdata, callback){
    if(!is_function(callback)) callback = function(){
    };
    if(mdata){
        if(is_a(mdata.action_queue) && is_function(cc_ajax_handle_ajax_action))
            for(var i = 0; i < mdata.action_queue.length; i++)
                cc_ajax_handle_ajax_action(mdata.action_queue[i][0], mdata.action_queue[i][1]);
        if(is_a(mdata.data)) return callback(mdata.data);
    }
    else console.log("Invalid data received!");
    return callback(false);
}

if(typeof (is_a) !== 'function'){
    function is_a(obj, type){
        if(typeof (type) == 'undefined') return (typeof (obj) != 'undefined'); else return (typeof (obj) == type);
    }
}
if(typeof (is_string) !== 'function'){
    function is_string(possible_type_string){
        return (typeof (possible_type_string) === 'string');
    }
}
if(typeof (is_function) !== 'function'){
    function is_function(possible_type_function){
        return (typeof (possible_type_function) === 'function');
    }
}

jQuery(function(){
    jQuery.fn.serializeObject = function(also_unchecked){
        var o = {};
        var a = this.find(':input').serializeArray();
        if(typeof (also_unchecked) === 'undefined') also_unchecked = true;
        if(also_unchecked){
            this.find(':checkbox:not(:checked)').each(function(){
                if(this_name = jQuery(this).attr('name')){
                    a.push({name : this_name, value : ''});
                }
            });
        }
        jQuery.each(a, function(){
            if(o[this.name] !== undefined){
                if(!o[this.name].push) o[this.name] = [o[this.name]];
                o[this.name].push(this.value || '');
            }
            else o[this.name] = this.value || '';
        });
        return o;
    };
    jQuery.fn.serO = jQuery.fn.serializeObject;
});

function show_temp_error(selector, msg)  {
	if(msg)
		jQuery(selector).html(msg);

	jQuery(selector).removeClass('d-none');
	setTimeout(function() {
		jQuery(selector).addClass('d-none');

		if(msg) {
			jQuery(selector).html('');
		}

	}, 2500);
}
