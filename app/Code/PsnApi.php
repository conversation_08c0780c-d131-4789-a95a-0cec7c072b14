<?php

namespace App\Code;

use Exception;
use App\Http\Models\PsnUsers;
use App\Code\Struct\Trophy;
use App\Code\Struct\TrophyTitle;
use App\Http\Models\Games;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Psr\SimpleCache\InvalidArgumentException;

class PsnApi{
    /**
     * @param $code
     *
     * @return array
     */
    function handleTokenAuth($accessToken){
        try{
            $tokenInfo    = $this->verifyAccessToken($accessToken);
            $tokenMap     = @json_decode($tokenInfo, true);
            $onlineId     = @$tokenMap["online_id"] ?? ""; // Username

	        // Debug
	        /*
	        try{
			        @Log::info("PSN Auth Debug: " . @json_encode($tokenMap) . " - " . @json_encode($accessToken));
	        }
	        catch(Exception | \Throwable $e){
		        sprintf("%s() - (%s) %s @ %s %s", __METHOD__, $e->getCode(), $e->getMessage(), $e->getFile(), $e->getLine());
		        Log::notice(sprintf("%s() - (%s) %s @ %s %s", __METHOD__, $e->getCode(), $e->getMessage(), $e->getFile(), $e->getLine()));
	        }
	        */

            $entitlementData = $this->obtainUserPlusEntitlementData($accessToken, @$tokenMap["user_id"] ?? "");
            $entitlementMap  = @json_decode($entitlementData, true);

            $userUuid     = @$tokenMap["user_uuid"] ?? ""; // Unique
            $countryCode  = @$tokenMap["country_code"] ?? "";
            $languageCode = @$tokenMap["language_code"] ?? "";
            $isSubAccount = @$tokenMap["is_sub_account"] ?? "";

            $userId = @$tokenMap["user_id"] ?? "";
            $dcimId = @$tokenMap["dcim_id"] ?? "";

            $psPlusStatus    = @$entitlementMap["member_type"] ?? "";
			#$psPlusStatus    = "unkown";
            $accessToken  = @json_decode($accessToken, true);
            if(config("app.debug")) Log::debug(sprintf("%s() - %s", __METHOD__, @var_export($accessToken, true)));

            return [
                "onlineId"     => $onlineId,
                "userUuid"     => $userUuid,
                "countryCode"  => $countryCode,
                "languageCode" => $languageCode,
                "isSubAccount" => $isSubAccount,

                "userId" => $userId,
                "dcimId" => $dcimId,

                "psPlusStatus" => $psPlusStatus,
                "accessToken"  => (isset($accessToken["accessToken"]) ? @$accessToken["accessToken"] : @$accessToken["access_token"]),
                "refreshToken" => (isset($accessToken["refreshToken"]) ? @$accessToken["refreshToken"] : @$accessToken["refresh_token"]),
                "expiresIn"    => (isset($accessToken["expiresIn"]) ? @$accessToken["expiresIn"] : @$accessToken["expires_in"]),
            ];
        }
        catch(Exception $e){
            sprintf("%s() - (%s) %s @ %s %s", __METHOD__, $e->getCode(), $e->getMessage(), $e->getFile(), $e->getLine());
            Log::notice(sprintf("%s() - (%s) %s @ %s %s", __METHOD__, $e->getCode(), $e->getMessage(), $e->getFile(), $e->getLine()));
        }
        return [];
    }

    /**
     * @param $code
     *
     *
     * @return array
     */
    function handleAuth($code){
        try{
            $accessToken = $this->getAccessToken($code);
            return $this->handleTokenAuth($accessToken);
        }
        catch(Exception $e){
            Log::notice(sprintf("%s() - (%s) %s @ %s %s", __METHOD__, $e->getCode(), $e->getMessage(), $e->getFile(), $e->getLine()));
        }
        return [];
    }

    /**
     * @param string $onlineId
     * @param string $userUuid
     * @param string $countryCode
     * @param string $languageCode
     * @param string $plus
     * @param string $userLocale
     * @param string $userName
     *
     * @return array
     */
    function fakeAuth($onlineId = "", $userUuid = "", $countryCode = "", $languageCode = "", $plus = "", $userLocale = "", $userName = ""){
        $psPlusStatus = $plus ? "member" : "-";
        if($userLocale){
            $localeCrater = explode("_", $userLocale);
            $countryCode  = $localeCrater[1] ?? "";
            $languageCode = $localeCrater[0] ?? "";
        }
        if($userName){
            $userUuid = $userName . "_fake_uuid";
            $onlineId = $userName;
        }
        if(!$onlineId || !$userUuid || !$countryCode || !$languageCode || !$psPlusStatus)
            return [];
        return [
            "onlineId"     => $onlineId,
            "userUuid"     => $userUuid,
            "countryCode"  => $countryCode,
            "languageCode" => $languageCode,

            "userId" => "1406059599977312345",
            "dcimId" => "ce2fe82d-2c9e-4b58-9cbf-c59fc3a86e45",

            "psPlusStatus" => "unknown",
            "accessToken"  => "aab43e17-z489-457f-ab4b-45d09c465h44",
            "refreshToken" => "",
            "expiresIn"    => 3599,
        ];
    }


    /**
     * @param $code
     *
     * @return mixed
     * @throws Exception
     */
    function getAccessToken($code){
        if(devModeActive()) Log::channel("psnapi")->info(sprintf("PSN_API %s.code: %s", shortClassMethod(__METHOD__), $code));
        $curlhelper = new PsnCurlHelper();
        $curlhelper->setMethod('POST');
        $curlhelper->setAuthorization(config("app.psn_client_id"), config("app.psn_client_secret"));
        $curlhelper->setRequestMessage('grant_type=authorization_code&redirect_uri=' . config("app.psn_redirect_uri") . '&code=' . $code);
        $curlhelper->setUrl("https://auth.api.sonyentertainmentnetwork.com/2.0/oauth/token");
        $success = $curlhelper->execute();
        if($success === false || $curlhelper->getReturnCode() != '200'){
            $curlhelper->end();
            throw new Exception('Obtaining the authentication code failed. (' . $curlhelper->getBody() . ')', $curlhelper->getReturnCode());
        }
        $authenticationCode = $curlhelper->getBody();
        $curlhelper->end();
        if(devModeActive()) Log::channel("psnapi")->info(sprintf("PSN_API %s.ret: %s", shortClassMethod(__METHOD__), var_export($authenticationCode, true)));
        return $authenticationCode;
    }


    /**
     * @param $token
     *
     * @return mixed
     * @throws Exception
     */
    function verifyAccessToken($token){
        $this->logRequest(__METHOD__, ["token" => $token]);
        $auth       = json_decode($token);
        $curlhelper = new PsnCurlHelper();
        $curlhelper->setMethod('GET');
        $curlhelper->setAuthorization(config("app.psn_client_id"), config("app.psn_client_secret"));
        $curlhelper->setUrl("https://auth.api.np.ac.playstation.net/2.0/oauth/token/" . ($auth->accessToken ?? $auth->access_token));
        $success = $curlhelper->execute();
        if($success === false || $curlhelper->getReturnCode() != '200'){
            $curlhelper->end();
            throw new Exception('Access token verification failed.', $curlhelper->getReturnCode());
        }
        $validatedInfo = $curlhelper->getBody();
        $curlhelper->end();
        $this->logRequest(__METHOD__, ["validatedInfo" => $validatedInfo]);
        return $validatedInfo;
    }


    /**
     * @param PsnUsers $psnUser
     *
     * @return TrophyTitle[]|null
     * @throws InvalidArgumentException
     */
    function getUserDetail(PsnUsers $psnUser){

        if($psnUser->fake_user)  {
            return $this->fakeUserDetail($psnUser);
        }
        else  {
            // Fetch user profile
            $userProfile = json_decode(PsnApi::executeUrl("GET", "https://us-prof.np.community.playstation.net/userProfile/v1/users/me/profile2?fields=npId,onlineId,avatarUrls,plus,aboutMe,languagesUsed,trophySummary(@default,progress,earnedTrophies),isOfficiallyVerified,personalDetailSharing,personalDetailSharingRequestMessageFlag,primaryOnlineStatus,presences(@titleInfo,hasBroadcastData),friendRelation,requestMessageFlag,blocking,mutualFriendsCount,following,followerCount,friendsCount,followingUsersCount&avatarSizes=m,xl&profilePictureSizes=m,xl&languagesUsedLanguageSet=set3&psVitaTitleIcon=circled&titleIconSize=s", $psnUser->getAccessToken()), true);
        }


        \Illuminate\Support\Facades\Log::info(sprintf("PSN_API %s.userProfile: %s", shortClassMethod(__METHOD__), @json_encode($userProfile)));

        return $userProfile;
    }


    /**
     * @param PsnUsers $psnUser
     *
     * @return TrophyTitle[]|null
     * @throws InvalidArgumentException
     */
    function getTrophyGames(PsnUsers $psnUser){
        if($psnUser->fake_user)
            return $this->fakeTrophyGames($psnUser);
        else
            return $this->getTrophyTitles($psnUser->getAccessToken(), $psnUser->online_id, $psnUser);
    }

    /**
     * @param string        $accessToken
     * @param string        $onlineId
     * @param PsnUsers|null $psnUser
     *
     * @return TrophyTitle[]|null
     * @throws InvalidArgumentException
     */
    function getTrophyTitles($accessToken = "", $onlineId = "", PsnUsers $psnUser = null){
        $this->logRequest(__METHOD__, ["accessToken" => $accessToken]);

        // ---
        $baseUrl      = $this->getTrophyBaseUrl($accessToken);
        $trophyTitles = [];
        $i            = 0;
        $limit        = 128;
        $offset       = 0;
        while(true){
            $data   = [
                "npLanguage"      => "de",
                "platform"        => "PS4",
                "fields"          => "@default",
                "comparedUser"    => $onlineId,
                "returnUrlScheme" => "https",
                "limit"           => $limit,
                "offset"          => $offset,
            ];
            $result = $this->executeUrl("GET", $baseUrl . "/v1/trophyTitles/?"
                . http_build_query($data), $accessToken);
            $value  = @json_decode($result, true);
            $titles = $value["trophyTitles"] ?: [];
            if($titles){
                $trophyTitles = array_merge($trophyTitles, $titles);
                $offset       += $limit;
            }
            else break;
            if(@$value["totalResults"] == @$value["limit"]) break;
            if(++$i > 20) break;
        }

        // ---
        $this->logRequest(__METHOD__, ["value" => $value]);

        /* @var TrophyTitle[] $outData */
        $outData = [];
        foreach($trophyTitles as $trophyTitle) $outData[] = new TrophyTitle($trophyTitle, $psnUser);
        return $outData;
    }


    /**
     * @param string $accessToken
     *
     * @return mixed
     * @throws InvalidArgumentException
     * @throws Exception
     */
    function getTrophyBaseUrl($accessToken = ""){

        if($this->getCached(__METHOD__, false)) return $this->getCached(__METHOD__, false);
        $this->logRequest(__METHOD__, ["accessToken" => $accessToken]);
        // ---

        $result = $this->executeUrl("GET", "https://asm.np.community.playstation.net/asm/v1/apps/me/baseUrls/trophy", $accessToken);
        $value  = json_decode($result)->{'url'};

        // ---
        $this->logRequest(__METHOD__, ["baseURL" => $value]);
        $this->setCached(__METHOD__, $value, false);
        return $value;
    }

    /**
     * @param PsnUsers $psnUser
     *
     * @return TrophyTitle[]|null
     */
    function fakeTrophyGames(PsnUsers $psnUser){

        $trophies = (object)cache()->store("array")->get("tmp_psn_trophies");

        // Fetch 3x random games with np_communication_id's
        $games           = Games::where("np_communication_id", "!=", "")->inRandomOrder()->limit(3)->get();
        $games           = $games ?: Games::inRandomOrder()->limit(3)->get();
        $outTrophyTitles = [];
        $amountGames     = count($games);
        $bronzePerGame   = $trophies->bronze > 0 ? $trophies->bronze / $amountGames : 0;
        $silverPerGame   = $trophies->silver > 0 ? $trophies->silver / $amountGames : 0;
        $goldPerGame     = $trophies->gold > 0 ? $trophies->gold / $amountGames : 0;
        $platinumPerGame = $trophies->platinum > 0 ? $trophies->platinum / $amountGames : 0;
        $bronzeLeft      = $trophies->bronze;
        $silverLeft      = $trophies->silver;
        $goldLeft        = $trophies->gold;
        $platinumLeft    = $trophies->platinum;

        foreach($games as $game){
            $totalBronze   = 999;
            $totalSilver   = 999;
            $totalGold     = 999;
            $totalPlatinum = 999;

            $earnedBronze = (int)ceil($bronzePerGame);
            if($earnedBronze >= $bronzeLeft) $earnedBronze = $bronzeLeft;
            $bronzeLeft -= $earnedBronze;

            $earnedSilver = (int)ceil($silverPerGame);
            if($earnedSilver >= $silverLeft) $earnedSilver = $silverLeft;
            $silverLeft -= $earnedSilver;

            $earnedGold = (int)ceil($goldPerGame);
            if($earnedGold >= $goldLeft) $earnedGold = $goldLeft;
            $goldLeft -= $earnedGold;

            $earnedPlatinum = (int)ceil($platinumPerGame);
            if($earnedPlatinum > $platinumLeft) $earnedPlatinum = $platinumLeft;
            $platinumLeft -= $earnedPlatinum;

            $totalSum  = $totalBronze + $totalSilver + $totalGold + $totalPlatinum;
            $earnedSum = $earnedBronze + $earnedSilver + $earnedGold + $earnedPlatinum;
            $progress  = $totalSum > 0 ? $earnedSum / $totalSum * 100 : 0;

            $outTrophyTitles[] = new TrophyTitle([
                "npCommunicationId"   => $game->np_communication_id,
                "trophyTitleName"     => "Faked: " . $game->game_name,
                "trophyTitleDetail"   => "This is a fake debugging trophy entry",
                "trophyTitleIconUrl"  => $game->game_image,
                "trophyTitlePlatfrom" => "ps4",
                "hasTrophyGroups"     => false,
                "definedTrophies"     => [
                    "bronze"   => $totalBronze,
                    "silver"   => $totalSilver,
                    "gold"     => $totalGold,
                    "platinum" => $totalPlatinum,
                ],
                "fromUser"            => [
                    "onlineId"       => $psnUser->online_id,
                    "progress"       => $progress,
                    "earnedTrophies" => [
                        "bronze"   => $earnedBronze,
                        "silver"   => $earnedSilver,
                        "gold"     => $earnedGold,
                        "platinum" => $earnedPlatinum,
                    ],
                    "hiddenFlag"     => false,
                    "lastUpdateDate" => date("Y-m-d H:i:s", current_time()),
                ],

            ], $psnUser);
        }
        return $outTrophyTitles;
    }


    /**
     * @param PsnUsers $psnUser
     *
     * @return TrophyTitle[]|null
     */
    function fakeUserDetail(PsnUsers $psnUser){

        $trophies = (object)cache()->store("array")->get("tmp_user_trophies");

        $outUserDetails = [];

        $outUserDetails['profile'] = [
            "onlineId"   => $psnUser->online_id,
            "trophySummary"     => [
                //"level" => $trophies->level,
                "progress" => 0,
                "earnedTrophies" => array(
                    "bronze"   => $trophies->bronze,
                    "silver"   => $trophies->silver,
                    "gold"     => $trophies->gold,
                    "platinum" => $trophies->platinum,
                )
            ]
        ];

        return $outUserDetails;
    }


    /**
     * @param string $method
     * @param string $url
     * @param string $accessToken
     *
     * @return mixed
     * @throws Exception
     */
    function executeUrl($method = "GET", $url = "", $accessToken = ""){
        $curlhelper = new PsnCurlHelper();
        $curlhelper->setMethod($method);
        $curlhelper->setUrl($url);

        $headers = ['Authorization: Bearer ' . $accessToken];
        if($debugRateLimit = false) $headers[] = "X-NP-RateLimit: GetDetail";

        $curlhelper->setHeader($headers);
        $curlhelper->execute();
        $returnCode = $curlhelper->getReturnCode();
        if($returnCode < 200 || $returnCode >= 300){
            $psnUserSess = session()->get("psn_user");
            $onlineId    = @$psnUserSess["onlineId"] ?? "-";
            Log::channel("psn_api_error")->error(sprintf("%s - %s - %s - %s - %s - %s - %s", $curlhelper->getErrorMsg(),
                $curlhelper->getReturnCode(), $curlhelper->getBody(), $returnCode, $accessToken, $onlineId, $url));
            throw new Exception("Curl request error for access_token=" . $accessToken . " - " .
                $curlhelper->getErrorMsg() . " - " . $curlhelper->getReturnCode() . " - " . $curlhelper->getBody(), $returnCode);
        }
        $curlhelper->end();
        $body = $curlhelper->getBody();

        $this->logRequest(__METHOD__, ["body" => $body, "headers" => $curlhelper->getHeader()]);

        return $body;
    }

    /**
     * @param string $method
     * @param string $info
     */
    function logRequest($method = "", $info = ""){
        if(devModeActive()) Log::channel("psnapi")->info(sprintf("PSN_API %s: %s", shortClassMethod($method), @var_export($info, true)));
    }

    /**
     * @param string $key
     * @param bool   $runtime
     *
     * @return mixed|null
     * @throws InvalidArgumentException
     */
    function getCached($key = "", $runtime = true){
        if(!$runtime){
            if(cache()->has($key)) return cache()->get($key);
            else return null;
        }
        if(cache()->store(($runtime ? "array" : "database"))->has($key)) return cache()->store(($runtime ? "array" : "database"))->get($key);
        return null;
    }

    /**
     * @param string $key
     * @param string $value
     * @param bool   $runtime
     *
     * @throws InvalidArgumentException
     */
    function setCached($key = "", $value = "", $runtime = true){
        if(!$runtime){
            cache()->set($key, $value);
            return;
        }
        cache()->store(($runtime ? "array" : "database"))->set($key, $value);
    }

    /**
     * @param $token
     *
     * @return mixed
     * @throws \Exception
     */
    function getEntitlementBaseUrl($token){
        if(devModeActive()) Log::channel("psnapi")->info(sprintf("PSN_API %s.token: %s",
            shortClassMethod(__METHOD__), $token));
        $auth       = json_decode($token);
        $curlhelper = new PsnCurlHelper();
        $curlhelper->setMethod('GET');
        $curlhelper->setUrl("https://asm.np.community.playstation.net/asm/v1/apps/me/baseUrls/entitlement");
        $curlhelper->setHeader(array('Authorization: Bearer ' . ($auth->accessToken ?? $auth->access_token)));
        $curlhelper->execute();
        $returnCode = $curlhelper->getReturnCode();
        if($returnCode < 200 || $returnCode >= 300)
            throw new \Exception("Couldn't get entitlement base url. - " . ($auth->accessToken ?? $auth->access_token) . " - " .
                $curlhelper->getErrorMsg(), $returnCode);
        $curlhelper->end();
        $baseURL = json_decode($curlhelper->getBody())->{'url'};
        if(devModeActive()) Log::channel("psnapi")->info(sprintf("PSN_API %s.ret: %s",
            shortClassMethod(__METHOD__), $baseURL));
        return $baseURL;
    }

    /**
     * @param $token
     * @param $account_id
     *
     * @return mixed
     * @throws \Exception
     */
    function obtainUserPlusEntitlementData($token, $account_id){
        if(devModeActive()) Log::channel("psnapi")->info(sprintf("PSN_API %s.token.account_id: %s %s",
            shortClassMethod(__METHOD__), $token, $account_id));
        if(!Cache::store('file')->has("getEntitlementBaseUrl")){
            $cached = false;
            Cache::store('file')->put("getEntitlementBaseUrl", $this->getEntitlementBaseUrl($token), now()->addMinutes(30));
        }
        else $cached = true;
        $baseurl = Cache::store('file')->get("getEntitlementBaseUrl");
        if(devModeActive()) Log::channel("psnapi")->info(sprintf("PSN_API %s.cache: %s -> %s",
            shortClassMethod(__METHOD__), var_export(["is_cached" => $cached], true), $baseurl));
        $auth       = json_decode($token);
        $curlhelper = new PsnCurlHelper();
        $curlhelper->setMethod('GET');
        $curlhelper->setHeader(array('Authorization: Bearer ' . ($auth->accessToken ?? $auth->access_token)));
        $curlhelper->setUrl($baseurl . '/v1/users/me/psnsubscriptions');
        $success = $curlhelper->execute();
        if($success === false || $curlhelper->getReturnCode() < 200 || $curlhelper->getReturnCode() >= 300){
            $curlhelper->end();
            throw new \Exception("Couldn't obtain entitlement data. - " . $baseurl .
                '/v1/users/me/psnsubscriptions' . ' - ' . $curlhelper->getErrorMsg() . " - " . $account_id .
                " - " . $curlhelper->getBody(), $curlhelper->getReturnCode());
        }
        $accountInfo = $curlhelper->getBody();
        $curlhelper->end();
        if(devModeActive()) Log::channel("psnapi")->info(sprintf("PSN_API %s.ret: %s", shortClassMethod(__METHOD__), $accountInfo));
        return $accountInfo;
    }
}
