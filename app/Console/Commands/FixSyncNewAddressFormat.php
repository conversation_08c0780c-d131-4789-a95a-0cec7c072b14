<?php

namespace App\Console\Commands;

use App\Code\External\Api\SalesforceMarketingApi;
use App\Http\Models\Api\Address;
use App\Http\Models\Api\Country;
use App\Http\Models\Api\User;
use Illuminate\Console\Command;

class FixSyncNewAddressFormat extends Command{
    protected $signature   = 'fix_sync_new_address_format';
    protected $description = 'Fix Sync New Address Format';

    public function handle(){
        ini_set('max_execution_time', '3000');
        ini_set('memory_limit', '2048M');

        // Select all users
        $users = User::all();

        foreach($users as $user){
            $this->syncUser($user);
        }
    }

    public function syncUser($user = null){
        /* @var Address $address */
        $address = $user->addresses()->first();
        if(!$address){
            $this->info("Skip user [" . $user->member_id . ":" . $user->id . "] -> no address");
            return;
        }

        /* @var Country $country */
        $country = $user->country_id ? Country::where("id", $user->country_id)->first() : false;
        if(!$country){
            $this->info("Skip user [" . $user->member_id . ":" . $user->id . "] -> no country");
            return;
        }


        // Create new user with salesforce api
        $salesforceData = [
            "salesforceId"             => $user->salesforce_id,
            "email"                    => $user->email,
            "title"                    => $user->title,
            "titleAfter"               => $user->title_after,
            "street"                   => getSfStreet($address->street, $address->street_number, $address->street_additional),
            "postalCode"               => $address->post_code,
            "mobile"                   => $user->mobile,
            "lastName"                 => $user->last_name,
            "gender"                   => @["m" => "MALE", "f" => "FEMALE", "d" => "UNKNOWN"][$user->gender],
            "firstName"                => $user->first_name,
            "country"                  => $country->getLanguageCode(),
            "city"                     => $address->city,
            "marketingNewsOptOut"      => !$user->newsletter_sub,
            "nlClubNewsOptOut"         => !$user->club_newsletter_sub,
            "salutation"               => $user->salutation ?: "",
            "isEmployee"               => (bool)$user->employee_info_id,
            "isAppDownloaded"          => (bool)$user->app_is_downloaded,
            "pushAllowed"              => (bool)$user->push_allowed,
        ];
        if($user->day_of_birth && (correctDate($user->day_of_birth) ||
                correctDate($user->day_of_birth, "j.n.Y")))
            $salesforceData["birthdate"] = date("Y-m-d", strtotime($user->day_of_birth));

        if($user->day_of_birth && (correctDate($user->day_of_birth) ||
                correctDate($user->day_of_birth, "j.n.Y")))
            $salesforceData["birthdate"] = date("Y-m-d", strtotime($user->day_of_birth));

        $responseData = app(SalesforceMarketingApi::class)->createOrUpdateAccount($salesforceData);
        $this->info("RESULT -> " . @json_encode($responseData));

        // Check changed status
        $data = app(SalesforceMarketingApi::class)->getAccountViaCardNum($user->member_id);
        $data = $data["accountInfo"] ?? [];
        if(!$data){
            $this->info("User [" . $user->member_id . ":" . $user->id . "] has no api data. (Card not found?)" . PHP_EOL);
            return;
        }

        if(@trim(@$data["street"])){
            $this->comment("User [" . $user->member_id . "(" . @$data["cardNumber"] . ")" . ":" . $user->id . "] is fixed=true " . @$data["street"] . PHP_EOL);
        }
    }
}
