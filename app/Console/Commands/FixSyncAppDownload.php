<?php

namespace App\Console\Commands;

use App\Code\External\Api\SalesforceMarketingApi;
use App\Http\Models\Api\Address;
use App\Http\Models\Api\Country;
use App\Http\Models\Api\User;
use Illuminate\Console\Command;

class FixSyncAppDownload extends Command{
    protected $signature   = 'fix_sync_app_download';
    protected $description = 'Fix Sync App download';

    public function handle(){
        ini_set('max_execution_time', '3000');
        ini_set('memory_limit', '2048M');

        // Select all users before xy
        $users = User::where("created_at", "<", date("Y-m-d H:i:s", @strtotime("2021-05-14 10:00:00")))
                     ->get();
        foreach($users as $user){
            $this->syncUser($user);
        }
    }

    public function syncUser($user = null){
        /* @var Address $address */
        $address = $user->addresses()->first();
        if(!$address){
            $this->info("Skip user -> no address");
            return;
        }

        /* @var Country $country */
        $country = $user->country_id ? Country::where("id", $user->country_id)->first() : false;
        if(!$country){
            $this->info("Skip user [" . $user->member_id . ":" . $user->id . "] -> no country");
            return;
        }
        $oldAppDownloaded = $user->app_is_downloaded;

        if(!$user->app_is_downloaded && stripos($user->source_of_registration, "Mobile App") !== false){
            $user->app_is_downloaded = true;
            $user->save();
        }
        elseif(!$user->app_is_downloaded && stripos($user->source_of_registration, "Mobile App") === false){
            $this->info("Skip user [" . $user->member_id . ":" . $user->id . "] -> No app download and source is " .
                var_export($user->source_of_registration, true));
            return;
        }

        if($oldAppDownloaded != $user->app_is_downloaded){
            $this->info("User [" . $user->member_id . ":" . $user->id . "] has new app_is_downloaded status! old=" .
                ($oldAppDownloaded ? "true" : "false") . ", new=" . ($user->app_is_downloaded ? "true" : "false"));
        }

        $salesforceData = [
            "salesforceId"             => $user->salesforce_id,
            "email"                    => $user->email,
            "title"                    => $user->title,
            "titleAfter"               => $user->title_after,
            "street"                   => getSfStreet($address->street, $address->street_number, $address->street_additional),
            "postalCode"               => $address->post_code,
            "mobile"                   => $user->mobile,
            "lastName"                 => $user->last_name,
            "gender"                   => @["m" => "MALE", "f" => "FEMALE", "d" => "UNKNOWN"][$user->gender],
            "firstName"                => $user->first_name,
            "country"                  => $country->getLanguageCode() ?? "",
            "city"                     => $address->city,
            "personHasOptedOutOfEmail" => false,
            "marketingNewsOptOut"      => $user->newsletter_sub ? false : true,
            "nlClubNewsOptOut"         => $user->club_newsletter_sub ? false : true,
            "salutation"               => $user->salutation ?: "",
            "isEmployee"               => $user->employee_info_id ? true : false,
            "isAppDownloaded"          => (bool)$user->app_is_downloaded,
            "pushAllowed"              => $user->push_allowed ? true : false,
        ];

        if($user->day_of_birth && (correctDate($user->day_of_birth) ||
                correctDate($user->day_of_birth, "j.n.Y")))
            $salesforceData["birthdate"] = date("Y-m-d", strtotime($user->day_of_birth));

        $responseData = app(SalesforceMarketingApi::class)->createOrUpdateAccount($salesforceData);

        // Check changed status
        $data = app(SalesforceMarketingApi::class)->getAccountViaCardNum($user->member_id);
        $data = $data["accountInfo"] ?? [];
        if(!$data){
            $this->info("User [" . $user->member_id . ":" . $user->id . "] has no api data.");
            return;
        }

        if(@$data["isAppDownloaded"] == $user->app_is_downloaded)
            $this->info("User [" . $user->member_id . ":" . $user->id . "] isAppDownloaded=true");
        else
            $this->info("User [" . $user->member_id . ":" . $user->id . "] isAppDownloaded=false");
    }
}
