<?php

namespace App\Console\Commands;

use App\_modules\ErrorNotifier\ErrorNotifier;
use App\Http\Models\Category;
use App\Http\Models\Product;
use Framework\src\Http\Models\Data;
use Framework\src\Http\Models\DataRevision;
use Illuminate\Console\Command;
use Illuminate\Log\Events\MessageLogged;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use JsonException;

class CronScrapeNow extends Command{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cron_scrape_now';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cron Scrape Now';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(){
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     * @throws JsonException
     */
    public function handle(){
        @ini_set('memory_limit', '8G');
        $maxWorkerCount = 30;
        $maxWorkerCount+= 1; // +1 for pagination
        $batchSize = 1000;

        // Output log to console
        $that = $this;
        Log::getLogger();
        Log::listen(static function (MessageLogged $message) use (&$results, $that){
            if(stripos($message->message, "SQL") === false){
                $that->comment(date("Y-m-d H:i:s") . " " . $message->message);
            }
        });

        $shouldRun = Data::getNoCache("CronScrapeNow_StartNow", 0);
        if(!$shouldRun){
            $this->info("no should run");
            return;
        }
        $currentWorkerStarted = Data::getNoCache("CronScrapeNow_CurrentWorkerStarted", 1);
        if($currentWorkerStarted >= $maxWorkerCount){
            $this->info("Max worker amount reached");
            Data::setNoCache("CronScrapeNow_StartNow", 0);
            return;
        }
        $lastStart = Data::getNoCache("CronScrapeNow_LastStart", 0);
        if($currentWorkerStarted >= $maxWorkerCount && $lastStart > time() - (60 * 5)){
            $this->info("no time run");
            return;
        }
        $currentWorkerStarted += 1;
        Data::setNoCache("CronScrapeNow_CurrentWorkerStarted", $currentWorkerStarted);
        Data::setNoCache("CronScrapeNow_LastStart", time());
        $this->info(__METHOD__ . " Now running " . $shouldRun . " Last Run Sec Ago: " . (time() - $lastStart));
        Log::info(__CLASS__ . " Running now");

        if($currentWorkerStarted <= 2){
            Category::query()->update(["last_detection" => null]);
            Product::query()->update(["last_scraped" => null]);
            Log::info(__CLASS__ . " cron_scrape_pagination() Start worker number " . ($currentWorkerStarted - 1));
            $this->callCron("cron_scrape_pagination");
        }
        else{
            $from = ($currentWorkerStarted - 2) * $batchSize;
            if($currentWorkerStarted >= $maxWorkerCount){
                $to = 999999;
            }
            else{
                $to = ($currentWorkerStarted - 2) * $batchSize + $batchSize;
            }
            Log::info(__CLASS__ . " Start processing range from {$from} to {$to}");
            $this->callCron("cron_scrape_missing_detail_pages --from={$from} --to={$to}");
        }

        DataRevision::incrementRevision();
        app(ErrorNotifier::class)->sendOutErrorReport();
    }

    public function callCron($signature = ""){
        $this->comment(__METHOD__ . " - " . $signature . " - Start");
        $timeStart = microtime(true);
        Artisan::call($signature);
        $timeElapsedSec = microtime(true) - $timeStart;
        $this->comment(__METHOD__ . " - " . $signature . " - Done - Took: " . $timeElapsedSec);
    }
}
