<?php

namespace App\Excel;

use App\Http\Models\TwitterYearlyData;
use App\Http\Models\TwitterMonthlyData;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use TypeError,Session;

class TwitterReportAnalyticsImport implements ToCollection  {

    public $time_status = '';

	public function __construct($time_status='')  {

        $this->time_status = $time_status;
    }
    
    /**
     * @param array $rows
     *
     * @return User|null
    */
	public function collection(Collection $rows)  {

		//validation
		if(trim($rows[0][0]) != 'Tweet ID') {
			exit('Wrong first column name');
		}
		
        if($this->time_status == 'year')
            TwitterYearlyData :: where('year', 'temp-data')->delete();
        else if($this->time_status == 'month')
            TwitterMonthlyData :: where('month', 'temp-data')->delete();

        //import records
        foreach($rows as $ri => $row)  {

            $impressions = (int) sanitizeStr($row[4]);

            if(trim($row[0]) && $row[0] != 'Tweet ID')  {
				
                try {

                    //insert record
                    if($this->time_status == 'year')  {

                        $monthEntry = new TwitterYearlyData();
                        $monthEntry->year = 'temp-data';

                        $publish_month = date('m', strtotime($row[1]));
                        if(in_array($publish_month, [1,2,3]))
                            $monthEntry->quarter = 4;
                        else if(in_array($publish_month, [4,5,6]))
                            $monthEntry->quarter = 1;
                        else if(in_array($publish_month, [7,8,9]))
                            $monthEntry->quarter = 2;
                        else if(in_array($publish_month, [10,11,12]))
                            $monthEntry->quarter = 3;
                    }
                    else if($this->time_status == 'month' )  {

                        $monthEntry = new TwitterMonthlyData();
                        $monthEntry->month = 'temp-data';
                    }
                    
                    $monthEntry->post_id = sanitizeStr($row[0]);
                    $monthEntry->post_link = sanitizeStr($row[1]);
                    $monthEntry->post_text = sanitizeHTMLStr($row[2]);
                    $monthEntry->publish_date = date('Y-m-d H:i:s', strtotime($row[3]));

                    $monthEntry->impressions = $impressions;
                    $monthEntry->interactions = sanitizeStr($row[5]);
                    $monthEntry->interaction_rate = round(sanitizeStr($row[6]) * 100, 2);

                    $monthEntry->post_retweets = (int) sanitizeStr($row[7]);
                    $monthEntry->post_answers = (int) sanitizeStr($row[8]);
                    $monthEntry->post_likes = (int) sanitizeStr($row[9]);
                    $monthEntry->user_profile_clicks = (int) sanitizeStr($row[10]);
                    $monthEntry->post_clicks = (int) sanitizeStr($row[11]);
                    $monthEntry->hashtag_clicks = (int) sanitizeStr($row[12]);
                    $monthEntry->detail_expansions = (int) sanitizeStr($row[13]);
                    $monthEntry->follows = (int) sanitizeStr($row[17]);
                    $monthEntry->media_interactions = (int) sanitizeStr($row[21]);

                    $monthEntry->permalink_clicks = (int) sanitizeStr($row[14]);
                    $monthEntry->app_opens = (int) sanitizeStr($row[15]);
                    $monthEntry->app_installs = (int) sanitizeStr($row[16]);
                    $monthEntry->email_tweet = (int) sanitizeStr($row[18]);
                    $monthEntry->dialed_phone = (int) sanitizeStr($row[19]);
                    $monthEntry->media_views = (int) sanitizeStr($row[20]);

                    $monthEntry->ctr = $impressions > 0 ? round(($monthEntry->post_clicks / $impressions) * 100, 2) : 0;
                    
                    $monthEntry->save();
                }
                catch(TypeError $e)  {
                    echo "Row ".($ri + 1)." has issue!";
                    exit();
                }
			}
        }

        if($this->time_status == 'year')  {

            $sum_record = TwitterYearlyData :: selectRaw("
                ROUND(AVG(impressions)) as avg_impressions, MIN(impressions) as min_impressions, MAX(impressions) as max_impressions, 
                ROUND(AVG(interactions), 2) as avg_interactions, MIN(interactions) as min_interactions, MAX(interactions) as max_interactions, 
                ROUND(AVG(interaction_rate), 2) as avg_ir, MIN(interaction_rate) as min_ir, MAX(interaction_rate) as max_ir
            ")->where('year', 'temp-data')->first();
        }

        if($this->time_status == 'month')  {

            $sum_record = TwitterMonthlyData :: selectRaw("
                ROUND(AVG(impressions)) as avg_impressions, MIN(impressions) as min_impressions, MAX(impressions) as max_impressions, 
                ROUND(AVG(interactions)) as avg_interactions, MIN(interactions) as min_interactions, MAX(interactions) as max_interactions, 
                ROUND(AVG(interaction_rate), 2) as avg_ir, MIN(interaction_rate) as min_ir, MAX(interaction_rate) as max_ir
            ")->where('month', 'temp-data')->first();
        }

        //impression
        $diff_min = $sum_record->avg_impressions - $sum_record->min_impressions;
        $diff_max = $sum_record->max_impressions - $sum_record->avg_impressions;

        $min_imp = $sum_record->min_impressions + round($diff_min / 5);
        $max_imp = $sum_record->max_impressions - round($diff_max / 5);

        //interactions
        $diff_min = $sum_record->avg_interactions - $sum_record->min_interactions;
        $diff_max = $sum_record->max_interactions - $sum_record->avg_interactions;
        
        $min_interactions = $sum_record->min_interactions + round($diff_min / 5);
        $max_interactions = $sum_record->max_interactions - round($diff_max / 5);
        
        //interaction_rate
        $diff_min = $sum_record->avg_ir - $sum_record->min_ir;
        $diff_max = $sum_record->max_ir - $sum_record->avg_ir;
        
        $min_ir = $sum_record->min_ir + round($diff_min / 4, 2);
        $max_ir = $sum_record->max_ir - round($diff_max / 4, 2);

        if($this->time_status == 'year' )  {

            TwitterYearlyData :: where('year', 'temp-data')
                ->whereRaw("(impressions <= ? OR impressions >= ? OR interactions <= ? OR interactions >= ? OR interaction_rate <= ? OR interaction_rate >= ?)", [$min_imp, $max_imp, $min_interactions, $max_interactions, $min_ir, $max_ir])
                ->update(["is_error" => 1]);
        }
        else if($this->time_status == 'month')  {

            TwitterMonthlyData :: where('month', 'temp-data')
                ->whereRaw("(impressions <= ? OR impressions >= ? OR interactions <= ? OR interactions >= ? OR interaction_rate <= ? OR interaction_rate >= ?)", [$min_imp, $max_imp, $min_interactions, $max_interactions, $min_ir, $max_ir])
                ->update(["is_error" => 1]);
        }
        
        Session::put('twitter_min_imp', $min_imp);
        Session::put('twitter_max_imp', $max_imp);
        Session::put('twitter_min_interactions', $min_interactions);
        Session::put('twitter_max_interactions', $max_interactions);
        Session::put('twitter_min_ir', round($min_ir,2));
        Session::put('twitter_max_ir', round($max_ir,2));
    }
}