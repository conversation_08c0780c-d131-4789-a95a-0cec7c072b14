<?php

namespace App\Excel;

use Framework\src\Http\Models\DataRevision;
use Framework\src\Http\Models\Language;
use Framework\src\Http\Models\Text;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;

class TextsImport implements ToCollection{
    /**
     * @param array $rows
     *
     * @return null
     */
    public function collection(Collection $rows){
        // Validation
        if(trim($rows[0][0]) != 'Key'){
            exit('Wrong first column name');
        }
        $languages = Language::all()->pluck('id', 'code')->all();

        // Get language id from header cols
        $headerCols = $rows[0];
        $colLangs   = [];
        foreach($headerCols as $headerCol){
            $languageId = '';
            if($headerCol != "Key" && $headerCol){
                $colParts = explode("_", $headerCol);
                $langCode = $colParts[0];
                if(isset($colParts[1])){
                    $langCode .= "_" . strtoupper($colParts[1]);
                }
                $languageId = isset($languages[$langCode]) ? $languages[$langCode] : '';
            }
            $colLangs[] = $languageId;
        }

        // Import records
        foreach($rows as $row){
            if(trim($row[0]) && $row[0] != 'Key'){
                $textkey = sanitizeStr($row[0]);
                foreach($colLangs as $cli => $languageId){
                    if($languageId){
                        // Get language value
                        $langTextVal = sanitizeHTMLStr($row[$cli]);
                        $text        = Text::where('language_id', $languageId)->where('key', sanitizeStr($textkey))->first();
                        if($text){
                            // If text exists than update value
                            $text->value = $langTextVal;
                            $text->save();
                        }
                        elseif($langTextVal || ($languageId == fS('app_settings.default_lang_id'))){
                            // Insert new text if there is value
                            $text              = new Text;
                            $text->language_id = $languageId;
                            $text->key         = $textkey;
                            $text->value       = $langTextVal;
                            $text->save();
                        }
                    }
                }
            }
        }
        DataRevision::incrementRevision();
    }
}
