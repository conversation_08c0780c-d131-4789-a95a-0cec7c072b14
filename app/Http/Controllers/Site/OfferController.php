<?php

namespace App\Http\Controllers\Site;

use App\Code\Ajax;
use App\Code\CustomerApi;
use App\Code\OfferFpdi;
use App\Http\Controllers\Controller;
use App\Http\Models\Category;
use App\Http\Models\CoverImage;
use App\Http\Models\Institute;
use App\Http\Models\LetterTemplate;
use App\Http\Models\LetterTemplateCategory;
use App\Http\Models\Offer;
use App\Http\Models\OfferEmail;
use App\Http\Models\Product;
use App\Http\Models\ProductFinanceTerm;
use App\Http\Models\ProductSaftey;
use App\Mail\SendEmail;
use Exception;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;

class OfferController extends Controller{

    public $ajax;

    /**
     * Create a new controller instance.
     *
     */
    public function __construct(Ajax $ajax){

        $this->ajax = $ajax;

        $this->middleware('employeelogincheck');
    }


    /**
     * @param Request $request
     *
     * @return JSON
     */
    public function startOffer(Request $request){

        $customer_kukurz = strtoupper(trim(sanitizeStr($request->input('customer_kukurz'))));
        $customer_email  = strtolower(trim(sanitizeStr($request->input('customer_email'))));
        $user_group      = session()->get('user_group');

        if($customer_kukurz && $user_group){

            $customerFound = false;
            $error_msg     = "";

            try{

                if($customer_email && (!session()->get('debug_login') && $customer_kukurz == "NK") ||
                    (session()->get('debug_login') && $customer_kukurz == "TEST001")
                ){

                    @Log::info(__METHOD__ . " Debug login with details Kukurz: " . @$customer_kukurz);

                    $custDummyInfo = [
                        "customerType"       => "U",
                        "customerShortCode"  => $customer_kukurz,
                        "customerShortName"  => $customer_kukurz,
                        "customerIdentifier" => $customer_kukurz,
                        "fullName"           => $customer_kukurz
                    ];

                    $customer_full_name = trim(sanitizeStr($request->input('customer_full_name')));
                    $cust_address       = trim(sanitizeStr($request->input('customer_adderss')));

                    $customerFound = true;
                    session()->put('o_cust_kukurz', $customer_kukurz);
                    session()->put('o_cust_name', $customer_full_name);
                    session()->put('o_cust_full_info', $custDummyInfo);

                    session()->put('o_cust_email', $customer_email);
                    session()->put('o_cust_address', $cust_address);

                    session()->forget('o_edit_offer_id');
                    session()->forget('o_products');
                    session()->forget('o_covenants');
                    session()->forget('offer_session_data');
                }
                else if(session()->get('debug_login')){

                    @Log::info(__METHOD__ . " Debug login Kukurz: " . @$customer_kukurz);

                    if(strpos($customer_kukurz, "TEST00") !== false){
                        @Log::info(__METHOD__ . " Debug login Kukurz: " . @$customer_kukurz);

                        $custDummyInfo = [
                            "customerType"       => "U",
                            "customerShortCode"  => $customer_kukurz,
                            "customerShortName"  => $customer_kukurz,
                            "customerIdentifier" => $customer_kukurz,
                            "fullName"           => $customer_kukurz
                        ];

                        $customerFound = true;
                        session()->put('o_cust_kukurz', $customer_kukurz);
                        session()->put('o_cust_name', $custDummyInfo['fullName']);
                        session()->put('o_cust_full_info', $custDummyInfo);

                        $result = json_decode('{"emails":[{"id":"DD9D345CB0847D0D","fip":"20230718153529400000","email":"<EMAIL>","kind":"G","status":"G","mainContact":false,"businessInfo":"Y","marketingInfo":"Y","bestBusinessInfo":false,"bestMarketingInfo":false,"skontaktAviso":false,"georgeBusinessAviso":false,"confirmationRequired":true,"lastModified":{"dateTime":"2023-07-18T15:35:29+0200","channel":"FIL","customerId":"B5ACE3F40C123456","name":"Test Name"}},{"id":"DD9D36B735260D11","fip":"20230718163030700000","email":"<EMAIL>","kind":"G","status":"G","mainContact":true,"businessInfo":"Y","marketingInfo":"Y","bestBusinessInfo":true,"bestMarketingInfo":true,"skontaktAviso":false,"georgeBusinessAviso":false,"confirmationRequired":true,"lastModified":{"dateTime":"2023-07-18T15:46:01+0200","channel":"FIL","customerId":"B5ACE3F40C123456","name":"Test Name"}}],"homepages":[],"phoneFaxs":[{"id":"B90A6667A1832A64","fip":"20040719132222000000","phoneType":"FN","mainContact":true,"status":"G","kind":"U","businessInfo":"Y","marketingInfo":"Y","phoneCountryCode":"0043","phoneAreaCode":"1","phoneNumber":"123456","exemptFromCrs":false,"bestBusinessInfo":true,"bestMarketingInfo":true,"lastModified":{"dateTime":"2004-07-19T13:22:22+0200","name":"Maschinelle Änderung"}},{"id":"B90A6667A1939464","fip":"20220505140053500000","phoneType":"FX","mainContact":true,"status":"G","kind":"U","businessInfo":"Y","marketingInfo":"N","phoneCountryCode":"0043","phoneAreaCode":"1","phoneNumber":"123456 10","exemptFromCrs":false,"bestBusinessInfo":false,"bestMarketingInfo":false,"lastModified":{"dateTime":"2022-05-05T14:00:53+0200","channel":"ZEN","name":"Maschinelle Änderung"}}],"addresses":[{"id":"B5ACE3F4ABF5EA44","fip":"2025021406054430000020010412141655400000","addressUsage":{"addressType":"HPT","mainContact":true,"status":"G","businessInfo":"Y","marketingInfo":"U","bestBusinessInfo":true,"bestMarketingInfo":true,"lastModified":{"name":"Maschinelle Änderung"}},"address":{"country":"AT","postcode":"1234","cityName":"Wien","cityId":"C81CF03EA26C8148","street":"Test address line 1","pobox":false,"exemptFromCrs":false,"automaticEnvelope":true,"envelope":["                              ","Test address line 1         ","1234 Wien                     "],"lastModified":{"name":"Maschinelle Änderung"},"changeable":true}}],"skontakt":{"available":true,"marketingInfo":"Y","lastModified":{"dateTime":"2014-08-21T10:40:52+0200","name":"Maschinelle Änderung"}}}', true);

                        $o_cust_email = '';
                        if(isset($result['emails'])){
                            foreach($result['emails'] as $emails){
                                if($emails['mainContact']){
                                    $o_cust_email = $emails['email'];
                                    break;
                                }
                            }
                        }
                        session()->put('o_cust_email', $o_cust_email);

                        $resultAddress = json_decode('[{"id":"B5ACE3F4ABF5EA44","fip":"2025021406054430000020010412141655400000","addressUsage":{"addressType":"HPT","mainContact":true,"status":"G","businessInfo":"Y","marketingInfo":"U","bestBusinessInfo":true,"bestMarketingInfo":true,"lastModified":{"name":"Maschinelle Änderung"}},"address":{"country":"AT","postcode":"1234","cityName":"Wien","cityId":"C81CF03EA26C8148","street":"Test address line 1","pobox":false,"exemptFromCrs":false,"automaticEnvelope":true,"envelope":["","Test address line 1","1234 Wien"],"lastModified":{"name":"Maschinelle Änderung"},"changeable":true}}]', true);

                        $o_cust_address = getEnvelopeAddress($resultAddress);
                        session()->put('o_cust_address', $o_cust_address);

                        session()->forget('o_edit_offer_id');
                        session()->forget('o_products');
                        session()->forget('o_covenants');
                        session()->forget('offer_session_data');
                    }
                    else{
                        @Log::error(__METHOD__ . " Error: Kukurz is not TEST00! IP: " . @getRequestIp() . " Data: " . @$customer_kukurz . " Group: " . @$user_group);

                        $error_msg = fT('b_customer.d_offer.j_customer_not_found_error_text', 'Customer info not found!');
                    }
                }
                else{
                    @Log::info(__METHOD__ . " API request Kukurz: " . @$customer_kukurz);

                    $user = @session()->has("user_id") ? @$this->getUser($request) : null;

                    //get data from real api
                    if(
                        ($user && $user->saml_user_api_customer_token) ||
                        (session()->has('user_api_customer_token') && session()->get('user_api_customer_token'))
                    ){

                        $userCustomerToken = session()->get('user_api_customer_token');
                        $userCustomerToken = $userCustomerToken ?: (json_decode($user->saml_user_api_customer_token, true) ?? "");

                        @Log::info(__METHOD__ . " API request with token length: " . (@strlen(@json_encode($userCustomerToken)) ?? -1) . " IP: " . @getRequestIp() . " For User: " . @$user->id);

                        $customerData = CustomerApi::getMicroCustomerFromApi($userCustomerToken["access_token"], $customer_kukurz);

                        if(isset($customerData['readMicroCustomerOutputView']) && isset($customerData['readMicroCustomerOutputView']['customerShortCode'])){

                            $custInfo = $customerData['readMicroCustomerOutputView'];

                            $customerFound = true;
                            session()->put('o_cust_kukurz', $customer_kukurz);
                            session()->put('o_cust_name', $custInfo['fullName']);
                            session()->put('o_cust_full_info', $custInfo);

                            session()->forget('o_edit_offer_id');
                            session()->forget('o_products');
                            session()->forget('o_covenants');
                            session()->forget('offer_session_data');

                            // Debug call and log
                            try{

                                $result = CustomerApi::getCustomerContacts($userCustomerToken["access_token"], $custInfo["customerIdentifier"], [], config("app.customer_contacts_api_app_key", "1755cfb98f9f467a96be98e51d3238cc"));
                                Log::info("Example Customer Contacts: " . json_encode($result, JSON_THROW_ON_ERROR));

                                $o_cust_email = '';
                                if(isset($result['emails'])){

                                    foreach($result['emails'] as $emails){

                                        if($emails['mainContact'])
                                            $o_cust_email = $emails['email'];
                                    }
                                }

                                session()->put('o_cust_email', $o_cust_email);
                            }
                            catch(Exception $e){
                                Log::error("Error getting customer contacts: " . $e->getMessage() . " - " . $e->getTraceAsString());
                            }

                            try{
                                $result = CustomerApi::getCustomerAddresses($userCustomerToken["access_token"], $custInfo["customerIdentifier"], [], config("app.customer_contacts_api_app_key", "1755cfb98f9f467a96be98e51d3238cc"));
                                Log::info("Example Customer Addresses: " . json_encode($result, JSON_THROW_ON_ERROR));

                                $o_cust_address = getEnvelopeAddress($result);
                                session()->put('o_cust_address', $o_cust_address);
                            }
                            catch(Exception $e){
                                Log::error("Error getting customer addresses: " . $e->getMessage() . " - " . $e->getTraceAsString());
                            }

                        }
                        else{

                            $error_msg = fT('b_customer.d_offer.j_customer_not_found_error_text', 'Customer info not found!');
                        }

                        Log::info("Example Customer Data: " . json_encode($customerData, JSON_THROW_ON_ERROR));
                    }
                    else{
                        $error_msg = fT('b_customer.d_offer.k_customer_api_error_text', 'Error in getting customer data from API!');
                    }
                }
            }
            catch(Exception $e){

                $error_msg = $e->getMessage();
                Log::error("Error getting customer data from API: " . $e->getMessage() . " - " . $e->getTraceAsString());
            }

            if($customerFound){
                $this->ajax->action('redirect', ['value' => route('site_offer_select_products')]);
            }
            else{
                $this->ajax->action('html', ['selector' => '.kukurz-error-txt', 'value' => $error_msg]);
            }
        }
        else{
            @Log::error(__METHOD__ . " Error: Kukurz is empty! IP: " . @getRequestIp() . " Data: " . @$customer_kukurz . " Group: " . @$user_group);
        }

        return $this->ajax->getOutJson();
    }


    /**
     * @param Request $request
     *
     * @return Application|Factory|View
     * @throws Exception
     */
    public function selectProducts(Request $request, $id = false){

        $offer_id     = $id ? (int)sanitizeStr($id) : false;
        $institute_id = sanitizeStr(session()->get('id_qualifier'));

        if($request->input("back") != "true")
            session()->forget('offer_session_data');

        $user      = $this->getUser($request);
        $institute = Institute::where('institute_id', $institute_id)->first();

        if($offer_id > 0 && (!session()->has('o_edit_offer_id') || session()->get('o_edit_offer_id') != $offer_id)){

            $offer = Offer::selectRaw("*" . AESdecypts(Offer::encryptableFields()))
                          ->where('user_id', $user->id)->where('institute_id', $institute_id)
                          ->where('id', $offer_id)->first();

            if($offer){

                session()->put('o_edit_offer_id', $offer_id);

                $products_info = json_decode($offer->products_info, true);
                session()->put('o_products', $products_info['products']);
                session()->put('o_covenants', $products_info['covenants']);

                session()->put('o_cust_name', $offer->customer_name);
                session()->put('o_cust_kukurz', $offer->customer_kukurz);
                session()->put('o_cust_full_info', json_decode($offer->customer_info, true));
            }
        }

        if(session()->has('o_edit_offer_id') && $offer_id < 1){

            session()->forget('o_edit_offer_id');
            session()->forget('o_products');
            session()->forget('o_covenants');
            session()->forget('o_cust_name');
        }

        $customer_name = session()->get('o_cust_name');
        $user_group    = session()->get('user_group');

        if($institute && $user_group && $customer_name){

            $product_ids = getAllowedProductIds($user->id, $institute_id, $user_group);

            $categories         = [];
            $products           = [];
            $categoryProductIds = [];

            if(!empty($product_ids)){

                $products = Product::where('institute_id', $institute_id)->whereIn('id', $product_ids)->get();

                foreach($products as $product){

                    if(!isset($categoryProductIds[$product->category_id]))
                        $categoryProductIds[$product->category_id] = [];

                    $categoryProductIds[$product->category_id][] = $product->id;
                }

                $categoryIds = array_keys($categoryProductIds);
                $categories  = Category::where('institute_id', $institute_id)->whereIn('id', $categoryIds)->get();
            }

            $viewData                       = [];
            $viewData['institute']          = $institute;
            $viewData['covenants']          = $institute->covenants();
            $viewData['products']           = $products;
            $viewData['categories']         = $categories;
            $viewData['categoryProductIds'] = $categoryProductIds;
            $viewData['user']               = $user;
            $viewData['offer_id']           = $offer_id > 0 ? $offer_id : '';

            $viewData['selected_products']  = session()->has('o_products') ? session()->get('o_products') : [];
            $viewData['selected_covenants'] = session()->has('o_covenants') ? session()->get('o_covenants') : [];

            return view('site.employee.offer-select-products', $viewData);
        }

        return redirect(route('site_index'));
    }


    /**
     * @param Request $request
     *
     * @return JSON
     */
    public function saveProducts(Request $request){

        $institute_id  = sanitizeStr(session()->get('id_qualifier'));
        $customer_name = session()->get('o_cust_name');
        $user_group    = session()->get('user_group');

        $user      = $this->getUser($request);
        $institute = Institute::where('institute_id', $institute_id)->first();

        if($institute && $user_group && $customer_name){

            //get allowed product ids
            $allowed_product_ids = getAllowedProductIds($user->id, $institute_id, $user_group);

            //build final products array
            $final_products = [];

            $offer_id   = (int)sanitizeStr($request->input('offer_id'));
            $products   = $request->input('products');
            $productArr = $request->input('product');
            $covenants  = $request->input('covenants');

            if($products){

                foreach($products as $productId){

                    if(!in_array($productId, $allowed_product_ids)){
                        continue;
                    }

                    $final_products[$productId] = [];

                    if(isset($productArr[$productId])){

                        $productData = $productArr[$productId];

                        if(!isset($productData['has_safeties']) || $productData['has_safeties'] != "Y"){
                            unset($productData['product_safeties']);
                        }

                        if(!isset($productData['has_finance_terms']) || $productData['has_finance_terms'] != "Y"){
                            unset($productData['product_finance_terms']);
                        }

                        $final_products[$productId] = $productData;
                    }
                }
            }

            if(!empty($final_products)){

                $final_product_ids = array_keys($final_products);
                $category_ids      = Product::selectRaw('DISTINCT(category_id) as category_id')->whereIn('id', $final_product_ids)->pluck('category_id')->toArray();

                $catCount = Category::whereIn('id', $category_ids)->where('is_financing', 1)->count();

                if($catCount > 0)
                    session()->put('o_covenants', $covenants);
                else
                    session()->forget('o_covenants');

                session()->put('o_products', $final_products);

                $this->ajax->action('redirect', ['value' => $offer_id > 0 ? route('site_edit_offer', ['id' => $offer_id]) : route('site_create_offer')]);
            }
            else{
                $this->ajax->action('show_temp_error', ['selector' => '.form-error-text', 'value' => fT('b_customer.d_offer.l_product_select_error_text', 'Please select at least one product!')]);
            }

            return $this->ajax->getOutJson();
        }
    }


    /**
     * @param Request $request
     *
     * @return Application|Factory|View
     * @throws Exception
     */
    public function createOffer(Request $request){

        $institute_id   = sanitizeStr(session()->get('id_qualifier'));
        $sess_products  = session()->get('o_products');
        $sess_covenants = session()->get('o_covenants');

        $user = $this->getUser($request);

        $institute = Institute::where('institute_id', $institute_id)->first();

        if($user && $institute && !empty($sess_products)){

            $viewData = $this->offerViewData(false, $institute_id, $sess_products, $sess_covenants);

            $viewData['institute']     = $institute;
            $viewData['user']          = $user;
            $viewData['customer_name'] = session()->get('o_cust_name');
            $viewData['page_type']     = 'create-offer';

            return view('site.employee.offer-create', $viewData);
        }

        return redirect(route('site_index'));
    }

    private function offerViewData($offer, $institute_id, $sess_products, $sess_covenants){

        $viewData = [];

        //get selected offer products
        $product_ids = array_keys($sess_products);

        $products = Product::where('institute_id', $institute_id)->whereIn('id', $product_ids)
                           ->orderBy('category_id', 'asc')->orderBy('order_num', 'asc')->get()->keyBy('id');

        $categoryProducts = [];
        foreach($products as $product){

            if(!isset($categoryProducts[$product->category_id]))
                $categoryProducts[$product->category_id] = [];

            $categoryProducts[$product->category_id][] = $product->id;
        }

        $categoryIds = array_keys($categoryProducts);
        $categories  = Category::where('institute_id', $institute_id)->whereIn('id', $categoryIds)->orderBy('id', 'asc')->get()->keyBy('id');


        //get selected product safties and finance terms
        $product_safeties_ids     = [];
        $product_finance_term_ids = [];

        foreach($sess_products as $sProd){

            if(isset($sProd['product_safeties'])){
                foreach($sProd['product_safeties'] as $safteyId)
                    $product_safeties_ids[] = $safteyId;
            }

            if(isset($sProd['product_finance_terms'])){
                foreach($sProd['product_finance_terms'] as $fTermId)
                    $product_finance_term_ids[] = $fTermId;
            }
        }

        $productSafeties = [];
        if(!empty($product_safeties_ids))
            $productSafeties = ProductSaftey::whereIn('product_id', $product_ids)
                                            ->whereIn('id', $product_safeties_ids)
                                            ->where('is_covenant', 0)
                                            ->orderBy('sort_order', 'ASC')
                                            ->get()->keyBy('id');

        $productFinanceTerms = [];
        if(!empty($product_finance_term_ids))
            $productFinanceTerms = ProductFinanceTerm::whereIn('product_id', $product_ids)
                                                     ->whereIn('id', $product_finance_term_ids)
                                                     ->orderBy('sort_order', 'ASC')
                                                     ->get()->keyBy('id');

        $viewData['productSafeties']     = $productSafeties;
        $viewData['productFinanceTerms'] = $productFinanceTerms;

        $cover_images    = CoverImage::get()->keyBy('id');
        $first_cover_key = $cover_images->keys()->first();

        $offer_cover_image_id     = $offer && isset($cover_images[$offer->cover_image_id]) ? $offer->cover_image_id : $first_cover_key;
        $viewData['cover_images'] = $cover_images;

        $viewData['intro_categories'] = LetterTemplateCategory::selectRaw('letter_template_categories.*')
                                                              ->join('letter_templates', 'letter_templates.letter_template_category_id', 'letter_template_categories.id')
                                                              ->where('institute_id', $institute_id)
                                                              ->groupBy('letter_template_categories.id')
                                                              ->orderBy('letter_template_categories.id', 'ASC')->get();

        $intro_letters          = LetterTemplate::where('institute_id', $institute_id)
                                                ->orderBy('letter_template_category_id', 'ASC')->orderBy('id', 'ASC')
                                                ->get()->keyBy('id');
        $first_intro_letter_key = $intro_letters->keys()->first();

        $offer_letter_template_id  = $offer && isset($intro_letters[$offer->letter_template_id]) ? $offer->letter_template_id : $first_intro_letter_key;
        $viewData['intro_letters'] = $intro_letters;

        $viewData['offer']             = $offer;
        $viewData['offer_csses']       = $offer && $offer->offer_css ? json_decode($offer->offer_css, true) : [];
        $viewData['products']          = $products;
        $viewData['categories']        = $categories;
        $viewData['category_products'] = $categoryProducts;

        $viewData['sess_products'] = $sess_products;
        $viewData['covenants']     = $sess_covenants ?
            ProductSaftey::where('institute_id', $institute_id)->whereIn('id', $sess_covenants)->where('is_covenant', 1)->orderBy('sort_order', 'ASC')->get()
            : false;

        if($offer){

            $products_info = json_decode($offer->products_info, true);
            //$sess_products = $products_info['products'];
            //$sess_covenants = $products_info['covenants'];
            $viewData['products_html']   = $products_info['htmls'];
            $viewData['covenant_values'] = $products_info['covenant_values'];
        }

        if(session()->has('offer_session_data')){

            $offer_session = session()->get('offer_session_data');
            $products_info = json_decode($offer_session['products_info'], true);

            $viewData['products_html']   = $products_info['htmls'];
            $viewData['covenant_values'] = $products_info['covenant_values'];

            $viewData['offer_intro_text'] = $offer_session['intro_text'];
            $viewData['offer_csses']      = $offer_session['offer_css'] ? json_decode($offer_session['offer_css'], true) : [];

            $offer_letter_template_id = $offer_session['letter_template_id'];
            $offer_cover_image_id     = $offer_session['cover_image_id'];
        }

        $viewData['cover_image_id'] = $offer_cover_image_id;
        $viewData['coverImg']       = isset($cover_images[$offer_cover_image_id]) ? $cover_images[$offer_cover_image_id] : $cover_images[$first_cover_key];

        $viewData['selected_intro_letter'] = isset($intro_letters[$offer_letter_template_id]) ? $intro_letters[$offer_letter_template_id] : $intro_letters[$first_intro_letter_key];

        return $viewData;
    }

    /**
     * @param Request $request
     *
     * @return Application|Factory|View
     * @throws Exception
     */
    public function editOffer(Request $request, $id){

        $offer_id     = (int)sanitizeStr($id);
        $institute_id = sanitizeStr(session()->get('id_qualifier'));

        $user      = $this->getUser($request);
        $institute = Institute::where('institute_id', $institute_id)->first();
        $offer     = Offer::selectRaw("*" . AESdecypts(Offer::encryptableFields()))
                          ->where('user_id', $user->id)->where('institute_id', $institute_id)
                          ->where('id', $offer_id)->first();

        $sess_products = session()->get('o_products');

        if($user && $offer && $institute && !empty($sess_products)){

            $sess_covenants = session()->get('o_covenants');

            $viewData = $this->offerViewData($offer, $institute_id, $sess_products, $sess_covenants);

            $viewData['institute']     = $institute;
            $viewData['user']          = $user;
            $viewData['customer_name'] = $offer->customer_name;

            $viewData['page_type'] = 'create-offer';

            return view('site.employee.offer-create', $viewData);
        }

        return redirect(route('site_index'));
    }

    /**
     * @param Request $request
     *
     * @return JSON
     */
    public function saveOffer(Request $request, $session = false){

        $institute_id   = sanitizeStr(session()->get('id_qualifier'));
        $sess_products  = session()->get('o_products');
        $sess_covenants = session()->get('o_covenants');
        $customer_name  = session()->get('o_cust_name');

        $user = $this->getUser($request);

        $offer_id           = (int)sanitizeStr($request->input('offer_id'));
        $letter_template_id = (int)sanitizeStr($request->input('letter_template_id'));
        $cover_image_id     = (int)sanitizeStr($request->input('cover_image_id'));

        $products_html   = $request->input('products_html');
        $covenant_inputs = $request->input('covenant_inputs');
        $offer_css       = $request->input('offer_css');

        $institute = Institute::where('institute_id', $institute_id)->first();

        $cur_offer = true;
        if($offer_id > 0){

            $cur_offer = Offer::selectRaw("*" . AESdecypts(Offer::encryptableFields()))
                              ->where('id', $offer_id)->where('user_id', $user->id)
                              ->where('institute_id', $institute_id)->first();

            if($cur_offer){

                //$products_info = json_decode($cur_offer->products_info, true);
                //$sess_products = $products_info['products'];
                //$sess_covenants = $products_info['covenants'];
                $customer_name = $cur_offer->customer_name;
            }
        }

        if($institute && $cur_offer && $customer_name && !empty($sess_products) && !empty($products_html) && $cover_image_id && $letter_template_id){

            $products_info             = [];
            $products_info['products'] = $sess_products;

            foreach($products_html as $pId => $pHtml){
                $products_info['htmls'][$pId] = sanitizeHTMLStr($pHtml);
            }

            $products_info['covenants'] = $sess_covenants;

            $covenant_values = [];
            if(!empty($covenant_inputs)){

                foreach($covenant_inputs as $cId => $inputValues){

                    $covenant_values[$cId] = [];
                    foreach($inputValues as $inputVal){
                        $covenant_values[$cId][] = sanitizeStrFull($inputVal);
                    }
                }
            }
            $products_info['covenant_values'] = $covenant_values;

            $final_offer_css = [];
            if(!empty($offer_css)){

                foreach($offer_css as $styleId => $css_content){
                    $final_offer_css[$styleId] = sanitizeHTMLStr($css_content);
                }
            }

            $offer_product_ids    = implode(",", array_keys($sess_products));
            $intro_text           = sanitizeHTMLStr($request->input('intro_text'));
            $products_info_json   = json_encode($products_info);
            $final_offer_css_json = json_encode($final_offer_css);

            if($session == "session"){

                $offer_session = [
                    "product_ids"        => $offer_product_ids,
                    "letter_template_id" => $letter_template_id,
                    "cover_image_id"     => $cover_image_id,
                    "intro_text"         => $intro_text,
                    "products_info"      => $products_info_json,
                    "offer_css"          => $final_offer_css_json,
                ];

                session()->put('offer_session_data', $offer_session);
            }
            else{

                if($offer_id < 1 && $cur_offer === true){

                    $customer_kukurz = session()->get('o_cust_kukurz');
                    $customer_info   = json_encode(session()->get('o_cust_full_info'));

                    $offer                  = new Offer();
                    $offer->institute_id    = $institute_id;
                    $offer->user_id         = $user->id;
                    $offer->offer_code      = generate_offer_code($customer_kukurz);
                    $offer->customer_name   = $customer_name;
                    $offer->customer_kukurz = $customer_kukurz;
                    $offer->customer_info   = $customer_info;
                    $offer->cust_email      = session()->get('o_cust_email');
                    $offer->cust_address    = session()->get('o_cust_address');
                    $offer->version         = Offer::getNextVersion($offer->cust_address);
                }
                else{
                    $offer = $cur_offer;
                }

                $offer->product_ids        = $offer_product_ids;
                $offer->letter_template_id = $letter_template_id;
                $offer->cover_image_id     = $cover_image_id;
                $offer->intro_text         = $intro_text;
                $offer->products_info      = $products_info_json;
                $offer->offer_css          = $final_offer_css_json;
                $offer->save();

                $this->ajax->data([
                    'success'          => true,
                    'offer_id'         => $offer->id,
                    'offer_view_url'   => route('site_customer_offer_cover', ['offer_code' => $offer->offer_code]),
                    'offer_pdf_url'    => route('site_offer_pdf_download', ['offer_code' => $offer->offer_code]),
                    'mainContactEmail' => session()->get('o_cust_email')
                ], false);
            }
        }
        else{
            $this->ajax->data(['success' => false], false);
        }

        return $this->ajax->getOutJson();
    }


    /**
     * @param Request $request
     *
     * @return JSON
     */
    public function sendMail(Request $request){

        $validated = $request->validate([
            'customer_email' => ['required', 'email:rfc,dns'],
            'mail_subject'   => 'required',
            'mail_body'      => 'required',
            'offer_id'       => 'required',
        ]);

        $institute_id = sanitizeStrFull(session()->get('id_qualifier'));
        $offer_id     = (int)sanitizeStrFull($request->input('offer_id'));

        $user      = $this->getUser($request);
        $institute = Institute::where('institute_id', $institute_id)->first();

        $offer = Offer::selectRaw("*" . AESdecypts(Offer::encryptableFields()))
                      ->where('id', $offer_id)->where('user_id', $user->id)
                      ->where('institute_id', $institute_id)->first();

        if($user && $institute && $offer){

            $customer_email = strtolower(sanitizeStrFull($request->input('customer_email')));
            $mail_subject   = sanitizeStrFull($request->input('mail_subject'));
            $mail_body      = sanitizeStrFull($request->input('mail_body'));

            $offer->mail_subject = $mail_subject;
            $offer->mail_body    = $mail_body;
            $offer->save();

            //check if offer email already exists
            $offerEmail = OfferEmail::where('offer_id', $offer->id)
                                    ->whereRaw(AESdecypt('customer_email', false) . " = ?", array($customer_email))
                                    ->first();

            if(!$offerEmail){

                $offerEmail                 = new OfferEmail();
                $offerEmail->offer_id       = $offer->id;
                $offerEmail->customer_email = $customer_email;
                $offerEmail->save();
            }

            $final_mail_body = '
<table width="100%" border="0" cellpadding="0" cellspacing="0">
    <tr>
        <td style="padding:0 0 20px 0px;font-size:16px;font-weight:400;color:#000000;font-family: \'Inter\', \'Open Sans\', sans-serif;margin:0;line-height:24px;text-align:left;">
            <p style="margin:0;padding:0;">' . nl2br($mail_body) . '</p>
        </td>
    </tr>
</table>';

            //send mail to customer
            $mail_data    = [
                'customer_email'      => $customer_email,
                'customer_name'       => $offer->customer_name,
                'employee_email'      => $user->email,
                'employee_first_name' => $user->first_name,
                'employee_last_name'  => $user->last_name,
                'offer_link'          => '<a href="' . route('site_customer_offer_login', ['offer_code' => $offer->offer_code]) . '" target="_blank" style="color: #2870ed; font-weight:600; text-decoration: underline; text-underline-offset: 3px; text-decoration-thickness: 2px;">',
                "/offer_link"         => '</a>',
                'force_mail_subject'  => $mail_subject,
                'force_mail_body'     => $final_mail_body,
                'inst_logo'           => $institute->mail_logo ? url('public/uploads/' . $institute->mail_logo) : '',
                'inst_name'           => $institute->pdf_institute_name ?? $institute->institute_name,
            ];
            $sentResponse = Mail::send(new SendEmail('send_offer_to_customer', $mail_data));

            Log::info("Offer Email Sent: " . @$sentResponse?->getDebug() . " - " . @$sentResponse?->getMessage()?->toString());

            $this->ajax->data(['success' => true], false);
        }
        else{
            $this->ajax->data(['success' => false], false);
        }

        return $this->ajax->getOutJson();
    }


    /**
     * @param Request $request
     *
     * @return PDF File
     */
    public function offerPdfDownload(Request $request, $offer_code, $onlyStore = false){

        $institute_id = sanitizeStrFull(session()->get('id_qualifier'));
        $offer_code   = sanitizeStrFull($offer_code);

        $user      = $this->getUser($request);
        $institute = Institute::where('institute_id', $institute_id)->first();

        $offer = Offer::selectRaw("*" . AESdecypts(Offer::encryptableFields()))
                      ->where('offer_code', $offer_code)->where('user_id', $user->id)
                      ->where('institute_id', $institute_id)->first();

        if($user && $institute && $offer){

            $this->downloadPdf($offer, $user, $institute, $onlyStore);
        }

        return fT('b_customer.b_code_screen.i_not_able_to_download_error_text', 'You are not able to download this pdf!');
    }


    public function downloadPdf($offer, $user, $institute, $onlyStore = false){

        $products_info  = json_decode($offer->products_info, true);
        $sess_products  = $products_info['products'];
        $sess_covenants = $products_info['covenants'];

        $offerData = $this->offerViewData($offer, $institute->institute_id, $sess_products, $sess_covenants);

        $covenants       = $offerData['covenants'];
        $covenant_values = isset($products_info['covenant_values']) ? $products_info['covenant_values'] : [];

        if($covenants){

            foreach($covenants as $covenant){

                if(isset($covenant_values[$covenant->id])){

                    $replaceArrayVal = $covenant_values[$covenant->id];

                    foreach($replaceArrayVal as $replaceVal)
                        $covenant->safety_clause = Str::replaceFirst('[INPUT', '<span class="covenant-input-u">' . $replaceVal . '</span>' . 'COVINPUVAL', $covenant->safety_clause);

                    $covenant->safety_clause = str_replace(['COVINPUVAL]', 'COVINPUVALS]', 'COVINPUVALM]', 'COVINPUVALA]'], '', $covenant->safety_clause);
                }
            }
        }

        $offerData['covenants']     = $covenants;
        $offerData['products_html'] = $products_info['htmls'];

        $offerData['offer']     = $offer;
        $offerData['user']      = $user;
        $offerData['institute'] = $institute;

        //generate offer html files
        $header_html_path = public_path('uploads/tmp/header-' . $offer->offer_code . '.html');
        $header_html      = View::make('site.pdf.header', $offerData)->render();
        file_put_contents($header_html_path, $header_html);

        $footer_html_path = public_path('uploads/tmp/footer-' . $offer->offer_code . '.html');
        $footer_html      = View::make('site.pdf.footer', $offerData)->render();
        file_put_contents($footer_html_path, $footer_html);

        $cover_html_path = public_path('uploads/tmp/offer-cover-' . $offer->offer_code . '.html');
        $cover_html      = View::make('site.pdf.offer-cover', $offerData)->render();
        file_put_contents($cover_html_path, $cover_html);

        $offer_html_path = public_path('uploads/tmp/offer-products-' . $offer->offer_code . '.html');
        $offer_html      = View::make('site.pdf.offer-products', $offerData)->render();
        file_put_contents($offer_html_path, $offer_html);

        // Generate PDF once and download it (it will be uploaded to API within the generate_offer_pdf method)
        $this->generate_offer_pdf($offer, $user, $offer_html_path, $cover_html_path, $header_html_path, $footer_html_path, !$onlyStore);
    }


    private function generate_offer_pdf($offer, $user, $offer_html_path, $cover_html_path, $header_html_path, $footer_html_path, $force_download = true){

        $offer_code = $offer->offer_code;

        $html_url   = url('public/uploads/tmp/' . basename($offer_html_path));
        $cover_url  = url('public/uploads/tmp/' . basename($cover_html_path));
        $header_url = url('public/uploads/tmp/' . basename($header_html_path));
        $footer_url = url('public/uploads/tmp/' . basename($footer_html_path));

        // Init path and file names
        $template_output_folder_path = base_path() . DIRECTORY_SEPARATOR . "wkhtmltopdf" . DIRECTORY_SEPARATOR . "output" . DIRECTORY_SEPARATOR;
        $template_binary_folder_path = base_path() . DIRECTORY_SEPARATOR . "wkhtmltopdf" . DIRECTORY_SEPARATOR . "binary" . DIRECTORY_SEPARATOR;

        $pdf_file_name                  = "KOLT-Angebot-" . $offer->customer_kukurz . "-" . date('d.m.Y', strtotime($offer->created_at)) . ".pdf";
        $output_pdf_file_name_file_path = $template_output_folder_path . $pdf_file_name;

        $offer_products_file_path = $template_output_folder_path . "offer_products" . "_" . $offer_code . ".pdf";
        $cover_file_path          = $template_output_folder_path . "offer_cover" . "_" . $offer_code . ".pdf";

        // Set binary path
        $wkhtmltopdf_bin_path = strtoupper(substr(PHP_OS, 0, 3)) === 'WIN' ? ($template_binary_folder_path . "wkhtmltopdf.exe") : "wkhtmltopdf";

        // Generate cover page pdfs
        $command_str = $wkhtmltopdf_bin_path . " --page-size A4 --dpi 300 -O Portrait -L 3 -R 3 -T 3 -B 3 " . escapeshellarg($cover_url) . " " . escapeshellarg($cover_file_path);
        $command_str = escapeshellcmd($command_str);

        $output     = "";
        $resultCode = -1;
        $return_str = exec($command_str, $output, $resultCode);

        // for offer product pages
        $command_str = $wkhtmltopdf_bin_path . " --page-size A4 --dpi 300 -O Portrait -L 3 -R 3 -T 35 -B 13 ";
        $command_str .= " --header-spacing 7 ";
        $command_str .= " --header-html " . $header_url;
        $command_str .= " --footer-html " . $footer_url . " ";
        $command_str .= escapeshellarg($html_url) . " ";
        $command_str .= escapeshellarg($offer_products_file_path);

        $command_str = escapeshellcmd($command_str);
        $output      = "";
        $resultCode  = -1;
        $return_str  = exec($command_str, $output, $resultCode);


        $oMerger = new OfferFpdi();

        // set document information
        $pdf_title = str_replace([".pdf", "-"], ["", " "], $pdf_file_name);
        $oMerger->SetCreator(config('app.name'));
        $oMerger->SetAuthor($user->first_name . " " . $user->last_name);
        $oMerger->SetTitle($pdf_title);
        $oMerger->SetSubject($pdf_title);
        //$oMerger->SetKeywords('keywords, here 1');

        $preferences = [
            'DisplayDocTitle' => true
        ];
        $oMerger->setViewerPreferences($preferences);

        //Set document language.
        $oMerger->setLanguageArray(array('a_meta_language' => 'de-DE'));

        $mergeFiles = [$cover_file_path, $offer_products_file_path];

        foreach($mergeFiles as $mergeFile){

            // set the source file
            $pageCount = $oMerger->setSourceFile($mergeFile);

            // Loop through each page
            for($pageNo = 1; $pageNo <= $pageCount; $pageNo++){

                // Import the page
                $templateId = $oMerger->importPage($pageNo);

                // Get the size of the imported page
                $size = $oMerger->getTemplateSize($templateId);

                // Add a new page to the TCPDF document
                $oMerger->AddPage($size['orientation'], array($size['width'], $size['height']));

                // Use the imported page as a template
                $oMerger->useTemplate($templateId);
            }
        }

        $oMerger->Output($output_pdf_file_name_file_path, 'F');

        // Upload the PDF to the API
        $this->uploadPdfToApi($offer, $pdf_file_name, $output_pdf_file_name_file_path);

        // Clean up intermediate PDF files
        @unlink($cover_file_path);
        @unlink($offer_products_file_path);
        @unlink($output_pdf_file_name_file_path);

        // Clean up HTML files
        @unlink($header_html_path);
        @unlink($footer_html_path);
        @unlink($cover_html_path);
        @unlink($offer_html_path);

        if($force_download){

            $oMerger->Output(basename($output_pdf_file_name_file_path), 'D');
            exit();
        }

        // Return the path for other uses
        return $output_pdf_file_name_file_path;
    }

    private function uploadPdfToApi($offer, $pdf_file_name, $pdf_file_path){
        // Debug log
        \Illuminate\Support\Facades\Log::info("Uploading PDF to API for offer: " . $offer->offer_code, [
            'offer'         => $offer->toArray(),
            'pdf_file_name' => $pdf_file_name,
            'pdf_file_path' => $pdf_file_path
        ]);

        try{
            // Get user token from session or user model
            $userCustomerToken = session()->get('user_api_customer_token');
            if(!$userCustomerToken && isset($offer->user_id)){
                $user = \App\Http\Models\User::find($offer->user_id);
                if($user && $user->saml_user_api_customer_token){
                    $userCustomerToken = json_decode($user->saml_user_api_customer_token, true);
                }
            }
            if(!$userCustomerToken){
                \Illuminate\Support\Facades\Log::warning("Could not upload PDF to API - no valid access token found for offer: " . $offer->offer_code);
                return false;
            }

            if($userCustomerToken && isset($userCustomerToken["access_token"])){
                $customerInfo = session()->get('o_cust_full_info') ?? [];
                $customerId   = @$customerInfo["customerIdentifier"] ?? "";
                if(!$customerId){
                    $customerId = @json_decode($offer->customer_info, true)["customerIdentifier"] ?? "";
                }
                Log::debug("Customer ID: " . $customerId . " for offer: " . $offer->offer_code);

                // Store the PDF in the API
                $metadata = [
                    "namDokument" => $pdf_file_name,
                    "codGoNummer" => $customerId, // 16 Characters
                    "codGoTyp"    => "KU", // KU
                    "codHuelle"   => "364", // Folder number in their system
                    "codFormat"   => "pdf",
                    "codHerkunft" => "KOL", // Just 3 length identifier which app uploaded the file -> KOL"T
                ];

                $result = \App\Code\CustomerApi::storeDocumentWithSystemAttributes(
                    $userCustomerToken["access_token"],
                    $pdf_file_path,
                    $metadata
                );

                // Test #2
                /*
                try{
                    // Store the PDF in the API
                    $metadata = [
                        "namDokument" => "test2_" . $pdf_file_name,
                        "codGoNummer" => $offer->customer_kukurz,
                        "codGoTyp"    => "K64",
                        "codHuelle"   => "364",
                    ];

                    $result = \App\Code\CustomerApi::storeDocumentWithSystemAttributes(
                        $userCustomerToken["access_token"],
                        $pdf_file_path,
                        $metadata
                    );
                }
                catch(Exception $e){
                    Log::error("Test 2Error uploading PDF to API: " . $e->getMessage(), [
                        'offer_code' => $offer->offer_code,
                        'exception'  => $e
                    ]);
                }
                */

                \Illuminate\Support\Facades\Log::info("PDF uploaded to API for offer: " . $offer->offer_code, [
                    'result' => $result
                ]);

                return true;
            }
            else{
                \Illuminate\Support\Facades\Log::warning("Could not upload PDF to API - no valid access token found for offer: " . $offer->offer_code);
                return false;
            }
        }
        catch(\Exception $e){
            \Illuminate\Support\Facades\Log::error("Error uploading PDF to API: " . $e->getMessage(), [
                'offer_code' => $offer->offer_code,
                'exception'  => $e
            ]);
            return false;
        }
    }
}
