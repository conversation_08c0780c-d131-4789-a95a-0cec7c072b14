<?php

namespace App\Http\Controllers\Site;

use App\_modules\TvApi\Controllers\TvFeedController;
use App\Http\Controllers\Controller;
use App\Http\Models\ApiUser;
use App\Http\Models\Game;
use App\Http\Models\GameCatalogue;
use App\Http\Models\Publisher;
use App\Http\Models\VideoCreatorGame;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use App\Excel\GamesThumbnailImport;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Code\SimpleImage;
use Mobile_Detect;
use Framework\plugins\asset_optimizer\src\Code\ImageOptimizer;
use WebPConvert\Convert\Exceptions\ConversionFailedException;

class SiteController extends Controller{
    /**
     * Create a new controller instance.
     */
    public function __construct(Mobile_Detect $mobileDetect){
        ini_set('max_execution_time', '300');
        ini_set('memory_limit', '1024M');
        $this->is_mobile = $mobileDetect->isMobile() && !$mobileDetect->isTablet() ? true : false;
        if(fS('app.preview_allowed_to_all_ip', '1') != "1"){
            $this->middleware('ipcheck', ['only' => ['preview', 'index']]);
        }
    }

    /**
     * @param Request $request
     *
     * @return array|Application|Factory|View
     */
    public function index(Request $request){
        return view('site.index', []);
    }


    /**
     * @param Request $request
     *
     * @return array|Application|Factory|View
     */
    public function importThumbnails(Request $request){

        //import game thumbnails
        $tmpFileFullPath = public_path() . DIRECTORY_SEPARATOR . "PS_catalouge.xlsx";
        Excel::import(new GamesThumbnailImport, $tmpFileFullPath);
    }

    public function gameList(Request $request){

        //$games = Game :: select('id','name','data_json')->orderBy('created_at','desc')->limit(50)->get();

        $games = Game :: select('id')->inRandomOrder()->limit(10)->get();

        foreach($games as $g){

            $game = Game :: select('id','name','data_json')->where('id', $g->id)->first();

            $gameData = $game->getJsonData(true);
            
            if(@$gameData['id'])  {

                $gameCatalogue = GameCatalogue :: where('game_id', $gameData['id'])->first();

                if(!$gameCatalogue)  {

                    $gameCatalogue = new GameCatalogue();
                    $gameCatalogue->code = random_string(12);
                
                    $gameCatalogue->game_id = $gameData['id'];
                    $gameCatalogue->game_name = $game->name;

                    $code = random_string(12);

                    $screenshot_flag = false;
                    $master_flag = false;

                    if(isset($gameData['media']['images']['SCREENSHOT'][0])){
                        
                        $screenshot_flag = true;

                        $screenShot = $gameData['media']['images']['SCREENSHOT'][0];
                        list($screenShot_width) = getimagesize($screenShot); 
                        
                        $tmpFileExt = pathinfo($screenShot, PATHINFO_EXTENSION);
                        $screenshot_file_name = $gameData['id'].'_'.time().'.'.$tmpFileExt;

                        $screenshot_image_temp = public_path('uploads/tmp') . DIRECTORY_SEPARATOR . $screenshot_file_name;
                        copy($screenShot, $screenshot_image_temp);

                        $new_desktop_file_name = $code.'-screenshot-desktop.'.$tmpFileExt;
                        $uploadDestFullPath_desktop = public_path('uploads/advent') . DIRECTORY_SEPARATOR . $new_desktop_file_name;
                        $si_thumb = new SimpleImage();
                        $si_thumb->load($screenshot_image_temp);

                        $gameCatalogue->screenshot = $new_desktop_file_name;

                        $si_thumb->cropImage(640,360, 1);
                        $si_thumb->save($uploadDestFullPath_desktop, $si_thumb->image_type, 100);
                        $imageOptimizer = app(ImageOptimizer::class);
                        $imageOptimizer->setDebug(false);
                        $imageOptimizer->optimizeImage($uploadDestFullPath_desktop);

                        $new_4k_file_name = $code.'-screenshot-4k.'.$tmpFileExt;
                        $uploadDestFullPath_4k = public_path('uploads/advent') . DIRECTORY_SEPARATOR . $new_4k_file_name;
                        if($screenShot_width >= 1280)
                        {
                            $si_thumb = new SimpleImage();
                            $si_thumb->load($screenshot_image_temp);
                            $si_thumb->cropImage(1280,720, 1);
                            $si_thumb->save($uploadDestFullPath_4k, $si_thumb->image_type, 100);
                            $imageOptimizer = app(ImageOptimizer::class);
                            $imageOptimizer->setDebug(false);
                            $imageOptimizer->optimizeImage($uploadDestFullPath_4k);
                        }
                        else{
                            copy($screenshot_image_temp, $uploadDestFullPath_4k);
                            $imageOptimizer = app(ImageOptimizer::class);
                            $imageOptimizer->setDebug(false);
                            $imageOptimizer->optimizeImage($uploadDestFullPath_4k);
                        }    

                        $new_mobile_file_name = $code.'-screenshot-mobile.'.$tmpFileExt;
                        $uploadDestFullPath_mobile = public_path('uploads/advent') . DIRECTORY_SEPARATOR . $new_mobile_file_name;
                        $si_thumb = new SimpleImage();
                        $si_thumb->load($screenshot_image_temp);
                        $si_thumb->cropImage(420,236, 1);
                        $si_thumb->save($uploadDestFullPath_mobile, $si_thumb->image_type, 100);
                        $imageOptimizer = app(ImageOptimizer::class);
                        $imageOptimizer->setDebug(false);
                        $imageOptimizer->optimizeImage($uploadDestFullPath_mobile);
                    }

                    //For game image
                    if(isset($gameData["media"]["images"]["MASTER"][0]))  {

                        $master_flag = true;

                        $game_image = $gameData["media"]["images"]["MASTER"][0];
                        list($game_image_width) = getimagesize($game_image); 

                        $tmpFileExt = pathinfo($game_image, PATHINFO_EXTENSION);
                        $game_file_name = $gameData['id'].'_'.time().'.'.$tmpFileExt;

                        $game_image_temp = public_path('uploads/tmp') . DIRECTORY_SEPARATOR . $game_file_name;
                        copy($game_image, $game_image_temp);
                        
                        $new_thumb_desktop_file_name = $code.'-game-thumb-desktop.'.$tmpFileExt;
                        $uploadDestFullPath_desktop_thumb = public_path('uploads/advent') . DIRECTORY_SEPARATOR . $new_thumb_desktop_file_name;
                        $si_thumb = new SimpleImage();
                        $si_thumb->load($game_image_temp);
                        $si_thumb->cropImage(240,240, 1);
                        $si_thumb->save($uploadDestFullPath_desktop_thumb, $si_thumb->image_type, 100);
                        $imageOptimizer = app(ImageOptimizer::class);
                        $imageOptimizer->setDebug(false);
                        $imageOptimizer->optimizeImage($uploadDestFullPath_desktop_thumb);

                        $new_thumb_mobile_file_name = $code.'-game-thumb-mobile.'.$tmpFileExt;
                        $uploadDestFullPath_mobile_thumb = public_path('uploads/advent') . DIRECTORY_SEPARATOR . $new_thumb_mobile_file_name;
                        $si_thumb = new SimpleImage();
                        $si_thumb->load($game_image_temp);
                        $si_thumb->cropImage(120,120, 1);
                        $si_thumb->save($uploadDestFullPath_mobile_thumb, $si_thumb->image_type, 100);
                        $imageOptimizer = app(ImageOptimizer::class);
                        $imageOptimizer->setDebug(false);
                        $imageOptimizer->optimizeImage($uploadDestFullPath_mobile_thumb);

                        $new_thumb_4k_file_name = $code.'-game-thumb-4k.'.$tmpFileExt;
                        $uploadDestFullPath_4k_thumb = public_path('uploads/advent') . DIRECTORY_SEPARATOR . $new_thumb_4k_file_name;
                        $si_thumb = new SimpleImage();
                        $si_thumb->load($game_image_temp);
                        $si_thumb->cropImage(480,480, 1);
                        $si_thumb->save($uploadDestFullPath_4k_thumb, $si_thumb->image_type, 100);
                        $imageOptimizer = app(ImageOptimizer::class);
                        $imageOptimizer->setDebug(false);
                        $imageOptimizer->optimizeImage($uploadDestFullPath_4k_thumb);

                        ///
                        $new_desktop_file_name = $code.'-game-desktop.'.$tmpFileExt;
                        $uploadDestFullPath_desktop = public_path('uploads/advent') . DIRECTORY_SEPARATOR . $new_desktop_file_name;
                        $si_thumb = new SimpleImage();
                        $si_thumb->load($game_image_temp);
                        $si_thumb->cropImage(640,640, 1);
                        $si_thumb->save($uploadDestFullPath_desktop, $si_thumb->image_type, 100);
                        $imageOptimizer = app(ImageOptimizer::class);
                        $imageOptimizer->setDebug(false);
                        $imageOptimizer->optimizeImage($uploadDestFullPath_desktop);

                        $gameCatalogue->game_image = $new_desktop_file_name;

                        $new_mobile_file_name = $code.'-game-mobile.'.$tmpFileExt;
                        $uploadDestFullPath_mobile = public_path('uploads/advent') . DIRECTORY_SEPARATOR . $new_mobile_file_name;
                        $si_thumb = new SimpleImage();
                        $si_thumb->load($game_image_temp);
                        $si_thumb->cropImage(420,420, 1);
                        $si_thumb->save($uploadDestFullPath_mobile, $si_thumb->image_type, 100);
                        $imageOptimizer = app(ImageOptimizer::class);
                        $imageOptimizer->setDebug(false);
                        $imageOptimizer->optimizeImage($uploadDestFullPath_mobile);

                        $new_4k_file_name = $code.'-game-4k.'.$tmpFileExt;
                        $uploadDestFullPath_4k = public_path('uploads/advent') . DIRECTORY_SEPARATOR . $new_4k_file_name;
                        if($game_image_width >= 1280)
                        {
                            $si_thumb = new SimpleImage();
                            $si_thumb->load($game_image_temp);
                            $si_thumb->cropImage(1280,1280, 1);
                            $si_thumb->save($uploadDestFullPath_4k, $si_thumb->image_type, 100);
                            $imageOptimizer = app(ImageOptimizer::class);
                            $imageOptimizer->setDebug(false);
                            $imageOptimizer->optimizeImage($uploadDestFullPath_4k);
                        }
                        else{
                            copy($game_image_temp, $uploadDestFullPath_4k);
                            $imageOptimizer = app(ImageOptimizer::class);
                            $imageOptimizer->setDebug(false);
                            $imageOptimizer->optimizeImage($uploadDestFullPath_4k);
                        }    

                    }
                    
                    if($screenshot_flag && $master_flag)
                        $gameCatalogue->save();

                }    
            }    
        }

    }

    public function updatePublisher(Request $request){

        $games = VideoCreatorGame :: select('game_id')->get();

        foreach($games as $g){

            $game = Game :: select('id','name','data_json')->where('id', $g->game_id)->first();

            $game_data_json  = $game->getJsonData(true);
            if(isset($game_data_json['info']['publisher_name']))  {
                $publisher = $game_data_json['info']['publisher_name'];
                
                $publisherObj = Publisher :: where('name', $publisher)->first();
                if(!$publisherObj)
                {
                    $publisherObj = new Publisher();
                    $publisherObj->name = $publisher;
                    $publisherObj->save();
                }    
            }

        }    
    }
}
