<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Framework\src\Http\Models\DataRevision;
use App\Http\Models\GiftCode;
use App\Framework\src\Http\Models\Log;
use App\Http\Requests\GiftCodeSaveRequest;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Excel\GiftCodesImport;
use Illuminate\Support\Facades\File;
use DB;

class GiftCodeController extends Controller{
    /**
     * GiftCodeController constructor.
     *
     * @param Request $request
     */
    public function __construct(Request $request){
        $this->middleware('ipcheck');
        $this->middleware('logincheck:admin');
    }

    /**
     * @param Request $request
     *
     * @return Factory|View
     */
    public function index(Request $request){

        $data['active_menu'] = 'gift_codes';
        return view('admin.gift_codes', $data);
    }

    /**
     * @param Request $request
     *
     * @return JSON
     */
    public function ajaxList(Request $request){

        $draw   = intval($_GET["draw"]);
        $start  = intval($_GET["start"]);
        $length = intval($_GET["length"]);
        $search = $_GET["search"];
        $order  = $_GET["order"];

        $search_where = "";
        if(isset($search['value']) && $search['value'] != ""){
            $search_val   = sanitizeStr($search['value']);
            $search_val_e = DB::connection()->getPdo()->quote('%' . $search_val . '%');
            $search_where = "(code LIKE $search_val_e)";
        }

        //get count
        $gift_codes  = $search_where ? GiftCode::whereRaw($search_where)->get() : GiftCode::get();
        $total_count = $gift_codes->count();

        //get order by
        $sort_cols = array(0 => "code", 1 => "tickets", 2 => "used");
        if(in_array($order[0]['column'], array_keys($sort_cols))){
            $sort_col   = $order[0]['column'];
            $sort_order = $order[0]['dir'];
        }

        //get gift_codes
        $gift_codes = GiftCode::orderBy($sort_cols[$sort_col], $sort_order)->take($length)->skip($start);
        if($search_where){
            $gift_codes = $gift_codes->whereRaw($search_where);
        }
        $gift_codes = $gift_codes->get();

        //table data
        $data = array();
        foreach($gift_codes as $gift_code){

            $data[] = array(
                $gift_code->code,
                $gift_code->tickets,
                $gift_code->used,
                '<div class="hstack gap-2"><a href="javascript:;" class="btn btn-info btn-copy-overview" data-clipboard-text="' . $gift_code->code . '">Copy to Clipboard</a><br><span class="mt-2 text-success d-none">Code copied!</span></div>',
            );
        }

        $output = array(
            "draw"            => $draw,
            "recordsTotal"    => $total_count,
            "recordsFiltered" => $total_count,
            "data"            => $data
        );
        echo json_encode($output);
    }


    /**
     * @param Request $request
     * @param         $id
     *
     * @return Factory|View
     */
    public function edit(Request $request){

        $data['active_menu'] = 'gift_codes';
        return view('admin.gift_code_form', $data);
    }


    /**
     * @param GiftCodeSaveRequest $request
     * @param int                 $id
     *
     * @return RedirectResponse
     */
    public function save(GiftCodeSaveRequest $request){

        /* @var GiftCode $gift_code */
        $gift_code         = new GiftCode();
        $gift_code->code   = sanitizeStr($request->input("code"));
        $gift_code->tickets = (int)sanitizeStr($request->input("tickets"));
        $gift_code->save();

        //DataRevision::incrementRevision();
		
        Log::addFromRequest($request, shortClassMethod(__METHOD__));
        return redirect()->route("admin_gift_codes");
    }

    public function importCodes(Request $request){

        // Validation
        $allowedFileExts = ['xlsx'];
        if(!isset($_FILES['codes_file']) || $_FILES['codes_file']['error'] != 0){
            die("Upload error!");
        }

        $fileExt = request()->codes_file->getClientOriginalExtension();
        if(!in_array($fileExt, $allowedFileExts)){
            die("'" . $fileExt . "' file extension is not supported!");
        }

        // Move upload to temporary save location
        $tmpStorageDir = storage_path("tmp") . DIRECTORY_SEPARATOR . "uploads" . DIRECTORY_SEPARATOR;
        $tmpUploadFile = time() . rand() . ".tmp." . $fileExt;
        request()->codes_file->move($tmpStorageDir, $tmpUploadFile);
        $tmpFileFullPath = $tmpStorageDir . $tmpUploadFile;

        //import retailers
        Excel::import(new GiftCodesImport, $tmpFileFullPath);

        // Remove original un-sanitized file
        @File::delete($tmpFileFullPath);

        // Logging
        Log::addFromRequest($request, shortClassMethod(__METHOD__), ["file" => @basename($tmpFileFullPath)]);

        // Return
        return response()->json([
            "success" => 1,
        ]);
    }

}
