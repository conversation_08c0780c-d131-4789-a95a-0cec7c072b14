<?php

namespace App\Http\Controllers\Admin;

use App\Code\GoogleAuthenticator;
use App\Http\Controllers\Controller;
use DB;
use Framework\src\Http\Models\Admin;
use Framework\src\Http\Models\AdminPassword;
use Framework\src\Http\Models\Log;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\View\View;

class AdminController extends Controller{

    /**
     * AdminController constructor.
     */
    public function __construct(){
        $this->middleware('ipcheck');
        $this->middleware('logincheck');
        $this->middleware('admincheck');
    }

    /**
     * @param Request $request
     *
     * @return Application|Factory|View
     */
    public function listAdmin(Request $request){
        $data['admins']      = Admin::selectRaw("*," . aesDecypt('email'))->get();
        $data['active_menu'] = 'admin';
        return view('admin.admin_list', $data);
    }

    /**
     * @param Request $request
     *
     * @return JSON
     */
    public function ajaxList(Request $request){

        $draw   = intval($_GET["draw"]);
        $start  = intval($_GET["start"]);
        $length = intval($_GET["length"]);
        $search = $_GET["search"];
        $order  = $_GET["order"];

        $search_where = "";
        if(isset($search['value']) && $search['value'] != ""){
            $search_val   = sanitizeStr($search['value']);
            $search_val_e = DB::connection()->getPdo()->quote('%' . $search_val . '%');
            $search_where = "(username LIKE $search_val_e)";
        }

        //get count
		$admins = Admin::selectRaw("*," . aesDecypt('email'));
		if($search_where){
            $admins = $admins->whereRaw($search_where);
        }
        $total_count = $admins->count();

        //get order by
        $sort_cols = array(1 => "id", 2 => "username", 3 => "email", 4 => "ga_secret", 5 => "created_at");
        if(in_array($order[0]['column'], array_keys($sort_cols))){
            $sort_col   = $order[0]['column'];
            $sort_order = $order[0]['dir'];
        }

        //get admins
        $admins = $admins->orderBy($sort_cols[$sort_col], $sort_order)->orderBy('id','desc')->take($length)->skip($start);
        $admins = $admins->get();

        //table data
        $data = array();
        foreach($admins as $admin){

            $actionData = '<div class="text-end">';
            $actionData .= '<a href="#" class="btn btn-sm btn-light btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">Actions '.getSvgIcon('assets/media/icons/duotune/arrows/arr072.svg', 'svg-icon-5 m-0').'</a>';

            $actionData .= '<div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4" data-kt-menu="true">';
            $actionData .= '<div class="menu-item px-3"><a href="'.route('admin_edit', ["id" => $admin->id]).'" class="menu-link px-3">Edit</a></div>';
            $actionData .= '<div class="menu-item px-3"><a href="'.route("admin_regenerate", ["id" => $admin->id]).'" class="menu-link px-3">(Re) Generate</a></div>';
            $actionData .= '<div class="menu-item px-3"><a data-record-id="'.$admin->id.'" class="menu-link px-3" data-kt-record-table-filter="delete_row">Delete</a></div>';
            $actionData .= '</div></div>';

            $data[] = array(
                '<div class="form-check form-check-sm form-check-custom form-check-solid">
                    <input class="form-check-input del-check-input" type="checkbox" value="'.$admin->id.'" />
                </div>',
                $admin->id,
                $admin->username,
                $admin->email,
                ($admin->qrcode ? '<a href="'.$admin->qrcode.'" target="_blank"><img src="'.$admin->qrcode.'" height="100"></a>' : ''),
                date('d.M.Y, h:i A', strtotime($admin->created_at)),
                $actionData,
            );
        }

        $output = array(
            "draw"            => $draw,
            "recordsTotal"    => $total_count,
            "recordsFiltered" => $total_count,
            "data"            => $data
        );
        echo json_encode($output);
    }

    /**
     * @param Request $request
     *
     * @return Application|Factory|View
     */
    public function newAdmin(Request $request){

        $data['active_menu'] = 'admin';
        return view('admin.admin_form', $data);
    }

    /**
     * @param Request $request
     * @param string  $id
     *
     * @return Application|Factory|RedirectResponse|View
     */
    public function editAdmin(Request $request, $id = ''){

        $id    = (int)sanitizeStr($id);
        $admin = Admin::selectRaw("*," . aesDecypt('email'))->where('id', $id)->first();
        if(!$admin)
            return redirect()->route("admin_list_admin");

        $data['admin']       = $admin;
        $data['admin_id']    = $id;
        $data['active_menu'] = 'admin';
        return view('admin.admin_form', $data);
    }

    /**
     * @param Request $request
     * @param string  $id
     *
     * @return RedirectResponse
     */
    public function saveAdmin(Request $request, $id = ''){

        $id = (int)sanitizeStr($id);
        if($id)  {

            if($request->password){
                $request->validate(array(
                    'password' => 'required|Min:8|Max:20|regex:/^.*(?=.{3,})(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[\s\S].*$/',
                ));
            }

            $admin = Admin::selectRaw("*," . aesDecypt('email'))->where('id', $id)->first();
            Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".update", ["id" => $id]);
        }
        else
        {
            $request->validate(array(
                'username' => 'required|unique:admins|alpha_num',
                'email'    => 'required|unique:admins|email',
                'password' => 'required|Min:8|Max:20|regex:/^.*(?=.{3,})(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[\s\S].*$/',
            ));

            $admin           = new Admin;
            $admin->username = sanitizeStr($request->get('username'));
            $admin->email    = sanitizeStr($request->get('email'));
            Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".insert", ["id" => $id]);
        }

        if($request->password)
            $admin->password = Hash::make($request->password);

        $admin->user_type = "admin";
        $admin->save();

        if($request->password) $this->savePassword($request->password, $admin->id);

        return redirect()->route("admin_list_admin");
    }

    /**
     * @param $password
     * @param $admin_id
     */
    function savePassword($password, $admin_id){

        $adminPassword           = new AdminPassword;
        $adminPassword->admin_id = $admin_id;
        $adminPassword->password = $password;
        $adminPassword->save();
    }

    /**
     * @param Request $request
     * @param         $id
     *
     * @return RedirectResponse
     */
    public function deleteAdmin(Request $request){

        $id = (int)sanitizeStr($request->post('record_id'));
        if($id > 0)  {
            Admin::destroy($id);

            Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".destroy", ["id" => $id]);
        }

        $record_ids = sanitizeStr($request->post('record_ids'));

        if($record_ids)  {

            $recordIds = explode(",", $record_ids);

            foreach($recordIds as $recordId)  {

                $id = (int)sanitizeStr($recordId);
                if($id > 0)  {
                    Admin::destroy($id);
                }
            }

            Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".destroy", ["id" => $record_ids]);
        }

        return '1';
    }

    /**
     * @param Request $request
     * @param         $id
     *
     * @return RedirectResponse
     */
    public function regenerate(Request $request, $id){
        Log::addFromRequest($request, shortClassMethod(__METHOD__) . ".refresh", ["id" => $id]);
        $id               = intval(sanitizeStr($id));
        $admin            = Admin::selectRaw("*," . aesDecypt('email'))->where('id', $id)->first();
        $ga               = new GoogleAuthenticator();
        $admin->ga_secret = $ga->createSecret();
        $admin->qrcode    = $ga->getQRCodeGoogleUrl(config("app.google_auth_identifier") . "_" . slugifyString($admin->username), $admin->ga_secret);
        $admin->save();
        return redirect()->route("admin_list_admin");
    }
}
