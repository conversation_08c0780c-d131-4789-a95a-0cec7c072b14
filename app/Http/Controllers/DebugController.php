<?php

namespace App\Http\Controllers;

use App\_modules\MicrosoftScraper\MicrosoftScraper;
use App\_modules\NintendoScraper\NintendoScraper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Psr\SimpleCache\InvalidArgumentException;

class DebugController extends Controller{
    /**
     * @throws InvalidArgumentException
     * @throws \JsonException
     */
    public function index(Request $request){
        $data   = [];
        $type   = $request->query("type") ?: "nintendo";
        $offset = $request->query("offset") ?: 0;
        $limit  = $request->query("limit") ?: 10;

        Log::info("DebugController::index() - type: " . $type . " offset: " . $offset . " data: " . json_encode($data, JSON_THROW_ON_ERROR));

        if($type === "nintendo"){
            $data = NintendoScraper::getProducts($offset, $limit);
        }
        else if($type === "microsoft"){
            $data = MicrosoftScraper::getProducts($offset, $limit);
        }

        Log::info("DebugController::index() - type: " . $type . " offset: " . $offset . " data-count: " . count($data));

        return view('site.debug', ["data" => $data]);
    }
}
