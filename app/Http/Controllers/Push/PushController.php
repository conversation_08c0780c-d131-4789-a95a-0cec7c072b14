<?php

namespace App\Http\Controllers\Push;

use App\Code\Api\RequestLanguage;
use App\Code\External\Api\SalesforceMarketingApi;
use App\Http\Controllers\Controller;
use App\Http\Models\Api\Address;
use App\Http\Models\Api\Country;
use App\Http\Models\Api\EmployeeInfo;
use App\Http\Models\Api\Language;
use App\Http\Models\Api\User;
use App\Http\Models\Api\UserPurchase;
use App\Http\Models\Api\UserVoucher;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Response;

class PushController extends Controller{
    /**
     * Create a new Controller instance.
     *
     * @return void
     */
    public function __construct(){
        // Debug
        //if(isDevEnv()){
        @Log::channel("push")->info(
            sprintf("PUSH %s FROM %s VIA %s ON %s DATA %s", @(string)@request()->fingerprint(),
                @(string)@request()->ip(), @(string)@request()->userAgent(), @(string)@request()->getPathInfo(),
                @(string)@request()->getContent())
        );
        //}
    }

    /**
     * @param Request $request
     *
     * @queryParam data required
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function userVoucher(Request $request){
        // Validate json
        $errors = $this->validatePushJsonRequest();

        // Output errors
        if($errors) return Response::json(['errors' => $errors], 400);

        try{
            // Insert or update data
            $stats    = ["inserted" => 0, "updated" => 0];
            $dataRows = (array)$request->json()->get("data");
            foreach($dataRows as $dataRow){
                // Sanitize input
                $insertData = [];
                $data       = [];
                if(isset($dataRow["voucherID"]) && is_string($dataRow["voucherID"]))
                    $data["voucherID"] = $insertData["voucher_id"] = sanitizeStrFull($dataRow["voucherID"]);

                if(isset($dataRow["type"]) && is_string($dataRow["type"]))
                    $data["type"] = $insertData["type"] = sanitizeStrFull($dataRow["type"]);
                if(array_key_exists("type", $dataRow) && is_null($dataRow["type"]))
                    $data["type"] = $insertData["type"] = "";

                if(isset($dataRow["code"]) && is_string($dataRow["code"]))
                    $data["code"] = $insertData["code"] = sanitizeStrFull($dataRow["code"]);
                if(array_key_exists("code", $dataRow) && is_null($dataRow["code"]))
                    $data["code"] = $insertData["code"] = "";

                if(isset($dataRow["forCardNumber"]) && is_string($dataRow["forCardNumber"]))
                    $data["forCardNumber"] = $insertData["member_id"] = sanitizeStrFull($dataRow["forCardNumber"]);
                if(array_key_exists("forCardNumber", $dataRow) && is_null($dataRow["forCardNumber"]))
                    $data["forCardNumber"] = $insertData["member_id"] = "";

                if(isset($dataRow["forStatus"]) && is_string($dataRow["forStatus"]))
                    $data["forStatus"] = $insertData["for_status"] = sanitizeStrFull($dataRow["forStatus"]);
                if(array_key_exists("forStatus", $dataRow) && is_null($dataRow["forStatus"]))
                    $data["forStatus"] = $insertData["for_status"] = "";

                if(isset($dataRow["redeemed"]) && is_bool($dataRow["redeemed"]))
                    $data["redeemed"] = $insertData["redeemed"] = $dataRow["redeemed"];
                if(array_key_exists("redeemed", $dataRow) && is_null($dataRow["redeemed"]))
                    $data["redeemed"] = $insertData["redeemed"] = "";

                if(isset($dataRow["validFrom"]) && is_string($dataRow["validFrom"]))
                    $data["validFrom"] = $insertData["valid_from"] = sanitizeStrFull($dataRow["validFrom"]);
                if(array_key_exists("validFrom", $dataRow) && is_null($dataRow["validFrom"]))
                    $data["validFrom"] = $insertData["valid_from"] = "";

                if(isset($dataRow["validUntil"]) && is_string($dataRow["validUntil"]))
                    $data["validUntil"] = $insertData["valid_until"] = sanitizeStrFull($dataRow["validUntil"]);
                if(array_key_exists("validUntil", $dataRow) && is_null($dataRow["validUntil"]))
                    $data["validUntil"] = $insertData["valid_until"] = "";

                if(isset($dataRow["lastChanged"]) && is_string($dataRow["lastChanged"]))
                    $data["lastChanged"] = $insertData["last_changed"] = sanitizeStrFull($dataRow["lastChanged"]);
                if(array_key_exists("lastChanged", $dataRow) && is_null($dataRow["lastChanged"]))
                    $data["lastChanged"] = $insertData["last_changed"] = "";

                if(isset($dataRow["sfID"]) && is_string($dataRow["sfID"]))
                    $data["sfID"] = $insertData["salesforce_id"] = sanitizeStrFull($dataRow["sfID"]);
                if(array_key_exists("sfID", $dataRow) && is_null($dataRow["sfID"]))
                    $data["sfID"] = $insertData["salesforce_id"] = "";

                if(isset($dataRow["campaignId"]) && is_string($dataRow["campaignId"]))
                    $data["campaignId"] = $insertData["campaign_id"] = sanitizeStrFull($dataRow["campaignId"]);
                if(array_key_exists("campaignId", $dataRow) && is_null($dataRow["campaignId"]))
                    $data["campaignId"] = $insertData["campaign_id"] = "";

                if(isset($dataRow["contentType"]) && is_string($dataRow["contentType"]))
                    $data["contentType"] = $insertData["content_type"] = sanitizeStrFull($dataRow["contentType"]);
                if(array_key_exists("contentType", $dataRow) && is_null($dataRow["contentType"]))
                    $data["contentType"] = $insertData["content_type"] = "";

                // Validate input
                $errors                  = $this->validatePushUserVoucherData($data);
                $insertData["last_push"] = date("Y-m-d H:i:s");

                // Output errors
                if($errors) return Response::json(['errors' => $errors], 400);

                // Insert or update data rows
                /* @var UserVoucher $userVoucher */
                $insertData["via_push"] = true;
                $userVoucher            = UserVoucher::updateOrCreate([
                    "salesforce_id" => $insertData["salesforce_id"],
                ], $insertData);
                if($userVoucher->wasRecentlyCreated) ++$stats["inserted"];
                else ++$stats["updated"];
            }
        }
        catch(Exception $e){
            Log::critical(sprintf("%s - %s - %s", __METHOD__, $e->getMessage(),
                var_export((array)$request->json()->get("data"), true)));
            return Response::json(['errors' => ["internal" => "error"]], 400);
        }

        // Build return meta
        $meta = [
            "statusKey"        => "vouchersUpdated",
            "statusInfo"       => "The vouchers have been saved.",
            "infoInsertAmount" => $stats["inserted"],
            "infoUpdateAmount" => $stats["updated"],
        ];
        return response()->json(toApiSingleResult([], $meta));
    }

    /**
     * @param Request $request
     *
     * @queryParam cardNumber required
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function userChanged(Request $request){
        // Sanitize user request
        $cardNumber = sanitizeStrFull($request->input("cardNumber"));

        // Validate user input
        $errors = [];
        /* @var User $user */
        if(!$cardNumber) $errors["cardNumber"] = ["errorKey" => "isEmpty", "info" => "No card number supplied."];
        $user = $cardNumber ? User::where("member_id", $cardNumber)->first() : null;
        if(!$user && $cardNumber) $errors["cardNumber"] = ["errorKey" => "notFound", "info" => "Card number not found."];

        // Output errors
        if($errors) return Response::json(['errors' => $errors], 400);

        // Get user main address
        /* @var Address[] $addresss */
        $address          = $user->addresses()->first();
        $address          = $address ?: new Address();
        $address->user_id = $user->id;

        // Sanitize user input
        $lastName              = sanitizeStrFull($request->input("lastName"));
        $firstName             = sanitizeStrFull($request->input("firstName"));
        $accountStatus         = sanitizeStrFull($request->input("accountStatus") ?: $request->input("status"));
        $nextLevelOfMembership = sanitizeStrFull($request->input("nextLevelOfMembership"));
        $amountToNextLevel     = sanitizeStrFull($request->input("amountToNextLevel"));
        $statusExpireAt        = sanitizeStrFull($request->input("statusExpireAt"));
        $statusDiscount        = sanitizeStrFull($request->input("accountStatusDiscount"));
        $statusDiscount        = $statusDiscount ?: sanitizeStrFull($request->input("statusDiscount"));
        $percentToNextLevel    = sanitizeStrFull($request->input("percentToNextLevel"));

        // New fields
        $title                    = sanitizeStrFull($request->input("title"));
        $titleAfter               = sanitizeStrFull($request->input("titleAfter"));
        $gender                   = sanitizeStrFull($request->input("gender"));
        $birthdate                = sanitizeStrFull($request->input("birthdate"));
        $street                   = sanitizeStrFull($request->input("street"));
        $postalCode               = sanitizeStrFull($request->input("postalCode"));
        $city                     = sanitizeStrFull($request->input("city"));
        $country                  = sanitizeStrFull($request->input("country"));
        $mobile                   = sanitizeStrFull($request->input("mobile"));
        $email                    = sanitizeStrFull($request->input("email"));
        $personContactId          = sanitizeStrFull($request->input("personContactId"));
        $sourceOfRegistration     = sanitizeStrFull($request->input("SourceOfregistration"));
        $isDeleted                = (bool)@trim($request->input("isDeleted"));
        if(@trim($request->input("isDeleted")) === "false") $isDeleted = false; // Fix for wrong json datatype
        // Update user
        if($personContactId) $user->person_contact_id = $personContactId;
        if($lastName) $user->last_name = $lastName;
        if($firstName) $user->first_name = $firstName;
        if($accountStatus) $user->status = $accountStatus;
        if($nextLevelOfMembership) $user->next_status = $nextLevelOfMembership;
        if($amountToNextLevel) $user->next_status_amount = $amountToNextLevel;
        if($statusExpireAt) $user->status_expire_at = date("Y-m-d H:i:s", @strtotime($statusExpireAt));
        if($statusDiscount || @(int)$statusDiscount === 0) $user->status_discount = $statusDiscount;
        if($percentToNextLevel) $user->next_status_percent = $percentToNextLevel;
        if(!$user->language_id) $user->language_id = app(RequestLanguage::class)->getActiveLanguageId();

        // New fields
        if($title) $user->title = $title;
        if($titleAfter) $user->title_after = $titleAfter;
        if($gender) $user->gender = stripos($gender, "FEMALE") !== false ? "f" :
            (stripos($gender, "MALE") !== false ? "m" : "d");
        if($birthdate) $user->day_of_birth = date("Y-m-d", @strtotime($birthdate));
        if($street){
            // Street 11/a
            if(stripos($street, "/") !== false && count(explode("/", $street)) < 2){
                $streetCrater          = explode("/", $street);
                $streetAndNumber       = $streetCrater[0] ?? "";
                $streetAdditional      = $streetCrater[1] ?? "";
                $streetAndNumberCrater = explode(" ", $streetAndNumber);
                $streetShort           = $streetAndNumberCrater[0] ?? "";
                $streetNumber          = $streetAndNumberCrater[1] ?? "";
            }
            // Street/11/a
            else if(stripos($street, "/") !== false){
                $streetCrater     = explode("/", $street);
                $streetShort      = $streetCrater[0] ?? "";
                $streetNumber     = $streetCrater[1] ?? "";
                $streetAdditional = $streetCrater[2] ?? "";
            }
            // Street 11 a
            else{
                $streetCrater     = preg_split("/(\d.*)/", $street, -1, PREG_SPLIT_NO_EMPTY | PREG_SPLIT_DELIM_CAPTURE);
                $streetCrater2    = explode(" ", ($streetCrater[1] ?? ""));
                $streetShort      = $streetCrater[0] ?? "";
                $streetNumber     = $streetCrater2[0] ?? "";
                $streetAdditional = implode(" ",
                    array_slice($streetCrater2, 1, count($streetCrater2) - 1));
            }
            $address->street            = $streetShort;
            $address->street_number     = $streetNumber;
            $address->street_additional = $streetAdditional;
        }
        if($postalCode) $address->post_code = $postalCode;
        if($city) $address->city = $city;
        if($country){
            /* @var Language $language */
            $language = Language::where("code", $country)->first();
            $language = $language ?: Language::where("name", $country)->first();
            if($language){
                /* @var Country $country */
                $country = Country::where("language_id", $language->id)->first();
                if($country) $user->country_id = $country->id;
            }
        }
        if($mobile) $user->mobile = $mobile;
        if($email) $user->email = $email;
        if($sourceOfRegistration) $user->source_of_registration = $sourceOfRegistration;
        if($sourceOfRegistration == "" || $sourceOfRegistration == null) {
            $user->source_of_registration = "";
        }
        if($request->input("doiStatus") !== null) {
            $user->doi_status = $request->input("doiStatus") == "allowed";
        }
        if($request->input("marketingNewsOptOut") !== null) {
            $user->newsletter_sub = $request->input("marketingNewsOptOut") == "true" ? 0 : 1;
        }
        if($request->input("NLClubNewsOptOut") !== null) {
            $user->club_newsletter_sub = $request->input("NLClubNewsOptOut") == "true" ? 0 : 1;
        }
        if($request->input("NLStatusInfoOptOut") !== null) {
            $user->club_status_newsletter_sub = $request->input("NLStatusInfoOptOut") == "true" ? 0 : 1;
        }
        if($request->input("NLBirthdayOptOut") !== null) {
            $user->birthday_newsletter_sub = $request->input("NLBirthdayOptOut") == "true" ? 0 : 1;
        }

        // Save
        $user->save();
        $address->save();

        // Check salesforce api for employee info
        $employeeInfo = null;
        $responseData = @app(SalesforceMarketingApi::class)->getAccountViaCardNum($user->member_id);
        if(isset($responseData["employeeInfo"]) && is_array($responseData["employeeInfo"])
            && $responseData["employeeInfo"]){
            /* @var EmployeeInfo $employeeInfo */
            $employeeInfo                   = @$user->employeeInfo()->first();
            $employeeInfo                   = $employeeInfo ?: new EmployeeInfo();
            $employeeInfo->photo_gfx        = @$responseData["employeeInfo"]["photo"];
            $employeeInfo->company_logo_gfx = @$responseData["employeeInfo"]["companyLogo"];
            $employeeInfo->company          = @$responseData["employeeInfo"]["company"];
            $employeeInfo->save();
            $user->employee_info_id = $employeeInfo->id;
            $user->is_employee      = true;
            $user->save();
        }
        else{
            $employeeInfo = @$user->employeeInfo()->first();
            if($employeeInfo) @$employeeInfo->delete();
            $user->employee_info_id = null;
            $user->is_employee      = false;
            $user->save();
        }

        // Mark user as deleted
        if($isDeleted){
            $user->delete();
            $address->delete();
            if($employeeInfo) $employeeInfo->delete();
            // Log
            @Log::channel("push")->info("MARK_DELETED - User {$user->id} has been marked as deleted.");
        }

        // Fetch country
        /* @var Country $country */
        $country = Country::where("id", $user->country_id)->first();

        // Build return data
        $returnData = [
            "accountStatus"            => $user->status,
            "firstName"                => $user->first_name,
            "lastName"                 => $user->last_name,
            "nextLevelOfMembership"    => $user->next_status,
            "amountToNextLevel"        => $user->next_status_amount,
            "statusExpireAt"           => $user->status_expire_at,
            "accountStatusDiscount"    => $user->status_discount,
            "percentToNextLevel"       => $user->next_status_percent,
            // New fields
            "title"                    => $user->title,
            "titleAfter"               => $user->title_after,
            "gender"                   => @["m" => "MALE", "f" => "FEMALE", "d" => "UNKNOWN"][$user->gender],
            "birthdate"                => $user->day_of_birth,
            "street"                   => getSfStreet($address->street, $address->street_number, $address->street_additional),
            "postalCode"               => $address->post_code,
            "city"                     => $address->city,
            "country"                  => @strtoupper($country->getLanguageCode()),
            "mobile"                   => $user->mobile,
            "email"                    => $user->email,
            "marketingNewsOptOut"      => !$user->newsletter_sub,
            "nlClubNewsOptOut"         => !$user->club_newsletter_sub,
            "isDeleted"                => (bool)$isDeleted,
        ];

        // Build return meta
        $meta = [
            "statusKey"  => "userUpdated",
            "statusInfo" => "The user has been updated.",
        ];

        return response()->json(toApiSingleResult($returnData, $meta));
    }

    /**
     * @param Request $request
     *
     * @queryParam cardNumber required
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function userDeleted(Request $request){
        // Sanitize user request
        $salesforce_id = sanitizeStrFull($request->input("salesforce_id"));

        // Validate user input
        $errors = [];
        /* @var User $user */
        if(!$salesforce_id) $errors["salesforce_id"] = ["errorKey" => "isEmpty", "info" => "No salesforce id supplied."];
        $user = $salesforce_id ? User::where("salesforce_id", $salesforce_id)->first() : null;
        if(!$user && $salesforce_id) $errors["salesforce_id"] = ["errorKey" => "notFound", "info" => "Salesforce id not found."];

        // Output errors
        if($errors) return Response::json(['errors' => $errors], 400);

        User::deleteViaGdpr($user);

        $isDeleted =  $user->deleted_via_gdpr;

        // Build return data
        $returnData = [
            "isDeleted"                => (bool)$isDeleted,
        ];

        // Build return meta
        $meta = [
            "statusKey"  => "userDeleted",
            "statusInfo" => "The user has been deleted.",
        ];

        return response()->json(toApiSingleResult($returnData, $meta));
    }

    /**
     * @param Request $request
     *
     * @queryParam data required
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function userPurchase(Request $request){
        // Validate json
        $errors = $this->validatePushJsonRequest();

        // Output errors
        if($errors) return Response::json(['errors' => $errors], 400);

        try{
            // Insert or update data
            $stats    = ["inserted" => 0, "updated" => 0, "missing" => 0];
            $dataRows = (array)$request->json()->get("data");
            foreach($dataRows as $dataRow){
                // Validate input
                $errors = $this->validatePushUserPurchaseData($dataRow);

                // Output errors
                if($errors) return Response::json(['errors' => $errors], 400);

                // Sanitize input
                $insertData = [];

                if(!strExists($dataRow, "forCardNumber")) continue;
                else{
                    $user = User::getByField("member_id", $dataRow["forCardNumber"]);
                    if($user) $insertData["user_id"] = $user->id;
                    else{
                        $stats["missing"] += 1;
                        // continue; // Still insert data
                    }
                }

                if(itemExists($dataRow, "totalAmount"))
                    $insertData["total_amount"] = doubleval(sanitizeStrFull($dataRow["totalAmount"]));

                if(strExists($dataRow, "purchaseId"))
                    $insertData["purchase_id"] = sanitizeStrFull($dataRow["purchaseId"]);

                if(strExists($dataRow, "purchaseDate"))
                    $insertData["purchase_date"] = @date("Y-m-d", @strtotime(sanitizeStrFull($dataRow["purchaseDate"])));

                if(strExists($dataRow, "lastChanged"))
                    $insertData["last_changed"] = sanitizeStrFull($dataRow["lastChanged"]);

                if(strExists($dataRow, "forCardNumber"))
                    $insertData["member_id"] = sanitizeStrFull($dataRow["forCardNumber"]);

                if(strExists($dataRow, "sfID"))
                    $insertData["salesforce_id"] = sanitizeStrFull($dataRow["sfID"]);

                if(strExists($dataRow, "customerSfId"))
                    $insertData["customer_id"] = sanitizeStrFull($dataRow["customerSfId"]);

                // Insert or update data rows
                $insertData["via_push"] = true;
                /* @var UserPurchase $userPurchase */
                $userPurchase = UserPurchase::updateOrCreate([
                    "salesforce_id" => $insertData["salesforce_id"],
                ], $insertData);
                if($userPurchase->wasRecentlyCreated) $stats["inserted"] += 1;
                else $stats["updated"] += 1;
            }
        }
        catch(Exception $e){
            Log::critical(sprintf("%s - %s - %s", __METHOD__, $e->getMessage(),
                var_export((array)$request->json()->get("data"), true)));
            return Response::json(['errors' => ["internal" => "error"]], 400);
        }

        // Build return meta
        $meta = [
            "statusKey"         => "purchaseChangesUpdated",
            "statusInfo"        => "The purchase changes have been saved.",
            "infoInsertAmount"  => $stats["inserted"],
            "infoUpdateAmount"  => $stats["updated"],
            "infoMissingAmount" => $stats["missing"],
        ];
        return response()->json(toApiSingleResult([], $meta));
    }

    // -------------- Validation --------------

    protected function validatePushJsonRequest(){
        $errors = [];
        if(!request()->isJson() || !isset(request()->all()["data"]))
            $errors["body"] = ["errorKey" => "invalidJsonRequest", "info" => "The json request is invalid."];
        else if(!is_array(request()->all()["data"]))
            $errors["body"] = ["errorKey" => "invalidJsonDataField", "info" => "The data field should by a array."];
        return $errors;
    }

    function validatePushUserVoucherData($data = []){
        $defaultTextFields = ["voucherID", "type", "code", "forCardNumber", "forStatus", "sfID", "campaignId", "contentType"];
        $defaultTimeFields = ["lastChanged"];
        $errors            = [];
        // Validate text fields
        foreach(array_merge($defaultTextFields, $defaultTimeFields) as $optionalTextField)
            if(isset($data[$optionalTextField]) && is_string($data[$optionalTextField]) && strlen($data[$optionalTextField]) > 128)
                $errors[$optionalTextField] = ["errorKey" => "tooLong", "info" => "The text should not be longer than 128 characters."];
        // Validate key field
        if(!isset($data["sfID"]))
            $errors["sfID"] = ["errorKey" => "missingMandatory", "info" => "A mandatory field is missing."];
        else if(!$data["sfID"])
            $errors["sfID"] = ["errorKey" => "empty", "info" => "A mandatory field is empty."];
        // Validate time fields
        foreach($defaultTimeFields as $optionalTimeField){
            if(isset($data[$optionalTimeField]) && !correctDateTime($data[$optionalTimeField]))
                $errors[$optionalTimeField] = ["errorKey" => "invalidFormat", "info" => "The datetime field has a incorrect format. (Needs to be Y-m-d H:i:s)"];
        }
        return $errors;
    }

    function validatePushUserPurchaseData($data = []){
        $defaultTextFields      = ["forCardNumber", "purchaseId", "customerSfId"];
        $defaultTimeFields      = ["lastChanged"];
        $defaultDateFields      = ["purchaseDate"];
        $defaultNumberFields    = ["totalAmount"];
        $defaultMandatoryFields = ["totalAmount", "purchaseId", "purchaseDate", "lastChanged", "forCardNumber",
                                   "customerSfId", "sfID"];
        $errors                 = [];
        // Validate text fields
        foreach(array_merge($defaultTextFields, $defaultTimeFields) as $optionalTextField)
            if(isset($data[$optionalTextField]) && is_string($data[$optionalTextField]) && strlen($data[$optionalTextField]) > 128)
                $errors[$optionalTextField] = ["errorKey" => "tooLong", "info" => "The text should not be longer than 128 characters."];
        // Validate key field
        foreach($defaultMandatoryFields as $defaultMandatoryField){
            if(!isset($data[$defaultMandatoryField]))
                $errors[$defaultMandatoryField] = ["errorKey" => "missingMandatory", "info" => "A mandatory field is missing."];
            else if(!$data[$defaultMandatoryField] && $data[$defaultMandatoryField] !== 0 && !is_string($data[$defaultMandatoryField]))
                $errors[$defaultMandatoryField] = ["errorKey" => "empty", "info" => "A mandatory field is empty."];
        }
        // Validate time fields
        foreach($defaultTimeFields as $optionalTimeField){
            if(isset($data[$optionalTimeField]) && !correctDateTime($data[$optionalTimeField]))
                $errors[$optionalTimeField] = ["errorKey" => "invalidFormat", "info" => "The datetime field has a incorrect format. (Needs to be Y-m-d H:i:s)"];
        }
        foreach($defaultDateFields as $optionalDateField){
            if(isset($data[$optionalDateField]) && !correctDate($data[$optionalDateField]))
                $errors[$optionalDateField] = ["errorKey" => "invalidFormat", "info" => "The date field has a incorrect format. (Needs to be Y-m-d)"];
        }
        // Validate number fields
        foreach($defaultNumberFields as $optionalNumberField){
            if(isset($data[$optionalNumberField]) && !is_numeric($data[$optionalNumberField]))
                $errors[$optionalNumberField] = ["errorKey" => "invalidFormat", "info" => "The number format is invalid."];
        }
        return $errors;
    }

    function validatePushVoucherInfoData($data = []){
        $defaultTextFields       = ["forVoucher", "gfxUrl", "size", "category"];
        $defaultMediumTextFields = ["actionUrl"];
        $defaultLongTextFields   = ["headLine"];
        $defaultHtmlFields       = ["excerpt", "content"];
        $defaultTimeFields       = ["lastChanged"];
        $errors                  = [];
        // Validate text fields
        foreach(array_merge($defaultTextFields, $defaultTimeFields) as $optionalTextField)
            if(isset($data[$optionalTextField]) && is_string($data[$optionalTextField]) && strlen($data[$optionalTextField]) > 128)
                $errors[$optionalTextField] = ["errorKey" => "tooLong", "info" => "The text should not be longer than 128 characters."];
        foreach($defaultLongTextFields as $optionalLongTextField)
            if(isset($data[$optionalLongTextField]) && is_string($data[$optionalLongTextField]) && strlen($data[$optionalLongTextField]) > 1024)
                $errors[$optionalLongTextField] = ["errorKey" => "tooLong", "info" => "The text should not be longer than 1024 characters."];
        foreach($defaultHtmlFields as $optionalHtmlField)
            if(isset($data[$optionalHtmlField]) && is_string($data[$optionalHtmlField]) && strlen($data[$optionalHtmlField]) > 10000000)
                $errors[$optionalHtmlField] = ["errorKey" => "tooLong", "info" => "The text should not be longer than 10000000 characters."];
        foreach($defaultMediumTextFields as $optionalMediumTextField)
            if(isset($data[$optionalMediumTextField]) && is_string($data[$optionalMediumTextField]) && strlen($data[$optionalMediumTextField]) > 256)
                $errors[$optionalMediumTextField] = ["errorKey" => "tooLong", "info" => "The text should not be longer than 256 characters."];
        // Validate key field
        if(!isset($data["forVoucher"]))
            $errors["forVoucher"] = ["errorKey" => "missingMandatory", "info" => "A mandatory field is missing."];
        else if(!$data["forVoucher"])
            $errors["forVoucher"] = ["errorKey" => "empty", "info" => "A mandatory field is empty."];
        // Validate time fields
        foreach($defaultTimeFields as $optionalTimeField){
            if(isset($data[$optionalTimeField]) && !correctDateTime($data[$optionalTimeField]))
                $errors[$optionalTimeField] = ["errorKey" => "invalidFormat", "info" => "The datetime field has a incorrect format. (Needs to be Y-m-d H:i:s)"];
        }
        return $errors;
    }
}
