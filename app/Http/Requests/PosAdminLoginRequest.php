<?php

namespace App\Http\Requests;

use App\Http\Models\Admins;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class PosAdminLoginRequest extends FormRequest{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(){
        return true; // Only check if non public request
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(){
        $messages = $this->messages();
        $password = sanitizeStrFull($this->input("password"));
        return [
            'username' => ['required', static function ($attribute, $value, $fail) use ($messages, $password){
                if(!$password) return;
                $user = Admins::selectRaw("*," . AESdecypt('email'))
                              ->where($attribute, sanitizeStrFull($value))->first();
                if(!$user) $fail(@$messages["username.not_found"]);
                else if(!Hash::check($password, $user->password)) $fail(@$messages["password.wrong"]);
                else if($user->user_type !== "pos_employee") $fail(@$messages["username.wrong_type"]);
            }],
            'password' => ['required'],
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param \Illuminate\Contracts\Validation\Validator $validator
     *
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function failedValidation(Validator $validator){
        addPosAdminInfo("error", webText("pos_admin:info.login_error", "Login fehlgeschlagen!"));
        throw (new ValidationException($validator))
            ->errorBag($this->errorBag)
            ->redirectTo($this->getRedirectUrl());
    }

    /**
     * Custom message for validation
     *
     * @return array
     */
    public function messages(){
        return [
            'username.required'   => 'Bitte gebe deinen Benutzernamen ein.',
            'username.not_found'  => 'Benutzer nicht gefunden.',
            'username.wrong_type' => 'Benutzer ist kein "POS Employee".',
            'password.wrong'      => 'Falsches Passwort.',
            'password.required'   => 'Bitte gebe dein Passwort ein.',
        ];
    }
}
