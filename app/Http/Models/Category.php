<?php
namespace App\Http\Models;

use Eloquent;
use App\Framework\src\Http\Traits\ManageableModelTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property integer id
 * @property string  category_name
 * @property string  category_icon
 * @property string  institute_id
 * @property boolean is_financing

 * @property string  created_at
 * @property string  updated_at
 *
 * @mixin Builder
 * @mixin Eloquent
 */
class Category extends Model{
	use ManageableModelTrait,SoftDeletes;

    protected $table   = 'categories';
    protected $guarded = [];
	
    protected static $NON_ID_FIELDS = [

		"category_name"     => ["type" => "string", "length" => 250, "null" => true],
        "category_icon"     => ["type" => "string", "length" => 250, "null" => true],
        "institute_id"   	=> ["type" => "string", "length" => 50, "null" => true, "index" => true],
        "is_financing"      => ["type" => "boolean", "default" => 0],
        
        // ---
        "created_at" => ["type" => "created_at", "null" => true],
        "updated_at" => ["type" => "updated_at"],

        // Soft delete
        "deleted_at" => ["type" => "created_at", "null" => true], // Add this for soft delete
    ];
}