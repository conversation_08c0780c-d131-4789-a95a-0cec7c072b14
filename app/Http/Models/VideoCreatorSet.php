<?php
namespace App\Http\Models;

use Framework\src\Http\Traits\ManageableModelTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * @property integer id
 * @property string set_name
 * @property string  video_template
 *
 * @property string  created_at
 * @property string  updated_at
 *
 * @mixin Builder
 */
class VideoCreatorSet extends Model{
    use ManageableModelTrait;

    protected        $table    = 'video_creator_sets';
    protected        $guarded  = [];
    protected static $cacheMap = [];

    protected static array $NON_ID_FIELDS = [

        "set_name"          => ["type" => "string", "length" => 100, "null" => true],
        "video_template"    => ["type" => "string", "length" => 100, "null" => true],
        
        // ----
        "created_at" => ["type" => "created_at", "null" => true],
        "updated_at" => ["type" => "updated_at", "null" => true],
    ];
}
