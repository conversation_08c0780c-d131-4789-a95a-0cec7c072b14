<?php

namespace App\Http\Models;

use App\Framework\src\Http\Traits\EncryptableTrait;
use App\Framework\src\Http\Traits\ManageableModelTrait;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property integer  id
 * @property string   institute_id
 * @property bigint   user_id
 * @property string   offer_code
 * @property string   customer_kukurz
 * @property blob     customer_name
 * @property blob     customer_info
 * @property bigint   cover_image_id
 * @property bigint   letter_template_id
 * @property text     intro_text
 * @property string   product_ids
 * @property longtext products_info
 * @property text     offer_css
 * @property text     mail_subject
 * @property text     mail_body
 * @property blob     cust_email
 * @property blob     cust_address
 * @property integer  version
 * @property string   created_at
 * @property string   updated_at
 *
 * @mixin Builder
 * @mixin Eloquent
 */
class Offer extends Model{

    use ManageableModelTrait;
    use SoftDeletes;
    use EncryptableTrait;

    protected static $NON_ID_FIELDS = [
        "institute_id"       => ["type" => "string", "length" => 50, "null" => true, "index" => true],   //e.g. 0198
        "user_id"            => ["type" => "bigint", "null" => true, "unsigned" => true, "foreign" => ["table" => "users", "field" => "id"]],
        "offer_code"         => ["type" => "string", "length" => 50, "null" => true, "index" => true],   //for offer link
        "customer_kukurz"    => ["type" => "string", "length" => 50, "null" => true],
        "customer_name"      => ["type" => "blob", "null" => true],
        "customer_info"      => ["type" => "blob", "null" => true],
        "cover_image_id"     => ["type" => "bigint", "null" => true, "unsigned" => true, "foreign" => ["table" => "cover_images", "field" => "id"]],
        "letter_template_id" => ["type" => "bigint", "null" => true, "unsigned" => true, "foreign" => ["table" => "letter_templates", "field" => "id"]],
        "intro_text"         => ["type" => "text", "null" => true],
        "product_ids"        => ["type" => "string", "length" => 500, "null" => true],
        "products_info"      => ["type" => "longtext", "null" => true],
        "offer_css"          => ["type" => "text", "null" => true],
        "mail_subject"       => ["type" => "text", "null" => true],
        "mail_body"          => ["type" => "text", "null" => true],
        "cust_email"         => ["type" => "blob", "null" => true],
        "cust_address"       => ["type" => "blob", "null" => true],
        "version"            => ["type" => "integer", "null" => true, "default" => 0],
        // ---
        "created_at"         => ["type" => "created_at", "null" => true],
        "updated_at"         => ["type" => "updated_at"],

        // Soft delete
        "deleted_at"         => ["type" => "created_at", "null" => true], // Add this for soft delete
    ];
    protected        $table         = 'offers';
    protected        $guarded       = [];
    protected        $encryptable   = ['customer_name', 'customer_info', 'cust_email', 'cust_address'];

    public static function getNextVersion($costumerKukurz){
        $obj = new Offer;
        $query = $obj->where('customer_kukurz', $costumerKukurz);
        $query->orderBy('version', 'desc');
        $query->limit(1);
        $result = $query->first();
        if($result){
            return $result->version + 1;
        }
        return 0;
    }

    public static function encryptableFields(){
        $obj = new Offer;
        return $obj->encryptable;
    }
}
