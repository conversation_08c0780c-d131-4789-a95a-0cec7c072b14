<?php

namespace App\Http\Models;

use Eloquent;
use App\Framework\src\Http\Traits\ManageableModelTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * @property integer id
 * @property integer category_id
 * @property string  text

 * @property string  created_at
 * @property string  updated_at
 *
 * @mixin Builder
 * @mixin Eloquent
 */
class TextTemplate extends Model{
	use ManageableModelTrait;

    protected $table   = 'text_templates';
    protected $guarded = [];
	
    protected static $NON_ID_FIELDS = [
		
        "category_id"       => ["type" => "string", "length" => 256, "null" => false],
        "template_text"   	=> ["type" => "text", 'default' => ''],
        
        // ---
        "created_at" => ["type" => "created_at", "null" => true],
        "updated_at" => ["type" => "updated_at"],
    ];

}