<?php

namespace App\Http\Models;

use Eloquent;
use App\Framework\src\Http\Traits\ManageableModelTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * @property integer id
 * @property string  year
 * @property integer subscriber
 * @property float   growth
 * @property integer videos
 * @property integer views
 * @property integer engagements
 * @property integer likes
 * @property integer dislikes
 * @property integer comments
 * @property integer api_total_views
 * @property integer api_total_watch
 * @property integer api_engagements
 * @property integer api_likes
 * @property integer api_dislikes
 * @property integer api_comments
 * @property integer api_shares
 * @property string  top_videos
 * @property integer is_locked
 * @property string  created_at
 * @property string  updated_at
 *
 * @mixin Builder
 * @mixin Eloquent
 */
class YoutubeYearOverviewData extends Model{
    use ManageableModelTrait;

    protected $table   = 'youtube_year_overview_data';
    protected $guarded = [];

    protected static $NON_ID_FIELDS = [
        "year"            => ["type" => "string", "length" => 20, "null" => true, "index" => true],
        "subscriber"      => ["type" => "big_integer", "unsigned" => true, "null" => true],
        "growth"          => ["type" => "decimal", "null" => true],
        "videos"          => ["type" => "big_integer", "unsigned" => true, "null" => true],
        "views"           => ["type" => "big_integer", "unsigned" => true, "null" => true],
        "engagements"     => ["type" => "big_integer", "unsigned" => true, "null" => true],
        "likes"           => ["type" => "big_integer", "unsigned" => true, "null" => true],
        "dislikes"        => ["type" => "big_integer", "unsigned" => true, "null" => true],
        "comments"        => ["type" => "big_integer", "unsigned" => true, "null" => true],
        "api_total_views" => ["type" => "big_integer", "unsigned" => true, "null" => true],
        "api_total_watch" => ["type" => "big_integer", "unsigned" => true, "null" => true],   //In minutes
        "api_engagements" => ["type" => "big_integer", "unsigned" => true, "null" => true],
        "api_likes"       => ["type" => "big_integer", "unsigned" => true, "null" => true],
        "api_dislikes"    => ["type" => "big_integer", "unsigned" => true, "null" => true],
        "api_comments"    => ["type" => "big_integer", "unsigned" => true, "null" => true],
        "api_shares"      => ["type" => "big_integer", "unsigned" => true, "null" => true],
        "top_videos"      => ["type" => "text", "null" => true],
        "is_locked"       => ["type" => "int", "default" => 0, "index" => true],
        // ---
        "created_at"      => ["type" => "created_at", "null" => true],
        "updated_at"      => ["type" => "updated_at"],
    ];
}
