<?php

namespace App\Http\Middleware;

use Closure;
use Framework\src\Http\Models\Admin;
use Illuminate\Support\Facades\Request;

class LoginCheck{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param Closure                  $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next){
        if($request->session()->has('isLogined') && $request->session()->get('isLogined') === "game_catalouge"){
            $adminId = $request->session()->get('backend_user_id');
            $admin   = Admin::selectRaw("*," . aesDecypt('email'))->where('id', $adminId)->first();
            if($admin){
                $request->session()->put('backend_user', $admin);
                $handled = Request::is('*change-password*') || Request::is('*change-pass*') || Request::is('*save-pass*');
                if(!$admin->pass_expire && !$handled) return redirect('admin/change-password/first');
                else if(time() > $admin->pass_expire && !$handled) return redirect('admin/change-password/expire');
                else return $next($request);
            }
            else $request->session()->flush();
        }
        return redirect('admin/login');
    }
}
