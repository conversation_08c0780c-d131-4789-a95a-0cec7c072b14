<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class IPCheck{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next){
        $serverName = $_SERVER["SERVER_NAME"] ?? "";

        //get allowed ips
        $ip_locks_array = explode(",", fS("app_settings.allowed_ips", "*************"));

        if($serverName == "localhost") $ip_locks_array = array_merge($ip_locks_array, ["127.0.0.1", "::1"]);
        if(!in_array($request->ip(), $ip_locks_array)){
            return redirect('https://www.sparkasse.at/sgruppe/privatkunden');
        }
        
        return $next($request);
    }
}
