<?php

use App\_modules\BlogPromotionCore\BlogPromotionCore;
use App\_modules\OpenrouterApi\AiInferenceExecutor;
use App\_modules\OpenrouterApi\Models\AiInference;
use App\Code\GoogleClient;
use App\Code\Metadata;
use App\Code\SimpleImageCC;
use App\Framework\src\Code\RequestLanguage;
use App\Framework\src\Http\Models\DataRevision;
use App\Framework\src\Http\Models\Log;
use App\Http\Models\BlogPromotion\BlogPromotion;
use App\Http\Models\Comment\Comment;
use App\Http\Models\ContentFreigaben\Channel;
use App\Http\Models\ContentFreigaben\ContentFreigaben;
use App\Http\Models\GoogleUser;
use App\Http\Models\Task\GoogleTaskList;
use App\Http\Models\Task\Project;
use App\Http\Models\Task\Task;
use App\Http\Models\Task\TaskComment;
use App\Http\Models\Task\TaskNewUpdate;
use App\Http\Models\SocialPlanning\SocialTopic;
use App\Http\Models\StoreSalePromotion\StoreSaleProduct;
use Facebook\Exceptions\FacebookResponseException;
use Facebook\Exceptions\FacebookSDKException;
use Facebook\Facebook;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

function module_text_prefixes(){

    $prefixArr = [
        'account' => 'y_settings,z_sk_quote_de,z_sk_quote_en,z_at_quote_de,z_at_quote_en,z_sk_invoice_de,z_sk_invoice_en,z_at_invoice_de,z_at_invoice_en',
    ];

    return $prefixArr;
}

function module_types($module = false, $field = false){

    $module_types = [
        'account' => [
            'title' => 'Buchhaltung',
        ],
    ];

    if($module !== false){

        $module_data = isset($module_types[$module]) ? $module_types[$module] : false;

        if($field !== false){
            return isset($module_data[$field]) ? $module_data[$field] : "";
        }

        return $module_data;
    }

    return $module_types;
}

function setUserLanguage($locale = ""){
    session()->put("user_language", $locale);
    app(RequestLanguage::class)->setLanguageCode($locale);
}

function getLanguageCodeGroups(){
    return [];
}

function getLanguageGroupFor($locale = ""){ // de_CH
    $locale         = @trim($locale);
    $locale         = $locale ?: getRequestLocale();
    $languageGroups = getLanguageCodeGroups();
    foreach($languageGroups as $languageGroup) if(in_array($locale, $languageGroup)) return $languageGroup;
    return [];
}

function hasLanguageGroups($locale = ""){ // de_CH
    $locale = $locale ?: getRequestLocale();
    return (bool)getLanguageGroupFor($locale);
}

function getRequestLanguageCode(){
    return app(RequestLanguage::class)->getLanguageCode();
}

function getRequestLocale(){
    return app(RequestLanguage::class)->getLanguageLocale();
}

function getRequestRegionCode(){
    return app(RequestLanguage::class)->getLanguageRegion();
}

function getLanguageId($langCode){
    return app(RequestLanguage::class)->getLanguageId($langCode);
}

function getAllowedLanguageCodes(){
    return ["de_DE"];
}

function getDefaultLanguageCode(){
    return "de_DE";
}

function devModeActive(){
    $serverName = $_SERVER["SERVER_NAME"] ?? "";
    return in_array($serverName, ["localhost", "dev09.contentcreators.at"]);
}

function is_localhost(){

    if(devModeActive())
        return @$_SERVER['SERVER_NAME'] == "localhost";
    else
        return false;
}

function test_mode(){
    // @TODO: disable!
    return dev_test_mode();
}

function dev_test_mode(){
    return $_SERVER['SERVER_NAME'] == "localhost" || $_SERVER['SERVER_NAME'] == "dev09.contentcreators.at";
}

function gamecatalogeApi($api_path, $method = "GET", $data = []){

    $api_base_url = "https://sied02.contentcreators.at/api/";
    $api_token    = "2|WR5HexBNeAfzM5FM5TJsoWDfQj8Qb8mFS4QYmMk7";

    $api_url = $api_base_url . $api_path;

    $response = call_curl($api_url . '?token=' . $api_token, $method, $data);
    $response = $response ? json_decode($response, true) : [];

    return $response;
}

function readCsvFile($filePath = "", $columnAmount = 2){
    $retData = [];
    if(($handle = fopen($filePath, 'r')) !== false){
        $header = fgetcsv($handle); // First row -> headers
        while(($data = fgetcsv($handle)) !== false){
            $retData[] = $data;
            unset($data);
        }
        fclose($handle);
    }
    return $retData;
}

function get_youtube_video($youtube_id){

    $general_video_info = file_get_contents("https://www.googleapis.com/youtube/v3/videos?id=" . $youtube_id . "&part=snippet,statistics,contentDetails&key=AIzaSyCOEcfRv8C4Xn-JtD5WiymC9AOuPaAxajE");
    $general_json       = json_decode($general_video_info, TRUE);

    $return                 = array();
    $return['name']         = $general_json['items'][0]['snippet']['title'];
    $return['thumbnailUrl'] = $general_json['items'][0]['snippet']['thumbnails']['medium']['url'];

    return $return;
}

function sendPushNotifications($payload, $registration_ids, $fcmAccessToken){

    //chunk registration ids to 1000 and send
    $registration_id_sets = array_chunk($registration_ids, 1000);

    foreach($registration_id_sets as $registration_id_set){

        $url = 'https://fcm.googleapis.com/v1/projects/contentcreators-internal/messages:send';

        $payload['token'] = $registration_id_set[0];
        $fields           = array('message' => $payload);

        $fields = json_encode($fields);

        $headers = array(
            'Authorization: Bearer ' . $fcmAccessToken,
            'Content-Type: application/json'
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);

        $result = curl_exec($ch);
        if(curl_errno($ch)){
            //echo $error_msg = curl_error($ch);die;
        }

        $respJson = json_decode($result);
        curl_close($ch);
    }
}

function sendPushNotificationTopic($payload, $topic, $fcmAccessToken){

    $url = 'https://fcm.googleapis.com/v1/projects/project-name/messages:send';

    $payload['topic'] = $topic;
    $fields           = array('message' => $payload);
    $fields           = json_encode($fields);

    $headers = array(
        'Authorization: Bearer ' . $fcmAccessToken,
        'Content-Type: application/json'
    );

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);

    $result = curl_exec($ch);
    if(curl_errno($ch)){
        //echo $error_msg = curl_error($ch);
    }

    $respJson = json_decode($result);
    curl_close($ch);
}

function tokenToTopic($is_enabled, $topic, $token){

    //add registration token to topic
    if($is_enabled)
        $url = "https://iid.googleapis.com/iid/v1:batchAdd";
    else
        $url = "https://iid.googleapis.com/iid/v1:batchRemove";

    $headers = [
        'Content-Type: application/json',
        'Authorization: key=' . fS('app.push_server_key'),
    ];

    $apiData = json_encode([
        "to"                  => "/topics/" . $topic,
        "registration_tokens" => [$token]
    ]);

    $topicResp = call_curl($url, "POST", $apiData, $headers);

    $hasError = false;
    if(isset($topicResp['results'][0]['error'])){
        $hasError = true;
    }
}

function getFcmAccessToken(){
    $client = new Google_Client();
    try{
        $client->setAuthConfigFile(base_path("firebase-auth-adminsdk.json"));
        $client->addScope(Google_Service_FirebaseCloudMessaging::CLOUD_PLATFORM);

        // retrieve the saved oauth token if it exists, you can save it on your database or in a secure place on your server
        $savedTokenJson = readFCMAccessTokenFile();

        if($savedTokenJson != null){
            // the token exists, set it to the client and check if it's still valid
            $client->setAccessToken($savedTokenJson);
            if($client->isAccessTokenExpired()){
                // the token is expired, generate a new token and set it to the client
                $accessToken = generateFCMAccessToken($client);
                $client->setAccessToken($accessToken);
            }
            else{
                $accessToken = $client->getAccessToken();
            }
        }
        else{
            // the token doesn't exist, generate a new token and set it to the client
            $accessToken = generateFCMAccessToken($client);
            $client->setAccessToken($accessToken);
        }

        // the client is configured, now you can send the push notification using the $oauthToken.
        $oauthToken = $accessToken["access_token"];
        return $oauthToken;
    }
    catch(Google_Exception $e){
        // handle exception
    }

    return "";
}

function generateFCMAccessToken($client){

    $client->fetchAccessTokenWithAssertion();
    $accessToken = $client->getAccessToken();

    // save the oauth token json on your database or in a secure place on your server
    $tokenJson = json_encode($accessToken);
    saveFCMAccessTokenFile($tokenJson);

    return $accessToken;
}

function readFCMAccessTokenFile(){

    $accessTokenJson = null;
    $filePath        = base_path("firebase-access-token.json");

    $myfile = @fopen($filePath, "r");
    if($myfile){

        $accessTokenJson = json_decode(fread($myfile, filesize($filePath)), true);
        fclose($myfile);
    }

    return $accessTokenJson;
}

function saveFCMAccessTokenFile($tokenJson){

    $filePath = base_path("firebase-access-token.json");
    $myfile   = @fopen($filePath, "w");

    if($myfile){

        fwrite($myfile, $tokenJson);
        fclose($myfile);
    }
}

function get_youtube_id($url){
    // Here is a sample of the URLs this regex matches: (there can be more content after the given URL that will be ignored)

    // http://youtu.be/dQw4w9WgXcQ
    // http://www.youtube.com/embed/dQw4w9WgXcQ
    // http://www.youtube.com/watch?v=dQw4w9WgXcQ
    // http://www.youtube.com/?v=dQw4w9WgXcQ
    // http://www.youtube.com/v/dQw4w9WgXcQ
    // http://www.youtube.com/e/dQw4w9WgXcQ
    // http://www.youtube.com/user/username#p/u/11/dQw4w9WgXcQ
    // http://www.youtube.com/sandalsResorts#p/c/54B8C800269D7C1B/0/dQw4w9WgXcQ
    // http://www.youtube.com/watch?feature=player_embedded&v=dQw4w9WgXcQ
    // http://www.youtube.com/?feature=player_embedded&v=dQw4w9WgXcQ

    // It also works on the youtube-nocookie.com URL with the same above options.
    // It will also pull the ID from the URL in an embed code (both iframe and object tags)

    preg_match('%(?:youtube(?:-nocookie)?\.com/(?:[^/]+/.+/|(?:v|e(?:mbed)?)/|.*[?&]v=)|youtu\.be/)([^"&?/ ]{11})%i', $url, $match);
    $youtube_id = @$match[1];

    return $youtube_id;
}

function reverseSanitizeString($text){

    return html_entity_decode($text, ENT_QUOTES);
}

function convertExcelDate($dateValue, $format = 'Y-m-d H:i:s'){

    $unixDate = ceil(($dateValue - 25569) * 86400);
    return gmdate($format, $unixDate);
}

function pagination_links($pagination){

    $currentPage = $pagination->currentPage();
    $totalPages  = $pagination->lastPage();
    $link        = $pagination->path();

    $adjacents = 2;

    $pagination_links  = '<ul class="cm-pagination">';
    $max_links_to_show = (($adjacents * 2) + 1);

    if($totalPages <= $max_links_to_show){

        for($counter = 1; $counter <= $totalPages; $counter++){

            if($counter == $currentPage){
                $pagination_links .= "<li class='active'><a role='button'>" . $counter . "</a></li>";
            }
            else{
                $pagination_links .= "<li><a role='button' href='" . $link . "?page=" . $counter . "'>" . $counter . "</a></li>";
            }
        }
    }
    elseif($totalPages > $max_links_to_show){

        if($currentPage <= 4){

            for($counter = 1; $counter <= ($currentPage + $adjacents); $counter++){

                if($counter == $currentPage){
                    $pagination_links .= "<li class='active'><a role='button' href='" . $link . "?page=" . $counter . "'>" . $counter . "</a></li>";
                }
                else{
                    $pagination_links .= "<li><a role='button' href='" . $link . "?page=" . $counter . "'>" . $counter . "</a></li>";
                }
            }

            $pagination_links .= "<li>...</li>";
            $pagination_links .= "<li><a role='button' href='" . $link . "?page=" . $totalPages . "'>" . $totalPages . "</a></li>";
        }
        elseif($currentPage > 2 && $currentPage < $totalPages - ($adjacents + 1)){

            $pagination_links .= "<li><a role='button' href='?=1'>1</a></li>";
            $pagination_links .= "<li>...</li>";

            for($counter = $currentPage - $adjacents; $counter <= $currentPage + $adjacents; $counter++){
                if($counter == $currentPage){
                    $pagination_links .= "<li class='active'><a role='button'>" . $counter . "</a></li>";
                }
                else{
                    $pagination_links .= "<li><a role='button' href='" . $link . "?page=" . $counter . "'>" . $counter . "</a></li>";
                }
            }

            $pagination_links .= "<li>...</li>";
            $pagination_links .= "<li><a role='button' href='" . $link . "?page=" . $totalPages . "'>" . $totalPages . "</a></li>";
        }
        else{

            $pagination_links .= "<li><a role='button' href='" . $link . "?page=1'>1</a></li>";
            $pagination_links .= "<li>...</li>";

            for($counter = $totalPages - 5; $counter <= $totalPages; $counter++){

                if($counter == $currentPage){
                    $pagination_links .= "<li class='active'><a role='button'>" . $counter . "</a></li>";
                }
                else{
                    $pagination_links .= "<li><a role='button' href='" . $link . "?page=" . $counter . "'>" . $counter . "</a></li>";
                }
            }
        }
    }

    $pagination_links .= "</ul>";

    return $pagination_links;
}

function getCountryList($countryCode = false){

    $countryList = array(
        "DE" => "Germany",
        "AT" => "Austria",
        // "" => "------------------------------",
        "AF" => "Afghanistan",
        "AL" => "Albania",
        "DZ" => "Algeria",
        "AS" => "American Samoa",
        "AD" => "Andorra",
        "AO" => "Angola",
        "AI" => "Anguilla",
        "AQ" => "Antarctica",
        "AG" => "Antigua and Barbuda",
        "AR" => "Argentina",
        "AM" => "Armenia",
        "AW" => "Aruba",
        "AU" => "Australia",
        "AZ" => "Azerbaijan",
        "BS" => "Bahamas",
        "BH" => "Bahrain",
        "BD" => "Bangladesh",
        "BB" => "Barbados",
        "BY" => "Belarus",
        "BE" => "Belgium",
        "BZ" => "Belize",
        "BJ" => "Benin",
        "BM" => "Bermuda",
        "BT" => "Bhutan",
        "BO" => "Bolivia",
        "BA" => "Bosnia and Herzegovina",
        "BW" => "Botswana",
        "BV" => "Bouvet Island",
        "BR" => "Brazil",
        "BQ" => "British Antarctic Territory",
        "IO" => "British Indian Ocean Territory",
        "VG" => "British Virgin Islands",
        "BN" => "Brunei",
        "BG" => "Bulgaria",
        "BF" => "Burkina Faso",
        "BI" => "Burundi",
        "KH" => "Cambodia",
        "CM" => "Cameroon",
        "CA" => "Canada",
        "CT" => "Canton and Enderbury Islands",
        "CV" => "Cape Verde",
        "KY" => "Cayman Islands",
        "CF" => "Central African Republic",
        "TD" => "Chad",
        "CL" => "Chile",
        "CN" => "China",
        "CX" => "Christmas Island",
        "CC" => "Cocos [Keeling] Islands",
        "CO" => "Colombia",
        "KM" => "Comoros",
        "CG" => "Congo - Brazzaville",
        "CD" => "Congo - Kinshasa",
        "CK" => "Cook Islands",
        "CR" => "Costa Rica",
        "HR" => "Croatia",
        "CU" => "Cuba",
        "CY" => "Cyprus",
        "CZ" => "Czech Republic",
        "CI" => "Côte d'Ivoire",
        "DK" => "Denmark",
        "DJ" => "Djibouti",
        "DM" => "Dominica",
        "DO" => "Dominican Republic",
        "NQ" => "Dronning Maud Land",
        "DD" => "East Germany",
        "EC" => "Ecuador",
        "EG" => "Egypt",
        "SV" => "El Salvador",
        "GQ" => "Equatorial Guinea",
        "ER" => "Eritrea",
        "EE" => "Estonia",
        "ET" => "Ethiopia",
        "FK" => "Falkland Islands",
        "FO" => "Faroe Islands",
        "FJ" => "Fiji",
        "FI" => "Finland",
        "FR" => "France",
        "GF" => "French Guiana",
        "PF" => "French Polynesia",
        "TF" => "French Southern Territories",
        "FQ" => "French Southern and Antarctic Territories",
        "GA" => "Gabon",
        "GM" => "Gambia",
        "GE" => "Georgia",
        "GH" => "Ghana",
        "GI" => "Gibraltar",
        "GR" => "Greece",
        "GL" => "Greenland",
        "GD" => "Grenada",
        "GP" => "Guadeloupe",
        "GU" => "Guam",
        "GT" => "Guatemala",
        "GG" => "Guernsey",
        "GN" => "Guinea",
        "GW" => "Guinea-Bissau",
        "GY" => "Guyana",
        "HT" => "Haiti",
        "HM" => "Heard Island and McDonald Islands",
        "HN" => "Honduras",
        "HK" => "Hong Kong SAR China",
        "HU" => "Hungary",
        "IS" => "Iceland",
        "IN" => "India",
        "ID" => "Indonesia",
        "IR" => "Iran",
        "IQ" => "Iraq",
        "IE" => "Ireland",
        "IM" => "Isle of Man",
        "IL" => "Israel",
        "IT" => "Italy",
        "JM" => "Jamaica",
        "JP" => "Japan",
        "JE" => "Jersey",
        "JT" => "Johnston Island",
        "JO" => "Jordan",
        "KZ" => "Kazakhstan",
        "KE" => "Kenya",
        "KI" => "Kiribati",
        "KW" => "Kuwait",
        "KG" => "Kyrgyzstan",
        "LA" => "Laos",
        "LV" => "Latvia",
        "LB" => "Lebanon",
        "LS" => "Lesotho",
        "LR" => "Liberia",
        "LY" => "Libya",
        "LI" => "Liechtenstein",
        "LT" => "Lithuania",
        "LU" => "Luxembourg",
        "MO" => "Macau SAR China",
        "MK" => "Macedonia",
        "MG" => "Madagascar",
        "MW" => "Malawi",
        "MY" => "Malaysia",
        "MV" => "Maldives",
        "ML" => "Mali",
        "MT" => "Malta",
        "MH" => "Marshall Islands",
        "MQ" => "Martinique",
        "MR" => "Mauritania",
        "MU" => "Mauritius",
        "YT" => "Mayotte",
        "FX" => "Metropolitan France",
        "MX" => "Mexico",
        "FM" => "Micronesia",
        "MI" => "Midway Islands",
        "MD" => "Moldova",
        "MC" => "Monaco",
        "MN" => "Mongolia",
        "ME" => "Montenegro",
        "MS" => "Montserrat",
        "MA" => "Morocco",
        "MZ" => "Mozambique",
        "MM" => "Myanmar [Burma]",
        "NA" => "Namibia",
        "NR" => "Nauru",
        "NP" => "Nepal",
        "NL" => "Netherlands",
        "AN" => "Netherlands Antilles",
        "NT" => "Neutral Zone",
        "NC" => "New Caledonia",
        "NZ" => "New Zealand",
        "NI" => "Nicaragua",
        "NE" => "Niger",
        "NG" => "Nigeria",
        "NU" => "Niue",
        "NF" => "Norfolk Island",
        "KP" => "North Korea",
        "VD" => "North Vietnam",
        "MP" => "Northern Mariana Islands",
        "NO" => "Norway",
        "OM" => "Oman",
        "PC" => "Pacific Islands Trust Territory",
        "PK" => "Pakistan",
        "PW" => "Palau",
        "PS" => "Palestinian Territories",
        "PA" => "Panama",
        "PZ" => "Panama Canal Zone",
        "PG" => "Papua New Guinea",
        "PY" => "Paraguay",
        "YD" => "People's Democratic Republic of Yemen",
        "PE" => "Peru",
        "PH" => "Philippines",
        "PN" => "Pitcairn Islands",
        "PL" => "Poland",
        "PT" => "Portugal",
        "PR" => "Puerto Rico",
        "QA" => "Qatar",
        "RO" => "Romania",
        "RU" => "Russia",
        "RW" => "Rwanda",
        "RE" => "Réunion",
        "BL" => "Saint Barthélemy",
        "SH" => "Saint Helena",
        "KN" => "Saint Kitts and Nevis",
        "LC" => "Saint Lucia",
        "MF" => "Saint Martin",
        "PM" => "Saint Pierre and Miquelon",
        "VC" => "Saint Vincent and the Grenadines",
        "WS" => "Samoa",
        "SM" => "San Marino",
        "SA" => "Saudi Arabia",
        "SN" => "Senegal",
        "RS" => "Serbia",
        "CS" => "Serbia and Montenegro",
        "SC" => "Seychelles",
        "SL" => "Sierra Leone",
        "SG" => "Singapore",
        "SK" => "Slovakia",
        "SI" => "Slovenia",
        "SB" => "Solomon Islands",
        "SO" => "Somalia",
        "ZA" => "South Africa",
        "GS" => "South Georgia and the South Sandwich Islands",
        "KR" => "South Korea",
        "ES" => "Spain",
        "LK" => "Sri Lanka",
        "SD" => "Sudan",
        "SR" => "Suriname",
        "SJ" => "Svalbard and Jan Mayen",
        "SZ" => "Swaziland",
        "SE" => "Sweden",
        "CH" => "Switzerland",
        "SY" => "Syria",
        "ST" => "São Tomé and Príncipe",
        "TW" => "Taiwan",
        "TJ" => "Tajikistan",
        "TZ" => "Tanzania",
        "TH" => "Thailand",
        "TL" => "Timor-Leste",
        "TG" => "Togo",
        "TK" => "Tokelau",
        "TO" => "Tonga",
        "TT" => "Trinidad and Tobago",
        "TN" => "Tunisia",
        "TR" => "Turkey",
        "TM" => "Turkmenistan",
        "TC" => "Turks and Caicos Islands",
        "TV" => "Tuvalu",
        "UM" => "U.S. Minor Outlying Islands",
        "PU" => "U.S. Miscellaneous Pacific Islands",
        "VI" => "U.S. Virgin Islands",
        "UG" => "Uganda",
        "UA" => "Ukraine",
        "SU" => "Union of Soviet Socialist Republics",
        "AE" => "United Arab Emirates",
        "GB" => "United Kingdom",
        "US" => "United States",
        "ZZ" => "Unknown or Invalid Region",
        "UY" => "Uruguay",
        "UZ" => "Uzbekistan",
        "VU" => "Vanuatu",
        "VA" => "Vatican City",
        "VE" => "Venezuela",
        "VN" => "Vietnam",
        "WK" => "Wake Island",
        "WF" => "Wallis and Futuna",
        "EH" => "Western Sahara",
        "YE" => "Yemen",
        "ZM" => "Zambia",
        "ZW" => "Zimbabwe",
        "AX" => "Åland Islands",
    );

    if($countryCode !== false){
        return $countryList[$countryCode];
    }
    else{
        return $countryList;
    }
}

function quote_status_options(){

    $options = [
        "Offen"                  => "Offen",
        "Beauftragt"             => "Beauftragt",
        "Zu verrechnen"          => "Zu verrechnen",
        "Abgerechnet"            => "Abgerechnet",
        "Monatliche Verrechnung" => "Monatliche Verrechnung",
        "Abgelehnt"              => "Abgelehnt",
    ];

    return $options;
}

function laraberg_text_fields(){

    return ['z_sk_quote_de.c_quote.a_quote_description', 'z_sk_quote_en.c_quote.a_quote_description', 'z_at_quote_de.c_quote.a_quote_description', 'z_at_quote_en.c_quote.a_quote_description'];
}

function template_options($template_type, $comapny){

    $options = [
        $comapny . "_" . $template_type . "_de" => strtoupper($comapny) . " German " . ucfirst($template_type) . " Template",
        $comapny . "_" . $template_type . "_en" => strtoupper($comapny) . " English " . ucfirst($template_type) . " Template",
    ];

    return $options;
}

function deFormatToStandard($date){

    $dateParts = explode(".", $date);

    if(count($dateParts) == 3)
        return $dateParts[2] . "-" . $dateParts[1] . "-" . $dateParts[0];
    else
        return $date;
}

function generate_pdf($type, $html_url, $header_url, $footer_url){

    // Init path and file names
    $template_output_folder_path = base_path("wkhtmltopdf" . DIRECTORY_SEPARATOR . "output") . DIRECTORY_SEPARATOR;
    $template_binary_folder_path = base_path("wkhtmltopdf" . DIRECTORY_SEPARATOR . "binary") . DIRECTORY_SEPARATOR;
    $output_pdf_path             = $template_output_folder_path . "pdf" . "_" . time() . ".pdf";

    // Set binary path
    $wkhtmltopdf_bin_path = strtoupper(substr(PHP_OS, 0, 3)) === 'WIN' ? ($template_binary_folder_path . "wkhtmltopdf.exe") : "wkhtmltopdf";

    $top_mm = $type == "sk" ? 36 : 29;   //SK top 36mm, AT top 29mm

    // Generate the pdf file from the html
    $command_str = $wkhtmltopdf_bin_path . " --page-size A4 -L 6mm -R 6mm -T " . $top_mm . "mm -B 36mm --header-html " . $header_url . " --footer-html " . $footer_url . " " . escapeshellarg($html_url) . " " . escapeshellarg($output_pdf_path);

    $command_str = escapeshellcmd($command_str);
    $output      = "";
    $resultCode  = -1;
    $return_str  = exec($command_str, $output, $resultCode);

    //delete html file
    @unlink(public_path("uploads/tmp") . DIRECTORY_SEPARATOR . basename($html_url));

    // Ret
    return $output_pdf_path;
}

function department_options(){

    return [
        "a" => "project_management",
        "b" => "editorial",
        "c" => "programming",
        "d" => "design",
        "e" => "graphic",
        "f" => "video",
    ];
}

function replaceDepartmentPrices($str, $quote_prices){

    foreach(department_options() as $dKey => $department){

        if($quote_prices){
            $str = str_replace("[" . $department . "_price]", $quote_prices[$dKey], $str);
        }
        else{
            $str = str_replace("[" . $department . "_price]", fT('y_settings.a_departmets_costs.' . $dKey . '_' . $department), $str);
        }
    }

    return $str;
}

function quote_texts($type, $from_page, $quote_prices = false, $quote = false){

    $de_options = [
        'currency'                     => fT('z_' . $type . '_quote_de.c_quote.c_currency', 'EUR'),
        'measure_total_cost_label'     => fT('z_' . $type . '_quote_de.c_quote.b_measure_total_cost_label', 'Zwischensumme Netto'),
        'total_cost_label'             => fT('z_' . $type . '_quote_de.c_quote.b_total_cost_label', 'Gesamtkosten Netto'),
        'cover_letter'                 => fT('z_' . $type . '_quote_de.b_cover_letter.e_quote_subject', '<p><strong>DE : Angebot Cover letter subject</strong></p>
<p>Angebot conver letter default description text</p>'),
        'quote_description'            => replaceDepartmentPrices(fT('z_' . $type . '_quote_de.c_quote.a_quote_description', ''), $quote_prices),
        'quote_template_liability'     => fT('z_' . $type . '_quote_de.b_template.a_liability', ''),
        'quote_template_accounting'    => fT('z_' . $type . '_quote_de.b_template.b_accounting', ''),
        'quote_template_cost_overview' => fT('z_' . $type . '_quote_de.b_template.c_cost_overview', ''),
    ];

    $en_options = [
        'currency'                     => fT('z_' . $type . '_quote_en.c_quote.c_currency', 'EUR'),
        'measure_total_cost_label'     => fT('z_' . $type . '_quote_en.c_quote.b_measure_total_cost_label', 'Zwischensumme Netto'),
        'total_cost_label'             => fT('z_' . $type . '_quote_en.c_quote.b_total_cost_label', 'Gesamtkosten Netto'),
        'cover_letter'                 => fT('z_' . $type . '_quote_en.b_cover_letter.e_quote_subject', '<p><strong>EN : Quote Cover letter subject</strong></p>
<p>Quote conver letter default description text</p>'),
        'quote_description'            => replaceDepartmentPrices(fT('z_' . $type . '_quote_en.c_quote.a_quote_description', ''), $quote_prices),
        'quote_template_liability'     => fT('z_' . $type . '_quote_en.b_template.a_liability', ''),
        'quote_template_accounting'    => fT('z_' . $type . '_quote_en.b_template.b_accounting', ''),
        'quote_template_cost_overview' => fT('z_' . $type . '_quote_en.b_template.c_cost_overview', ''),
    ];

    if($quote){

        $template_liability                     = find_between('<!-- wp:ccblocks/liability-block --><div class="wp-block-ccblocks-liability-block">', '</div><!-- /wp:ccblocks/liability-block -->', $quote->quote_description);
        $de_options['quote_template_liability'] = $template_liability;
        $en_options['quote_template_liability'] = $template_liability;

        $template_accounting                     = find_between('<!-- wp:ccblocks/accounting-block --><div class="wp-block-ccblocks-accounting-block">', '</div><!-- /wp:ccblocks/accounting-block -->', $quote->quote_description);
        $de_options['quote_template_accounting'] = $template_accounting;
        $en_options['quote_template_accounting'] = $template_accounting;

        $template_cost_overview                     = find_between('<!-- wp:ccblocks/cost-overview-block --><div class="wp-block-ccblocks-cost-overview-block">', '</div><!-- /wp:ccblocks/cost-overview-block -->', $quote->quote_description);
        $de_options['quote_template_cost_overview'] = $template_cost_overview;
        $en_options['quote_template_cost_overview'] = $template_cost_overview;
    }

    if($from_page == "quote"){

        $de_options['quote_template_cost_overview'] = replaceDepartmentPrices($de_options['quote_template_cost_overview'], $quote_prices);
        $en_options['quote_template_cost_overview'] = replaceDepartmentPrices($en_options['quote_template_cost_overview'], $quote_prices);
    }

    $options = [
        'de' => $de_options,
        'en' => $en_options,
    ];

    return $options;
}

function quote_department_options(){

    $de_options = [
        "z" => fT('y_settings.b_departmets_de_names.z_blank_option', 'Select Department'),
    ];

    $en_options = [
        "z" => fT('y_settings.c_departmets_en_names.z_blank_option', 'Select Department'),
    ];

    foreach(department_options() as $dKey => $dTextKey){

        $de_options[$dKey] = fT('y_settings.b_departmets_de_names.' . $dKey . '_' . $dTextKey, $dTextKey);
        $en_options[$dKey] = fT('y_settings.c_departmets_en_names.' . $dKey . '_' . $dTextKey, $dTextKey);
    }

    $options = [
        'de' => $de_options,
        'en' => $en_options,
    ];

    return $options;
}

function quote_department_costs($department_costs = false){

    $new_department_costs = [
        "z" => 0,
    ];

    foreach(department_options() as $dKey => $dTextKey){
        $new_department_costs[$dKey] = fT('y_settings.a_departmets_costs.' . $dKey . '_' . $dTextKey, 750);
    }

    if(!$department_costs){
        $department_costs = $new_department_costs;
    }
    else{
        $department_costs = array_merge($new_department_costs, $department_costs);
    }

    return $department_costs;
}

function quoteDepartmentWise($string){

    preg_match_all('/<!-- wp:ccblocks\/cost-calculation-block (.*?) --><div class="cc-table-a-wrapper">(.*?)<\/div>/', $string, $matches);

    $newArr = [];
    if($matches && !empty($matches[1])){

        foreach($matches[1] as $k => $m){

            $json                   = json_decode($m);
            $newArr[$json->blockId] = array("costing" => isset($json->costing) ? json_decode($json->costing) : []);
        }
    }

    return $newArr;
}

function replacePageBreak($str){

    $str = str_replace("<!--nextpage-->", '<hr class="page-break">', $str);
    return $str;
}

function find_between($start, $end, $string, $excludeSearchString = true){

    $startPos = strpos($string, $start);

    if($startPos !== false){

        $searchPos = $startPos + strlen($start);
        $endPos    = strpos($string, $end, $searchPos);

        if($endPos !== false){

            if($excludeSearchString){
                return substr($string, $searchPos, ($endPos - $searchPos));
            }
            else{
                return substr($string, $startPos, ($endPos - $startPos + strlen($end)));
            }
        }
    }

    return "";
}

function replace_between($start, $end, $string){

    $startPos = strpos($string, $start);
    $endPos   = strpos($string, $end, $startPos) + strlen($end);
    $aLink    = substr($string, $startPos, ($endPos - $startPos));

    $string = str_replace($aLink, '', $string);
    return $string;
}

function getTaxInvoiceTotal($invoice_amount, $tax_percent, $return_tax = false){

    $tax_amount = round(($invoice_amount * $tax_percent) / 100, 2);

    if($return_tax){
        return $tax_amount;
    }
    else{

        $invoice_total = $invoice_amount + $tax_amount;
        return $invoice_total;
    }
}

function task_priority_options($priorityVal = false){

    $options = [
        1 => "Low",
        2 => "Medium",
        3 => "High",
    ];

    if($priorityVal !== false){

        return isset($options[$priorityVal]) ? $options[$priorityVal] : '-';
    }
    else{
        return $options;
    }
}

function task_status_options(){

    $options = [
        "Not Started"       => "Not Started",
        "Working on it"     => "Working on it",
        "Fix Not Confirmed" => "Fix Not Confirmed",
        //"Stuck"             => "Stuck",
        "Done"              => "Done",
    ];

    return $options;
}

function space_member_options(){

    $options = [
        "ROLE_MEMBER"  => "Role Member",
        "ROLE_MANAGER" => "Role Manager",
    ];

    return $options;
}

function refreshGoogleAccessToken($user = false, $force_generate = false){

    if(!$user){
        $user = getUserDetail(session()->get('gl_user_id'));
    }

    //creating google instance
    $cli    = new GoogleClient;
    $client = $cli->client();

    if($user->google_access_token)
        $client->setAccessToken($user->google_access_token);

    if($user->google_access_token == "" || $client->isAccessTokenExpired() || $force_generate){

        // Refresh the token if possible, else fetch a new one.
        if($user->google_refresh_token){

            try{
                $client->fetchAccessTokenWithRefreshToken($user->google_refresh_token);

                $user->google_access_token = $client->getAccessToken();
                $user->save();
            }
            catch(ClientException $e){

                Log::add("refresh-access-token-issue", $e->getMessage(), $user->id, $user->name);
                return false;
            }
        }
        else{
            Log::add("refresh-access-token-issue", "No refresh token stored", $user->id, $user->name);
            return false;
        }
    }

    return $client;
}

function getGdriveFolder($client, $folderId){

    //create folder in google
    $service = new \Google_Service_Drive($client);

    $optParams   = array(
        'fields'             => 'id,name,parents',
        'supportsTeamDrives' => true,
    );
    $driveFolder = $service->files->get($folderId, $optParams);

    return $driveFolder;
}

function createGdriveFolder($client, $new_folder_name, $target_folder_id){

    try{
        //create folder in google
        $driveService = new \Google_Service_Drive($client);

        //check if folder with same name exists
        $params          = array(
            'q'                         => "'" . $target_folder_id . "' in parents and mimeType='application/vnd.google-apps.folder' and name='" . $new_folder_name . "' and trashed = false",
            'supportsAllDrives'         => true,
            'corpora'                   => 'allDrives',
            'includeItemsFromAllDrives' => true,
            'fields'                    => 'files(id, name, trashed)',
        );
        $responseFolders = $driveService->files->listFiles($params);

        if(empty($responseFolders->files)){

            $file = new \Google_Service_Drive_DriveFile();
            $file->setParents([$target_folder_id]);
            $file->setName(reverseSanitizeString($new_folder_name));
            $file->setMimeType('application/vnd.google-apps.folder');
            unset($file->exportLinks);

            $optParams = array(
                'fields'             => 'id, name, trashed',
                'supportsTeamDrives' => true,
            );
            $folder    = $driveService->files->create($file, $optParams);
        }
        else{
            $folder = $responseFolders->files[0];
        }
    }
    catch(Exception $e){
        $errorMsg = $e->getMessage();
        $folder   = false;
    }

    return $folder;
}

function updateGdriveFolderName($client, $new_folder_name, $folder_id){

    //create folder in google
    $api = new \Google_Service_Drive($client);

    $file = new \Google_Service_Drive_DriveFile();
    $file->setName(reverseSanitizeString($new_folder_name));

    $optParams     = array(
        'fields'             => 'id',
        'supportsTeamDrives' => true,
    );
    $updatedFolder = $api->files->update($folder_id, $file, $optParams);

    return $updatedFolder;
}

function getTasksTree($projectGroupTasks, $sort_col, $parent_task_id = 0){

    $final_tasks  = [];
    $no_due_tasks = [];

    if($parent_task_id > 0){

        foreach($projectGroupTasks as $projectTask){

            if($projectTask['parent_task_id'] == $parent_task_id){

                $projectTask['sub_tasks'] = getTasksTree($projectGroupTasks, $sort_col, $projectTask['id']);

                if($projectTask['due_date'] || $sort_col != "due_date"){
                    $final_tasks[] = $projectTask;
                }
                else{
                    $no_due_tasks[] = $projectTask;
                }
            }
        }

        foreach($no_due_tasks as $dueTask){

            $final_tasks[] = $dueTask;
        }
    }
    else{

        foreach($projectGroupTasks as $status => $projectTasks){

            $task_ids = get_task_ids($projectTasks);

            foreach($projectTasks as $projectTask){

                if(!in_array($projectTask['parent_task_id'], $task_ids) || $projectTask['parent_task_id'] == $parent_task_id){

                    $projectTask['sub_tasks'] = getTasksTree($projectTasks, $sort_col, $projectTask['id']);

                    if($projectTask['due_date'] || $sort_col != "due_date"){
                        $final_tasks[$status][] = $projectTask;
                    }
                    else{
                        $no_due_tasks[$status][] = $projectTask;
                    }
                }
            }
        }

        foreach($no_due_tasks as $status => $dueTasks){

            foreach($dueTasks as $dueTask){
                $final_tasks[$status][] = $dueTask;
            }
        }
    }

    return $final_tasks;
}

function get_task_ids($projectTasks){

    $taskIds = [];

    foreach($projectTasks as $task){
        $taskIds[] = $task['id'];
    }

    return $taskIds;
}

/*
function getTasksTree($projectTasks, $sort_col, $parent_task_id=0)  {

    $final_tasks = [];
    $no_due_tasks = [];

    foreach($projectTasks as $projectTask)  {

        if($projectTask['parent_task_id'] == $parent_task_id)  {

            $projectTask['sub_tasks'] = getTasksTree($projectTasks, $sort_col, $projectTask['id']);

            if($projectTask['due_date'] || $sort_col != "due_date")  {
                $final_tasks[] = $projectTask;
            }
            else  {
                $no_due_tasks[] = $projectTask;
            }
        }
    }

    foreach($no_due_tasks as $dueTask)  {

        $final_tasks[] = $dueTask;
    }

    return $final_tasks;
}
*/

function groupByStatus($tasks){

    $taskGroupArr = [];
    $taskLists = [];

    foreach($tasks as $task){

        $status = $task['status'];
        $google_space_name = $task['google_space_name'];
        $filter_id = $task['google_space_id'] ?? $task['google_task_list_id'];

        if(!isset($taskGroupArr[$status])){
            $taskGroupArr[$status] = [];
        }

        if(!isset($taskLists[$filter_id])){
            $taskLists[$filter_id] = $google_space_name;
        }

        $taskGroupArr[$status][] = $task;
    }

    return [$taskGroupArr, $taskLists];
}

function selectedTaskVal($field_val, $match_val, $default_val){

    if(!$field_val){
        $field_val = $default_val;
    }

    return $field_val == $match_val ? "selected" : "";
}

function addTaskUsers($user_ids_str, $new_user_id){

    $user_ids = $user_ids_str ? explode(",", $user_ids_str) : [];

    if(!in_array($new_user_id, $user_ids)){
        $user_ids[] = $new_user_id;
    }

    $user_ids_str = implode(",", $user_ids);
    return $user_ids_str;
}

function taskReadUpdate($task_id){

    $user_id = session()->get('gl_user_id');
    $taskObj = TaskNewUpdate::where('user_id', $user_id)->where('task_id', $task_id)->first();

    if(!$taskObj){

        $taskObj          = new TaskNewUpdate();
        $taskObj->user_id = $user_id;
        $taskObj->task_id = $task_id;

        $old_last_seen = "2024-01-01";
    }
    else{
        $old_last_seen = $taskObj->last_seen;
    }

    $taskObj->last_seen = date('Y-m-d H:i:s', time());
    $taskObj->save();

    return $old_last_seen;
}

function makeLinks($str){

    $reg_exUrl     = "/(http|https|ftp|ftps)\\:\\/\\/[a-zA-Z0-9\\-\\.]+\\.[a-zA-Z]{2,3}(\\/\\S*)?/";
    $urls          = array();
    $urlsToReplace = array();

    if(preg_match_all($reg_exUrl, $str, $urls)){

        $numOfMatches       = count($urls[0]);
        $numOfUrlsToReplace = 0;

        for($i = 0; $i < $numOfMatches; $i++){

            $alreadyAdded       = false;
            $numOfUrlsToReplace = count($urlsToReplace);
            for($j = 0; $j < $numOfUrlsToReplace; $j++){
                if($urlsToReplace[$j] == $urls[0][$i]){
                    $alreadyAdded = true;
                }
            }
            if(!$alreadyAdded){
                array_push($urlsToReplace, $urls[0][$i]);
            }
        }

        $numOfUrlsToReplace = count($urlsToReplace);
        for($i = 0; $i < $numOfUrlsToReplace; $i++){

            $replace_str = '<a href="' . $urlsToReplace[$i] . '" target="_blank" >' . $urlsToReplace[$i] . '</a>';
            $str         = str_replace($urlsToReplace[$i], $replace_str, $str);
        }
        return $str;
    }
    else{
        return $str;
    }
}

function cc_time_ago($datetime){

    $time_difference = time() - strtotime($datetime);

    if($time_difference < 1){
        return 'less than 1 second ago';
    }

    $condition = array(
        24 * 60 * 60 => 'day',
        60 * 60      => 'hour',
        60           => 'minute',
        1            => 'second'
    );

    foreach($condition as $secs => $str){

        $d = $time_difference / $secs;

        if($d >= 1){

            $t = round($d);

            if($str == "day" && $t > 10){
                return '<span>' . date('d.m.Y H:i', strtotime($datetime)) . '</span>';
            }
            else{
                return '<span title="' . date('d.m.Y H:i', strtotime($datetime)) . '">' . $t . ' ' . $str . ($t > 1 ? 's' : '') . ' ago</span>';
            }
        }
    }
}

function deleteTask($task){

    //delete task comments
    TaskComment::where('task_id', $task->id)->forceDelete();
    TaskNewUpdate::where('task_id', $task->id)->delete();

    //mark parent task = 0 for all child tasks
    Task::where('parent_task_id', $task->id)->update([
        'parent_task_id' => $task->parent_task_id
    ]);

    $task->forceDelete();
}

function isSocialTypeLink($url){
    // Define patterns for Instagram, Twitter, and TikTok
    $patterns = [
        'instagram' => 'instagram.com',
        'twitter'   => 'twitter.com',
        'tiktok'    => 'tiktok.com'
    ];

    // Check the URL against each pattern
    foreach($patterns as $platform => $platform_link){

        $pattern = strpos($url, $platform_link);
        if($pattern > 0){
            return $platform;
        }
    }

    return false;
}

function readGmailInbox(){

    try{

        $google_user = GoogleUser::selectRaw("*" . AESdecypts(GoogleUser::encryptableFields()))
                                 ->whereRaw(AESdecypt('email', false) . " = ?", array('<EMAIL>'))
                                 ->first();

        if($google_user){

            $client = refreshGoogleAccessToken($google_user);

            // Create a Google Gmail service.
            $gmailService = new \Google_Service_Gmail($client);

            // Specify the date range
            $gmail_sync_last_id = fS("content_freigaben.gmail_sync_last_id");

            //read emails from inboc
            $messageListResp = $gmailService->users_messages->listUsersMessages('me');

            $i                = 0;
            $post_added_count = 0;

            foreach($messageListResp as $messageResp){

                if($messageResp->getId() == $gmail_sync_last_id){
                    break;
                }
                else{
                    $message = $gmailService->users_messages->get('me', $messageResp->getId());

                    $mail_subject    = "";
                    $mail_from_name  = "";
                    $mail_from_email = "";
                    $mail_body       = $message->getSnippet();

                    $mail_headers = $message->getPayload()->getHeaders();

                    foreach($mail_headers as $mail_header){

                        if($mail_header->getName() == "Subject"){

                            $mail_subject = $mail_header->getValue();
                        }
                        else if($mail_header->getName() == "From"){

                            $from_parts = explode("<", $mail_header->getValue());

                            $mail_from_name  = trim($from_parts[0]);
                            $mail_from_email = rtrim(@$from_parts[1], ">");
                        }
                        else if($mail_header->getName() == "Date"){
                            $mail_date = $mail_header->getValue();
                        }
                    }

                    /*
                    echo '-------------------Start------------------------------';
                    echo "Mail From : ".$mail_from_name." (".$mail_from_email.")<br>";
                    echo "Mail Subject : ".$mail_subject."<br>";
                    echo "Mail Date : ".date('Y-m-d', strtotime($mail_date))."<br>";
                    echo "Mail Small Body : ".$mail_body."<br>";

                    echo '-------------------End------------------------------<br><br><br><br>';
                    */

                    preg_match_all('#\bhttps?://[^,\s()<>]+(?:\([\w\d]+\)|([^,[:punct:]\s]|/))#', $mail_body, $links);

                    preg_match_all('#\bhttps?://[^,\s()<>]+(?:\([\w\d]+\)|([^,[:punct:]\s]|/))#', $mail_subject, $links_subject);

                    $total_links = [];
                    if(isset($links_subject[0])){
                        $total_links = array_unique(array_merge($links[0], $links_subject[0]));
                    }

                    foreach($total_links as $link){

                        //check for social type link or not
                        $social_type = isSocialTypeLink($link);

                        if($social_type){

                            $post_status = 'draft';

                            //Add instagram link to DB
                            $content_freigaben = new ContentFreigaben();

                            //Check user is in our DB or Not
                            $user = GoogleUser::selectRaw("*" . AESdecypts(GoogleUser::encryptableFields()))
                                              ->whereRaw(AESdecypt('email', false) . " = ?", array($mail_from_email))
                                              ->first();
                            if($user)
                                $content_freigaben->admin_id = $user->id;
                            else
                                $content_freigaben->mail_from_email = $mail_from_email;

                            $content_freigaben->content_link = $link;
                            //$content_freigaben->channel_name    = $channel_name;
                            //$content_freigaben->followers       = $followers;
                            $content_freigaben->social_type = $social_type;

                            $channel_post_id = null;
                            if($social_type == 'tiktok'){
                                $id = getTikTokVideoID($link);
                                if($id){
                                    $content_freigaben->tiktok_video_id = $id;
                                    $content_freigaben_exit             = ContentFreigaben::where('tiktok_video_id', $id)->count();
                                    $post_status                        = ($content_freigaben_exit > 0) ? 'declined' : $post_status;
                                }
                            }
                            else if($social_type == 'instagram'){
                                $channel_post_id = getInstagramPostID($link);
                            }
                            else if($social_type == 'twitter'){
                                $channel_post_id = getTwitterTweetID($link);
                            }

                            if($channel_post_id){
                                $content_freigaben_exit = ContentFreigaben::where('content_link', 'like', '%' . $channel_post_id . '%')->count();
                                $post_status            = ($content_freigaben_exit > 0) ? 'declined' : $post_status;
                            }

                            $content_freigaben->status    = $post_status;
                            $content_freigaben->post_date = date('Y-m-d', strtotime($mail_date));
                            $content_freigaben->mail_id   = $messageResp->getId();
                            $content_freigaben->save();

                            if($post_status == 'declined'){

                                $comment             = new Comment();
                                $comment->type       = 'content_freigaben';
                                $comment->type_id    = $content_freigaben->id;
                                $comment->comment    = "Duplicate";
                                $comment->created_by = ($user) ? $user->id : $google_user->id;
                                $comment->save();
                            }

                            $post_added_count++;

                            //move mail to trash
                            if(!dev_test_mode() && $i > 0){
                                $gmailService->users_messages->trash('me', $messageResp->getId());
                            }
                        }
                    }

                    if($i == 0){
                        fS("content_freigaben.gmail_sync_last_id", $messageResp->getId(), true);
                    }

                    $i++;
                }
            }

            session()->flash("success_msg", $post_added_count . " new posts found!");
        }
        else{

            session()->flash("error_msg", "<EMAIL> user not fond!");

            return '<EMAIL> user not fond!';
        }
    }
    catch(Exception $e){

        $errorMsg = $e->getMessage();
        session()->flash("error_msg", $errorMsg);

        return $errorMsg;
    }

    return 'run successfully!';
}

function getInstagramPostID($url){
    $pattern = "/(instagram\.com|ig\.me)\/(p|reel)\/([^\/?]+)/";
    preg_match($pattern, $url, $matches);
    return isset($matches[3]) ? $matches[3] : null;
}

function getTwitterTweetID($url){
    $pattern = "/twitter\.com\/[^\/]+\/status\/([0-9]+)/";
    preg_match($pattern, $url, $matches);
    return isset($matches[1]) ? $matches[1] : null;
}

function getTiktokID($url){

    $pattern = "/(?:m|www|vm)?\.?tiktok\.com\/((?:.*\b(?:(?:v|embed|video)\/|\?shareId=|\&item_id=)(\d+))|\w+)/";
    preg_match($pattern, $url, $matches);

    print_r($matches);
    return isset($matches[2]) ? $matches[2] : null;
}

function deleteGoogleTaskList($googleTaskList, $google_user){

    try{

        $client = refreshGoogleAccessToken($google_user);

        // Create a Google Tasks service.
        $tasksService = new \Google_Service_Tasks($client);

        //Create / Find google task list
        $taskListRespObj = $tasksService->tasklists->delete($googleTaskList->task_list_id);

        return true;
    }
    catch(Exception $e){

        $errorMsg = $e->getMessage();
        return $errorMsg;
    }
}

function updateGoogleTask($client, $task, $googleUser, $oldClient=false, $field_updated = false, $threadDescComment = false){

    try{

        // Create a Google Tasks service.
        $tasksService = new \Google_Service_Tasks($client);

        $old_google_task_list_id = $task->google_task_list_id;

        $googleTaskList = GoogleTaskList::where('google_user_id', $googleUser->id)->where('google_space_id', $task->google_space_id)->first();

        if($googleTaskList){

            $task->google_task_list_id = $googleTaskList->task_list_id;

            $googleTaskList->sync_needed = 1;
            $googleTaskList->save();
        }
        else  {

            //check title in current task list
            $googleTaskListFound = false;
            $params = ["maxResults" => 100];
            $currentTaskList = $tasksService->tasklists->listTasklists($params);

            $taskListTitle = reverseSanitizeString($task->google_space_name);

            foreach($currentTaskList->getItems() as $taskList)  {

                if($taskList->title == $taskListTitle)  {
                    $task->google_task_list_id = $taskList->id;
                    $googleTaskListFound = true;
                }
            }

            if(!$googleTaskListFound)  {

                $taskListObj = new \Google_Service_Tasks_TaskList();
                $taskListObj->setTitle($taskListTitle);

                $taskListRespObj           = $tasksService->tasklists->insert($taskListObj);
                $task->google_task_list_id = $taskListRespObj->getID();
            }

            //insert google task list record
            $googleTaskList                  = new GoogleTaskList();
            $googleTaskList->google_user_id  = $googleUser->id;
            $googleTaskList->google_space_id = $task->google_space_id;
            $googleTaskList->google_space_name = $task->google_space_name;
            $googleTaskList->task_list_id    = $task->google_task_list_id;
            $googleTaskList->last_sync_time  = date('Y-m-d H:i:s', time());
            $googleTaskList->save();
        }

        if($old_google_task_list_id != "" && $old_google_task_list_id != $task->google_task_list_id){

            //delete old google task
            if($task->google_task_id){

                if($oldClient){
                    $oldTasksService = new \Google_Service_Tasks($oldClient);
                    $oldTasksService->tasks->delete($old_google_task_list_id, $task->google_task_id);
                }
                else{
                    $tasksService->tasks->delete($old_google_task_list_id, $task->google_task_id);
                }
            }

            if($task->google_thread_id)  {

                $oldGoogleTaskList = GoogleTaskList::where('task_list_id', $old_google_task_list_id)->first();

                if($oldGoogleTaskList && $googleTaskList->google_space_id != $oldGoogleTaskList->google_space_id){

                    /*
                    //delete old google thread when project is changed
                    $old_project = Project ::withTrashed()->where('id', $oldGoogleTaskList->project_id)->first();

                    if($old_project)  {

                        list($old_project_user, $old_project_client) = projectClient($old_project);

                        deleteGoogleThread($old_project_client, $task->google_thread_id);
                    }
                    */

                    $task->google_thread_id = "";
                }
            }

            $task->google_task_id = "";
        }

        //add task in space for communication
        if($task->google_thread_id){

            if(in_array($field_updated, ["title", "assigned_to", "status"])){

                list($created_by_user, $created_by_client) = googleUserClient($task->created_by);
                $thread_title = getTaskThreadMsg($task);

                updateGoogleThread($created_by_client, $task->google_thread_id, $thread_title);
            }
        }
        else{

            list($created_by_user, $created_by_client) = googleUserClient($task->created_by);
            $thread_title = getTaskThreadMsg($task);

            $spaceMessageResp       = createGoogleThread($created_by_client, $created_by_user, $task->google_space_id, $thread_title);
            $task->google_thread_id = $spaceMessageResp->getName();

            if($threadDescComment)  {

                //translate text so formatting works on google space
                $comment_text = makeGoogleText($task->description);
                addGoogleTaskThreadComment($task, $comment_text, $created_by_client);
            }
        }

        //create new google task
        $taskObj = new \Google_Service_Tasks_Task();
        $taskObj->setTitle(reverseSanitizeString($task->title));
        $taskObj->setNotes(makeGoogleText($task->description));
        $taskObj->setStatus((in_array($task->status, ["Done", "Fix Not Confirmed"]) ? "completed" : "needsAction"));

        if($task->due_date)  {

            $datetime = date("c", strtotime($task->due_date . " 06:00:00"));
            $taskObj->setDue($datetime);
        }
        else  {
            $taskObj->setDue("");
        }

        // Create a Google Task
        if($task->google_task_id){

            $taskObj->setID($task->google_task_id);
            $taskRespObj = $tasksService->tasks->update($task->google_task_list_id, $task->google_task_id, $taskObj);
        }
        else{

            $taskRespObj          = $tasksService->tasks->insert($task->google_task_list_id, $taskObj);
            $task->google_task_id = $taskRespObj->getID();
        }

        $task->save();

        return ["id" => $task->id];
    }
    catch(Exception $e){

        $errorMsg = $e->getMessage();
        return $errorMsg;
    }
}

function getTaskThreadMsg($task, $is_finished = false){

    $task_creator  = getUserDetail($task->created_by);
    $assigned_user = getUserDetail($task->assigned_to);

    $thread_title = "Neue Aufgabe für <users/" . $assigned_user->google_user_id . "> : \n";

    if($task->status == "Done" || $is_finished)
        $thread_title .= "~*" . reverseSanitizeString($task->title) . "*~";
    else
        $thread_title .= "*" . reverseSanitizeString($task->title) . "*";

    return $thread_title;
}

function deleteGoogleTask($client, $task){

    try{

        //delete old google task
        if($task->google_task_list_id && $task->google_task_id){

            $tasksService = new \Google_Service_Tasks($client);
            $tasksService->tasks->delete($task->google_task_list_id, $task->google_task_id);

            $task->google_task_list_id = "";
            $task->google_task_id      = "";
            $task->save();
        }

        //delete task google thread
        if($task->google_thread_id && $task->google_space_id){

            //mark task as finished in google thread by strike line through
            list($created_by_user, $created_by_client) = googleUserClient($task->created_by);

            $thread_title = getTaskThreadMsg($task, true);
            updateGoogleThread($created_by_client, $task->google_thread_id, $thread_title);

            //add comment that task is deleted
            $comment_text = "This Task is deleted!";
            if($task->assigned_to){

                $task_assigned_google_user_id = getUserDetail($task->assigned_to)->google_user_id;
                $comment_text                 = "<users/" . $task_assigned_google_user_id . "> " . $comment_text;
            }
            addGoogleTaskThreadComment($task, $comment_text);

            //deleteGoogleThread($project_client, $task->google_thread_id);
        }

        return true;
    }
    catch(Exception $e){

        $errorMsg = $e->getMessage();
        return $errorMsg;
    }
}


function updateChildTaskDueDate($parent_task_id, $time_diff, $googleUsers){

    $parent_task_id = (int)$parent_task_id;

    if($parent_task_id > 0){

        $child_tasks = Task::where('parent_task_id', $parent_task_id)->get();

        foreach($child_tasks as $child_task){

            if($child_task->due_date){

                $new_time = strtotime($child_task->due_date) + $time_diff;

                $child_task->due_date = date('Y-m-d', $new_time);
                $child_task->save();

                if($child_task->title && $child_task->assigned_to && $child_task->project_id){

                    $googleUser = $googleUsers[$child_task->assigned_to];
                    $client     = refreshGoogleAccessToken($googleUser);
                    updateGoogleTask($client, $child_task, $googleUser, false);
                }

                updateChildTaskDueDate($child_task->id, $time_diff, $googleUsers);
            }
        }
    }
}

function getFileAttachmentIcon($file){

    $ext       = pathinfo($file, PATHINFO_EXTENSION);
    $file_icon = 'file';
    if($ext == 'zip')
        $file_icon = 'file-zipper';
    else if($ext == 'pdf')
        $file_icon = 'file-pdf';
    else if($ext == 'xlsx' || $ext == 'xls')
        $file_icon = 'file-excel';
    else if($ext == 'csv')
        $file_icon = 'file-csv';
    else if($ext == 'ppt' || $ext == 'pptm' || $ext == 'pptx')
        $file_icon = 'file-powerpoint';
    else if($ext == 'sldx')
        $file_icon = 'file-salesforce';
    else if($ext == 'txt')
        $file_icon = 'file-text';
    else if($ext == 'mp3' || $ext == 'mp4' || $ext == 'mov')
        $file_icon = 'file-video';
    else if($ext == 'jpg' || $ext == 'jpeg' || $ext == 'png' || $ext == 'gif' || $ext == 'webp'){

        return '<a class="d-block overlay" data-fslightbox="lightbox-basic" href="' . url('public') . '/uploads/' . $file . '"><img height="65" src="' . url('public') . '/uploads/' . $file . '"></a>';
    }

    return '<a href="' . url('public') . '/uploads/' . $file . '" target="_blank" ><i class="fa-solid fa-' . $file_icon . ' fs-5x"></i></a>';
}

function getUserDetail($user_id){

    $user = GoogleUser::selectRaw("*" . AESdecypts(GoogleUser::encryptableFields()))->where('id', $user_id)->first();
    return $user;
}

function projectClient($project){

    $user = $project && $project->space_user_id ? getUserDetail($project->space_user_id) : false;
    if(!$user){
        //throw new Exception("Google space user not found!");
        return [false, false];
    }

    $client = refreshGoogleAccessToken($user);
    return [$user, $client];
}

function googleUserClient($user_id){

    $user = getUserDetail($user_id);
    if(!$user){
        //throw new Exception("Google space user not found!");
        return [false, false];
    }

    $client = refreshGoogleAccessToken($user);
    return [$user, $client];
}

function createGoogleThread($client, $user, $google_space_id, $msg_text){

    // Create a Google Spaces service.
    $chatService = new \Google_Service_HangoutsChat($client);

    $member = new \Google_Service_HangoutsChat_User();
    $member->setName("users/" . $user->google_user_id);
    $member->setDisplayName($user->name);
    $member->setType("HUMAN");

    //translate text so formatting works on google space
    $msg_text = makeGoogleText($msg_text);

    $message = new \Google_Service_HangoutsChat_Message();
    $message->setSender($member);
    $message->setText($msg_text);

    $spaceMessageResp = $chatService->spaces_messages->create($google_space_id, $message);
    return $spaceMessageResp;
}

function updateGoogleThread($client, $google_thread_id, $msg_text){

    // Create a Google Spaces service.
    $chatService = new \Google_Service_HangoutsChat($client);

    /*
    $member = new \Google_Service_HangoutsChat_User();
    $member->setName("users/".$user->google_user_id);
    $member->setDisplayName($user->name);
    $member->setType("HUMAN");
    */

    //translate text so formatting works on google space
    $msg_text = makeGoogleText($msg_text);

    $message = new \Google_Service_HangoutsChat_Message();
    //$message->setSender($member);
    $message->setText($msg_text);

    $optParams        = [
        "updateMask" => ['text']
    ];
    $spaceMessageResp = $chatService->spaces_messages->patch($google_thread_id, $message, $optParams);
}

function deleteGoogleThread($client, $google_thread_id){

    try{

        // Create a Google Spaces service.
        $chatService = new \Google_Service_HangoutsChat($client);

        $chatService->spaces_messages->delete($google_thread_id);

        return true;
    }
    catch(Exception $e){

        $errorMsg = $e->getMessage();
        return $errorMsg;
    }
}

function getMsgThreadId($msg_id){

    $msg_id       = str_replace("messages/", "threads/", $msg_id);
    $msg_id_parts = explode(".", $msg_id);

    return $msg_id_parts[0];
}

function uploadDriveFile($client, $google_space_id, $file_path, $new_file_name = false, $google_folder_id = false){

    $driveFileId = "";
    $err_msg     = "";

    try{

        if($google_folder_id === false)
            $google_folder_id = fS("app.task_attachment_parent_folder", "1KTGUyEN1e_Ea4vTejgxOHL7pMZbIMlqB");

        //upload file to google drive folder
        $drive = new \Google_Service_Drive($client);

        $chunkSizeBytes = 1 * 1024 * 1024;
        $file_name      = basename($file_path);
        $contentType    = mime_content_type($file_path);

        if($new_file_name === false)
            $new_file_name = $file_name;

        $driveFile = new \Google_Service_Drive_DriveFile();
        $driveFile->setName($new_file_name);
        $driveFile->setMimeType($contentType);
        $driveFile->setParents([$google_folder_id]);
        unset($driveFile->exportLinks);

        // Call the API with the media upload, defer so it doesn't immediately return.
        $client->setDefer(true);

        $optParams  = array(
            'fields'             => 'id',
            'supportsTeamDrives' => true,
        );
        $fileHandle = $drive->files->create($driveFile, $optParams);

        // Create a media file upload to represent our upload process.
        $media = new \Google_Http_MediaFileUpload($client, $fileHandle, $contentType, null, true, $chunkSizeBytes);
        $media->setFileSize(filesize($file_path));

        $status = false;
        $handle = fopen($file_path, "rb");

        while(!$status && !feof($handle)){

            $chunk  = readObjectChunk($handle, $chunkSizeBytes);
            $status = $media->nextChunk($chunk);
        }

        fclose($handle);

        // The final value of $status will be the data from the API for the object that has been uploaded
        $result = false;
        if($status != false){

            $result      = $status;
            $driveFileId = $result->id;
        }

        /*
        if($driveFileId)  {

            $Permission = new \Google_Service_Drive_Permission(array(
                'type' => 'domain', //anyone
                'role' => "reader",
                'domain' => "ContentCreators",
                'allowFileDiscovery' => true,
            ));

            $resp = $drive->permissions->create($driveFileId, $Permission, array('fields' => 'id','supportsTeamDrives'=>true));
        }
        */

        // Reset to the client to execute requests immediately in the future.
        $client->setDefer(false);
    }
    catch(\Google_Service_Exception $e){

        foreach($e->getErrors() as $err){
            $err_msg .= $err['message'] . "<br>";
        }
    }
    catch(Exception $e){
        $err_msg .= $e->getMessage();
    }
    catch(Error $e){
        $err_msg .= $e->getMessage();
    }

    return $driveFileId;
}

function addGoogleThreadComment($client, $google_space_id, $google_thread_id, $msg_text, $attachment_file = false, $convert_msg_text = true, $user = false){

    try{

        if(!$user){
            $user = getUserDetail(session()->get('gl_user_id'));
        }

        $googleMsgId = getMsgThreadId($google_thread_id);

        $member = new \Google_Service_HangoutsChat_User();
        $member->setName("users/" . $user->google_user_id);
        $member->setDisplayName($user->name);
        $member->setType("HUMAN");

        //translate text so formatting works on google space
        if($convert_msg_text){
            $msg_text = makeGoogleText($msg_text);
        }

        if($attachment_file){

            $attachment_file_src = public_path('uploads') . DIRECTORY_SEPARATOR . $attachment_file;
            $drive_file_id       = uploadDriveFile($client, $google_space_id, $attachment_file_src);
            //$drive_file_id = "1HMJUiMBIEPykiil8XPg43Ph04kHNbKMs";  //file id with anyone reader permission

            if($drive_file_id){

                $msg_text .= "\n\nhttps://drive.google.com/file/d/" . $drive_file_id . "/view";

                /*
                // Upload from Drive
                $driveDataRef = new \Google_Service_HangoutsChat_DriveDataRef();
                $driveDataRef->setDriveFileId($drive_file_id);

                // Prepare the attachment data
                $attachment = new \Google_Service_HangoutsChat_Attachment();
                $attachment->setName($google_space_id."/messages/".$googleMsgId."/attachments/".$drive_file_id);
                $attachment->setDriveDataRef($driveDataRef);

                $message->setAttachment([$attachment]);
                */
            }
        }

        $message = new \Google_Service_HangoutsChat_Message();
        $message->setSender($member);
        $message->setText($msg_text);

        $thread = new \Google_Service_HangoutsChat_Thread();
        $thread->setName($googleMsgId);
        $message->setThread($thread);

        //add reply to google thread
        $options          = ['messageReplyOption' => "REPLY_MESSAGE_OR_FAIL"];
        $chatService      = new \Google_Service_HangoutsChat($client);
        $spaceMessageResp = $chatService->spaces_messages->create($google_space_id, $message, $options);

        return true;
    }
    catch(Exception $e){
        return $e->getMessage();
    }
}

function addGoogleTaskThreadComment($task, $comment_text, $client=false){

    if(is_localhost()){
        //return;
    }

    //refresh google access token
    if($client === false)
        $client = refreshGoogleAccessToken();

    $project = $task->project_id ? Project::withTrashed()->where('id', $task->project_id)->first() : false;

    if($project && $task->google_thread_id){
        addGoogleThreadComment($client, $project->google_space_id, $task->google_thread_id, $comment_text, false);
    }
    else if($task->google_space_id && $task->google_thread_id){
        addGoogleThreadComment($client, $task->google_space_id, $task->google_thread_id, $comment_text, false);
    }
}

function readObjectChunk($handle, $chunkSize){

    $byteCount  = 0;
    $giantChunk = "";

    while(!feof($handle)){

        // fread will never return more than 8192 bytes if the stream is read buffered and it does not represent a plain file
        $chunk      = fread($handle, 8192);
        $byteCount  += strlen($chunk);
        $giantChunk .= $chunk;

        if($byteCount >= $chunkSize){
            return $giantChunk;
        }
    }

    return $giantChunk;
}

function getGoogleThreadMsgs($client, $measure_id, $space_id, $thread_id){

    try{

        // Create a Google Spaces service.
        $chatService = new \Google_Service_HangoutsChat($client);

        $options = [
            "pageSize" => 1000,
            "orderBy"  => 'createTime DESC',
            "filter"   => "thread.name = " . getMsgThreadId($thread_id)
        ];

        $spaceMessagesResp = $chatService->spaces_messages->listSpacesMessages($space_id, $options);
        $thread_msgs       = array_reverse($spaceMessagesResp->getMessages());

        $messageArr = [];
        foreach($thread_msgs as $msg){

            $attachment = $msg->getAttachment();
            $attachment = $attachment ? $attachment[0] : false;

            $msgArr = [
                'text'       => $msg->getText(),
                'sender_id'  => $msg->getSender()->getName(),
                'created_at' => $msg->getCreateTime(),
                'attachment' => $attachment,
            ];

            $messageArr[] = $msgArr;
        }

        // Render view
        $viewData = [
            'space_id'     => $space_id,
            'measure_id'   => $measure_id,
            'thread_id'    => $thread_id,
            'messages_arr' => $messageArr,
            'users'        => GoogleUser::selectRaw("*" . AESdecypts(GoogleUser::encryptableFields()))->get()->keyBy('google_user_id'),
        ];

        return \Illuminate\Support\Facades\View::make('site.tasks.elements.thread_messages', $viewData)->render();
    }
    catch(Exception $e){
        return $e->getMessage();
    }
}

function threadAttachmentFile($attachment){

    $file_name          = $attachment->getContentName();
    $file_thumbnail_url = $attachment->getThumbnailUri();
    $file_download_url  = $attachment->getDownloadUri();

    $ext = pathinfo($file_name, PATHINFO_EXTENSION);

    if(in_array($ext, ['jpg', 'jpeg', 'png', 'gif', 'webp'])){

        $file_thumbnail_url_parts = explode("&sz=", $file_thumbnail_url);
        $large_img_url            = $file_thumbnail_url_parts[0];

        //class="d-block overlay" data-fslightbox="lightbox-basic"
        return '<a href="' . $large_img_url . '" class="d-block" target="_blank">
            <img src="' . $file_thumbnail_url . '" height="100">
        </a>';
    }

    return '<a href="' . $file_download_url . '" class="d-block" target="_blank">
        <img src="' . $file_thumbnail_url . '" height="100"><br>
        ' . $file_name . '
    </a>';
}

function socialChannels($channel = '', $field = false, $size = '15', $sizeCls = ''){

    $channelArray = [
        'facebook'  => ['color' => fS("social.facebook_color_code", "#3b5998"), 'icon' => '<i class="cc-icon-facebook sizeCls" aria-hidden="true"><svg focusable="false" tabindex="-1"><use xlink:href="#cc-icon-facebook"></use></svg></i>'],
        'twitter'   => ['color' => fS("social.twitter_color_code", "#1da1f2"), 'icon' => '<i class="cc-icon-twitter sizeCls" aria-hidden="true"><svg focusable="false" tabindex="-1"><use xlink:href="#cc-icon-twitter"></use></svg></i>'],
        'instagram' => ['color' => fS("social.instagram_color_code", "#e1306c"), 'icon' => '<i class="cc-icon-instagram sizeCls" aria-hidden="true"><svg focusable="false" tabindex="-1"><use xlink:href="#cc-icon-instagram"></use></svg></i>'],
        'threads'   => ['color' => fS("social.threads_color_code", "#4285f4"), 'icon' => '<i class="cc-icon-threads sizeCls" aria-hidden="true"><svg focusable="false" tabindex="-1"><use xlink:href="#cc-icon-threads"></use></svg></i>'],
        'stories'   => ['color' => fS("social.stories_color_code", "#9831d6"), 'icon' => '<i class="cc-icon-story sizeCls" aria-hidden="true"><svg focusable="false" tabindex="-1"><use xlink:href="#cc-icon-story"></use></svg></i>'],
        //'youtube'   => [ 'color' => fS("social.youtube_color_code", "#ff0000") , 'icon' => '<i class="cc-icon-youtube sizeCls" aria-hidden="true"><svg focusable="false" tabindex="-1"><use xlink:href="#cc-icon-youtube"></use></svg></i>'],
        //'blog'      => [ 'color' => fS("social.blog_color_code", "#0077b5") , 'icon' => '<i class="cc-icon-blog sizeCls" aria-hidden="true"><svg focusable="false" tabindex="-1"><use xlink:href="#cc-icon-blog"></use></svg></i>'],
    ];

    if($channel){

        $channelArray['youtube'] = ['color' => fS("social.youtube_color_code", "#ff0000"), 'icon' => '<i class="cc-icon-youtube sizeCls" aria-hidden="true"><svg focusable="false" tabindex="-1"><use xlink:href="#cc-icon-youtube"></use></svg></i>'];

        $channelArray['blog'] = ['color' => fS("social.blog_color_code", "#0077b5"), 'icon' => '<i class="cc-icon-blog sizeCls" aria-hidden="true"><svg focusable="false" tabindex="-1"><use xlink:href="#cc-icon-blog"></use></svg></i>'];

        $channelArr = $channelArray[$channel];

        if($field){

            if($field == "icon"){
                return str_replace(["15", "sizeCls"], [$size, $sizeCls], $channelArr[$field]);
            }

            return $channelArr[$field];
        }

        return $channelArr;
    }

    return $channelArray;
}

function getMentionUsers(){

    return GoogleUser::selectRaw("google_user_id" . AESdecypts(['name']))->get()->toArray();
}

function socialChannelPostStatus($status = false){

    $statusArray = [
        'Draft'              => '<i class="fa-regular fa-file-lines me-1"></i>',
        'Ready To Write'     => '<i class="fa-solid fa-pen-to-square me-1"></i>',
        'Waiting for Assets' => '<i class="fa-regular fa-image me-1"></i>',
        'Ready for Review'   => '<i class="fa-solid fa-eye me-1"></i>',
        'Scheduled'          => '<i class="fa-solid fa-rocket me-1"></i>',
        'Done'               => '<i class="fa-regular fa-circle-check me-1"></i>',
        'Failed'             => '<i class="fa-regular fa-circle-xmark me-1"></i>',
    ];

    if($status)
        return isset($statusArray[$status]) ? $statusArray[$status] : '';

    return $statusArray;
}

function linkBeautifierApiData(){

    return [
        'api_url' => (is_localhost()) ? "https://localhost/roland/link-beautifier/api/" : "https://ps.playstation.com/link-beautifier/api/",
        'token'   => 'QLC3BPJP7DHS6BOE1IT1'
    ];
}

function saveSocialCopyValidation($socialCopy, $main_topic_name){

    $api = linkBeautifierApiData();

    $data_array = array(
        "url"         => $socialCopy->post_link,
        "title"       => $socialCopy->link_headline,
        "description" => fT('w_store_sale_promotion.a_social_copy.a_description', 'Tolle Spiele jetzt im Angebot: [SALE NAME]', ['SALE NAME' => $main_topic_name]),
        "link_image"  => $socialCopy->link_image ? url('public/uploads/' . $socialCopy->link_image) : "",
        "token"       => $api['token']
    );

    $api_url = $api['api_url'] . 'social-copy-validation/save';
    return call_curl($api_url, "POST", $data_array);
}

function createShortLinkPressRelease($postData){

    $api = linkBeautifierApiData();

    $postData['token'] = $api['token'];

    $api_url = $api['api_url'] . 'social-copy-validation/save';
    return call_curl($api_url, "POST", $postData);
}

function getShortLink($url){

    $api = linkBeautifierApiData();

    $data_array = array(
        "url"   => $url,
        "token" => $api['token']
    );

    $api_url = $api['api_url'] . 'getLinkInfo';
    return call_curl($api_url, "POST", $data_array);
}

function updateShortLink($postData){

    $api = linkBeautifierApiData();

    $postData['token'] = $api['token'];

    $api_url = $api['api_url'] . 'saveLink';
    return call_curl($api_url, "POST", $postData);
}

function getStoryShortLink($url, $social_post_id){

    $api = linkBeautifierApiData();

    $data_array = array(
        "url"      => $url,
        "story_id" => $social_post_id,
        "token"    => $api['token']
    );

    $api_url = $api['api_url'] . 'saveStoryLink';
    return call_curl($api_url, "POST", $data_array);
}

function commentProcessText($comment){

    $comment = str_replace('<a ', '<a target="_blank" rel="noopener noreferrer" ', $comment);
    return $comment;
}

function mentionCommentText($text){

    //find links in text
    preg_match_all('/<span class="mention" data-user-id="(.+?)">@(.+?)<\/span>/i', $text, $matches);

    if(!empty($matches[0])){

        foreach($matches[0] as $ai => $atext){

            $mentionTag = "[MENTION]users/" . $matches[1][$ai] . "[/MENTION]";
            $text       = str_replace($atext, $mentionTag, $text);
        }
    }

    return $text;
}

function makeGoogleText($text){

    $search_words  = ["<p>&nbsp;</p>", "&nbsp;", "<li>", "<br />", "<strong>", "</strong>", "<em>", "</em>", "<i>", "</i>", "<s>", "</s>"];
    $replace_words = ["\n", " ", "* ", "\n", "*", "*", "_", "_", "_", "_", "~", "~"];
    $text          = str_replace($search_words, $replace_words, $text);

    $text = mentionCommentText($text);

    if(strpos($text, "<users/") === false && strpos($text, "Channels:") === false){

        //find links in text
        preg_match_all('/<a.*?href="(.+?)".*?>(.+?)<\/a>/i', $text, $matches);

        if(!empty($matches[0])){

            foreach($matches[0] as $ai => $atext){

                $googleLink = $matches[2][$ai]." - " . $matches[1][$ai];
                $text       = str_replace($atext, $googleLink, $text);
            }
        }

        $text = strip_tags($text);
    }

    $text = str_replace(["[LINK]", "[/LINK]"], ["<", ">"], $text);
    $text = str_replace(["[MENTION]", "[/MENTION]"], ["<", ">"], $text);

    return $text;
}

function sharedDriveNames($sharedDriveId = false){

    $sharedDrives = [
        "0AHf12eLTiO-7Uk9PVA" => "SVN Copy",
        "0AArzX2Cp19LdUk9PVA" => "Development",
        "0ALkQA_GeT7SfUk9PVA" => "PlayStation",
        "0AJE7DEBwjvJ2Uk9PVA" => "PlayStation 2025",
    ];

    if($sharedDriveId){
        return isset($sharedDrives[$sharedDriveId]) ? $sharedDrives[$sharedDriveId] : "UNSPECIFIED";
    }

    return $sharedDrives;
}

function sortByFileName($a, $b){

    return strcmp(strtolower($a["name"]), strtolower($b["name"]));
}

function ccConvertFormat($postDate, $format = "Y-m-d H:i:s"){

    $postDateTimeParts = explode(" ", $postDate);
    $dateParts         = explode(".", $postDateTimeParts[0]);

    $post_date = $dateParts[2] . "-" . $dateParts[1] . "-" . $dateParts[0] . " " . (@$postDateTimeParts[1] ? $postDateTimeParts[1] . ":00" : '06:00:00');
    return date($format, strtotime($post_date));
}

function socialPostType(){

    $postTypes = ['Link' => 'Link Post', 'Bild' => 'Bild Post', 'Video' => 'Video Post', 'Story' => 'Story', 'Other' => 'Other'];
    return $postTypes;
}

function socialPostTypeColor($type = false){

    $postTypes = [
        'Link'  => fS("social.link_color_code", "#3b5998"),
        'Bild'  => fS("social.bild_color_code", "#1da1f2"),
        'Video' => fS("social.video_color_code", "#e1306c"),
        'Story' => fS("social.story_color_code", "#9831d6"),
        'Other' => fS("social.other_color_code", "#0077b5")
    ];

    if($type)
        return isset($postTypes[$type]) ? $postTypes[$type] : '';

    return $postTypes;
}

function projectStatus($status = false){

    $statusArray = [
        'In Development'    => 'In Development',
        'Temporary Offline' => 'Temporary Offline',
        'Live'              => 'Live',
        'Deleted'           => 'Deleted',
    ];

    if($status)
        return isset($statusArray[$status]) ? $statusArray[$status] : '';

    return $statusArray;
}


function updateGoogleFolderPaths(){

    //add google folder full paths
    //refresh google access token
    $client = refreshGoogleAccessToken();

    if($client){

        $social_topics = SocialTopic::get();

        foreach($social_topics as $social_topic){

            $folderName = "";

            $currentFolderID = $social_topic->gdrive_folder_id;

            while(true){

                $driveFolder = getGdriveFolder($client, $currentFolderID);

                $folderName = ($driveFolder->getName() == 'Drive' ? sharedDriveNames($currentFolderID) : $driveFolder->getName()) . ($folderName ? "\\" . $folderName : '');

                if($driveFolder->getParents()){
                    $currentFolderID = $driveFolder->getParents()[0];
                }
                else{
                    break;
                }
            }

            $social_topic->gdrive_folder_name = "G:\\Geteilte Ablagen\\" . $folderName;
            $social_topic->save();
        }
    }
}

function initialLoginRoute($user){

    if($user->user_type == "role_admin" && $user->user_roles == "campaign-manager"){
        return route('site_content_freigaben_review_link', ['type' => 'campaign-manager']);
    }
    else{
        return route('site_overview');
    }
}

function contentFreigabenPostStatus($status = false){

    $statusArray = [
        'draft'            => 'Draft',
        'campaign-manager' => 'Campaign Manager',
        'creator'          => 'Creator',
        'ready-to-prepare' => 'Ready to Prepare',
        'done'             => 'Done',
        'declined'         => 'Declined',
        'channel-manager'  => 'Channel Manager',
    ];

    if($status)
        return isset($statusArray[$status]) ? $statusArray[$status] : '';

    return $statusArray;
}

function contentFreigabenPostCampaign($campaign = false){

    $campaignArray = [

        '1st_party'      => '1st Party',
        '3rd_party'      => '3rd Party',
        'hardware'       => 'Hardware',
        'zubehör'        => 'Zubehör',
        'store_services' => 'Store & Services'
    ];

    if($campaign)
        return isset($campaignArray[$campaign]) ? $campaignArray[$campaign] : '';

    return $campaignArray;
}

function getChannelPermission($channel_name){

    $search_val   = sanitizeStr(strtolower($channel_name));
    $search_val_e = DB::connection()->getPdo()->quote($search_val);
    $search_where = "(LOWER(CONVERT(name USING utf8)) = $search_val_e)";

    $channel = Channel::select('is_permission_granted', 'name')->whereRaw($search_where)->first();
    if(($channel && $channel->is_permission_granted)){
        return true;
    }

    return false;
}

function url_title($str){

    return Str::slug($str);
}

function isView($roles){

    $google_user = session()->get('login_user');
    $user_roles  = $google_user ? explode(",", $google_user->user_roles) : [];

    return (count(array_intersect($user_roles, $roles))) ? true : false;
}

function isAllowedPostStatus($status = false){

    $google_user = session()->get('login_user');
    $user_roles  = $google_user ? explode(",", $google_user->user_roles) : [];

    $allowed_statuses = [];
    if(in_array('social-planning-planner', $user_roles)){

        $allowed_statuses = array_merge($allowed_statuses, ["Draft", "Ready To Write", "Waiting for Assets", "Ready for Review", "Scheduled", "Done", "Failed"]);
    }

    if(in_array('social-planning-writer', $user_roles)){

        $allowed_statuses = array_merge($allowed_statuses, ["Ready To Write", "Waiting for Assets"]);
    }

    if(in_array('social-planning-publisher', $user_roles)){

        $allowed_statuses = array_merge($allowed_statuses, ["Scheduled", "Ready for Review", "Done", "Failed"]);
    }

    $allowed_statuses = array_unique($allowed_statuses);

    if($status){
        return in_array($status, $allowed_statuses);
    }

    return $allowed_statuses;
}

function makeClickUpText($text){

    //$search_words = ["<p>&nbsp;</p>", "&nbsp;", "<li>", "</li>", "<br />", "<strong>", "</strong>", "<em>", "</em>", "<i>", "</i>", "<s>", "</s>","<ul>","</ul>","<h1>","</h1>","<h2>","</h2>","<h3>","</h3>","<h4>","</h4>","<h5>","</h5>","<h6>","</h6>"];
    //$replace_words = ['\n', " ", '*   ', '\n', "", "**", "**", "_", "_", "_", "_", "~", "~",'\n','\n\n','\n\n# ','\n\n','\n\n## ','\n\n','\n\n### ','\n\n','\n\n#### ','\n\n','\n\n##### ','\n\n','\n\n###### ','\n\n'];

    $search_words  = ["<h1>", "</h1>", "<h2>", "</h2>", "<h3>", "</h3>", "<h4>", "</h4>", "<h5>", "</h5>", "<h6>", "</h6>", "<li>", "</li>", "<strong>", "</strong>", "<p>&nbsp;</p>", "&nbsp;", "<br />", "<ul>", "</ul>", "<p>", "</p>", "&auml;", "&ndash;", "&amp;"];
    $replace_words = ["# ", "", "## ", "", "### ", "", "#### ", "", "##### ", "", "###### ", "", "*   ", "", '**', '**', "\n", ' ', "\n", "", "", "\n", "", 'ä', '–', '&'];

    $text = str_replace($search_words, $replace_words, $text);
    $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');

    //if(strpos($text, "<a") === false)  {

    //find links in text
    preg_match_all('/<a.*?href="(.+?)".*?>(.+?)<\/a>/i', $text, $matches);

    if(!empty($matches[0])){

        foreach($matches[0] as $ai => $atext){

            $googleLink = "[" . $matches[2][$ai] . "](" . $matches[1][$ai] . ")";
            $text       = str_replace($atext, $googleLink, $text);
        }
    }

    $text = trim(strip_tags($text));
    //}

    return $text;
}

function socialType($type = false){

    $typeArray = [
        'instagram' => 'Instagram',
        'twitter'   => 'Twitter',
        'tiktok'    => 'Tiktok',
    ];

    if($type)
        return isset($typeArray[$type]) ? $typeArray[$type] : '';

    return $typeArray;
}

function post_embed_code($content_freigaben, $isModal){

    $embed_code = "";

    if($content_freigaben->social_type == "tiktok"){

        if($content_freigaben->tiktok_video_id){
            $embed_code = '<blockquote class="tiktok-embed" cite="' . $content_freigaben->content_link . '" data-video-id="' . $content_freigaben->tiktok_video_id . '" style="max-width: 605px;min-width: 325px;" > <section></section> </blockquote>';

            if($isModal)
                $embed_code .= '<script async src="https://www.tiktok.com/embed.js" nonce="' . csp_nonce() . '"></script>';
        }
        else{
            $embed_code = "No video ID found in the URL.";
        }
    }
    /*
    elseif($post->type == "facebook")  {

        $embed_code = '<iframe src="https://www.facebook.com/plugins/post.php?href='.$post->post_url.'&show_text=true&width=500" width="500" height="590" style="border:none;overflow:hidden" scrolling="no" frameborder="0" allowfullscreen="true" allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"></iframe>';
    }
    elseif($post->type == "youtube")  {

        $embed_code = '<iframe width="560" height="315" src="https://www.youtube.com/embed/'.$post->post_id.'" title="YouTube video player" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe>';
    }*/
    elseif($content_freigaben->social_type == "twitter"){

        $embed_code = '<blockquote class="twitter-tweet"><p lang="de" dir="ltr"><a href="' . $content_freigaben->content_link . '">post</a></blockquote>';

        if($isModal)
            $embed_code .= '<script async src="https://platform.twitter.com/widgets.js" charset="utf-8" nonce="' . csp_nonce() . '"></script>';
    }
    elseif($content_freigaben->social_type == "instagram"){

        $embed_code = '<blockquote class="instagram-media" data-instgrm-captioned data-instgrm-permalink="' . $content_freigaben->content_link . '" data-instgrm-version="14" style="background:#FFF; border:0; border-radius:3px; box-shadow:0 0 1px 0 rgba(0,0,0,0.5),0 1px 10px 0 rgba(0,0,0,0.15); margin: 1px; padding:0; width:99.375%; width:-webkit-calc(100% - 2px); width:calc(100% - 2px);"></blockquote>';
    }

    return $embed_code;
}

function getTikTokVideoID($url){

    // Define the regular expression pattern to extract the video ID
    $pattern = '/(video|photo)\/(\d+)\?/';

    // Use preg_match to find the video ID
    if(preg_match($pattern, $url, $matches)){

        // The video ID will be in the second element of the $matches array
        return $matches[1];
    }
    else{

        $finalUrl = getFinalTiktokUrl($url);
        // Regular expression to match the TikTok video URL pattern
        $pattern = '/(?:https?:\/\/)?(?:www\.)?tiktok\.com\/(?:@[\w.-]+\/(video|photo)\/|v\/|embed\/|)(\d+)/';

        // Use preg_match to find a match
        if(preg_match($pattern, $finalUrl, $matches)){
            // Return the matched video ID
            return $matches[2];
        }
        else{
            // Return false if no match is found
            return false;
        }
    }
}

function getFinalTiktokUrl($url){
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);  // Follow redirects
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);  // Return the content
    curl_exec($ch);
    $finalUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL); // Get the last effective URL
    curl_close($ch);
    return $finalUrl;
}

function getCacheRevision(){
    return DataRevision::getRevision('cache.revision');
}

function removeNbspFromUrl($str){

    $last_url_parts  = explode('&nbsp', $str);
    $last_url_parts2 = explode('&amp;nbsp', $last_url_parts[0]);
    return $last_url_parts2[0];
}

function getLastLinkFromString($string){
    if($string){
        // Use regex to match all URLs in the string
        preg_match_all('/https?:\/\/[^\s]+/', $string, $matches);

        // Get the last URL from the matches
        $last_url = end($matches[0]);

        if($last_url){
            // Remove any trailing punctuation
            $last_url = rtrim($last_url, '.,;!?');

            // Validate and correct the URL if needed
            $parsed_url = parse_url($last_url);
            if(!isset($parsed_url['scheme'])){
                $last_url = 'https://' . $last_url;
            }

            return removeNbspFromUrl($last_url);
        }
    }
    return NULL;
}

function getMetaDataFromUrl($url){
    $finalMetaArr = [];
    $metaData     = [];
    if($url){

        $metaData = MetaData::fetch($url);

        if(!empty($metaData)){

            $finalArr = array();
            foreach($metaData as $key => $value){
                $key            = str_replace(array(":", "-"), "_", $key);
                $finalArr[$key] = $value;
            }

            $og_title       = isset($finalArr['og_title']) && is_array($finalArr['og_title']) ? $finalArr['og_title'][0] : @$finalArr['og_title'];
            $og_description = isset($finalArr['og_description']) && is_array($finalArr['og_description']) ? $finalArr['og_description'][0] : @$finalArr['og_description'];
            $og_type        = isset($finalArr['og_type']) && is_array($finalArr['og_type']) ? $finalArr['og_type'][0] : @$finalArr['og_type'];
            $og_image       = isset($finalArr['og_image']) && is_array($finalArr['og_image']) ? $finalArr['og_image'][0] : @$finalArr['og_image'];
            $og_site_name   = isset($finalArr['og_site_name']) && is_array($finalArr['og_site_name']) ? $finalArr['og_site_name'][0] : @$finalArr['og_site_name'];

            $finalMetaArr["title"]       = $og_title != "" ? $og_title : (isset($finalArr['title']) ? $finalArr['title'] : "");
            $finalMetaArr["description"] = $og_description ? $og_description : (isset($finalArr['description']) ? $finalArr['description'] : "");
            $finalMetaArr["image"]       = $og_image ? $og_image : (isset($finalArr['image']) ? $finalArr['image'] : "");
            $finalMetaArr["site_name"]   = $og_site_name;
            $finalMetaArr["locale"]      = isset($finalArr['og_locale']) ? $finalArr['og_locale'] : "";
            $finalMetaArr["type"]        = isset($finalArr['og_type']) ? $finalArr['og_type'] : "";

            return $finalMetaArr;
        }
        else
            return $finalMetaArr;
    }
    else
        return $finalMetaArr;
}

function getDomainFromUrl($url){
    $parsedUrl = parse_url($url);
    return isset($parsedUrl['host']) ? $parsedUrl['host'] : null;
}

function addAnchorTags($text, $channel){

    $pattern  = '/(https?:\/\/[^\s]+)/';
    $callback = function ($matches) use ($channel){
        $url = $matches[1];

        if($channel == 'threads'){
            $displayUrl = preg_replace('/^https?:\/\/(www\.)?/', '', $url);
            if(strlen($displayUrl) > 26){
                $displayUrl = substr($displayUrl, 0, 26) . '...';
            }
        }
        elseif($channel == 'facebook'){
            $displayUrl = $url;
            if(strlen($displayUrl) > 58){
                $displayUrl = substr($displayUrl, 0, 58) . '...';
            }
        }
        else{
            $displayUrl = preg_replace('/^https?:\/\/(www\.)?/', '', $url);
        }

        return '<a href="' . $url . '" target="_blank"  rel="nofollow noreferrer">' . $displayUrl . '</a>';
    };
    return preg_replace_callback($pattern, $callback, $text);
}

function copyFileToUploads($fileName, $channel, $type = ''){

    //copy files to uploads folder
    $tmpFileFullPath    = public_path('uploads/tmp') . DIRECTORY_SEPARATOR . $fileName;
    $uploadDestFullPath = public_path('uploads') . DIRECTORY_SEPARATOR . $fileName;

    if($type == 'duplicate' && file_exists($uploadDestFullPath)){

        $fileExt  = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        $fileName = mt_rand() . rand() . "_" . $channel . "." . $fileExt;

        //duplicate file to uploads directory
        $new_uploadDestFullPath = public_path('uploads') . DIRECTORY_SEPARATOR . $fileName;
        @File::copy($uploadDestFullPath, $new_uploadDestFullPath);
    }
    elseif(file_exists($tmpFileFullPath)){

        //copy to uploads directory
        @File::copy($tmpFileFullPath, $uploadDestFullPath);
    }

    return $fileName;
}

function externalImageExists($url){
    $headers = @get_headers($url);
    if($headers && strpos($headers[0], '200'))
        return $url;
    else
        return asset('public/assets') . '/media/avatars/blank.png';
}

function getWeekStartAndEnd($date){
    // Create a DateTime object from the given date
    $dateTime = new DateTime($date);

    // Clone the date object to calculate the start of the week
    $startOfWeek = clone $dateTime;
    // Modify the date to the start of the week (Sunday)
    $startOfWeek->modify('last Sunday');
    // If the given date is a Sunday, adjust to start from that day
    if($dateTime->format('w') == 0){
        $startOfWeek = $dateTime;
    }

    // Clone the start of the week object to calculate the end of the week
    $endOfWeek = clone $startOfWeek;
    // Modify the date to the end of the week (Saturday)
    $endOfWeek->modify('next Saturday');

    // Format the start and end of the week as strings
    $start = $startOfWeek->format('Y-m-d');
    $end   = $endOfWeek->format('Y-m-d');

    return array('start' => $start, 'end' => $end);
}

function getPostMedia($fileName, $folderName = false){

    $tmpFullPath = public_path('uploads/tmp') . DIRECTORY_SEPARATOR . $fileName;
    if($folderName)
        $uploadFullPath = public_path() . DIRECTORY_SEPARATOR . $fileName;
    else
        $uploadFullPath = public_path('uploads') . DIRECTORY_SEPARATOR . $fileName;

    $returnFile = asset('public') . '/gfx/no-image.jpg';

    if(file_exists($uploadFullPath)){

        if($folderName)
            $returnFile = asset('public') . '/' . $fileName;
        else
            $returnFile = asset('public') . '/uploads/' . $fileName;
    }
    elseif(file_exists($tmpFullPath)){
        $returnFile = asset('public') . '/uploads/tmp/' . $fileName;
    }

    return $returnFile;
}

function getCHannelInfoText($social_post_channel){

    $info = '0';
    if($social_post_channel == 'facebook')
        $info = '0 / 63206';
    elseif($social_post_channel == 'twitter')
        $info = '0 / 10000';
    elseif($social_post_channel == 'instagram')
        $info = '0 / 2200';
    elseif($social_post_channel == 'threads')
        $info = '0 / 500';

    return $info;
}

function trailingslashit($value){
    return untrailingslashit($value) . '/';
}

function untrailingslashit($value){
    return rtrim($value, '/\\');
}

function cc_is_writable($path){
    return @is_writable($path);
}

function get_temp_dir(){

    static $temp = '';

    if(function_exists('sys_get_temp_dir')){
        $temp = sys_get_temp_dir();
        if(@is_dir($temp) && cc_is_writable($temp)){
            return trailingslashit($temp);
        }
    }

    $temp = ini_get('upload_tmp_dir');
    if(@is_dir($temp) && cc_is_writable($temp)){
        return trailingslashit($temp);
    }

    return base_path() . '/tmp/';
}

function cc_read_video_metadata($file){

    if(!file_exists($file)){
        return false;
    }

    $metadata = array();

    if(!defined('GETID3_TEMP_DIR')){
        define('GETID3_TEMP_DIR', get_temp_dir());
    }

    $id3 = new getID3();
    // Required to get the `created_timestamp` value.
    $id3->options_audiovideo_quicktime_ReturnAtomData = true; // phpcs:ignore WordPress.NamingConventions.ValidVariableName

    $data = $id3->analyze($file);

    if(isset($data['video']['lossless'])){
        $metadata['lossless'] = $data['video']['lossless'];
    }

    if(!empty($data['video']['bitrate'])){
        $metadata['bitrate'] = (int)$data['video']['bitrate'];
    }

    if(!empty($data['video']['bitrate_mode'])){
        $metadata['bitrate_mode'] = $data['video']['bitrate_mode'];
    }

    if(!empty($data['filesize'])){
        $metadata['filesize'] = (int)$data['filesize'];
    }

    if(!empty($data['mime_type'])){
        $metadata['mime_type'] = $data['mime_type'];
    }

    if(!empty($data['playtime_seconds'])){
        $metadata['length'] = (int)round($data['playtime_seconds']);
    }

    if(!empty($data['playtime_string'])){
        $metadata['length_formatted'] = $data['playtime_string'];
    }

    if(!empty($data['video']['resolution_x'])){
        $metadata['width'] = (int)$data['video']['resolution_x'];
    }

    if(!empty($data['video']['resolution_y'])){
        $metadata['height'] = (int)$data['video']['resolution_y'];
    }

    if(!empty($data['fileformat'])){
        $metadata['fileformat'] = $data['fileformat'];
    }

    if(!empty($data['video']['dataformat'])){
        $metadata['dataformat'] = $data['video']['dataformat'];
    }

    if(!empty($data['video']['encoder'])){
        $metadata['encoder'] = $data['video']['encoder'];
    }

    if(!empty($data['video']['codec'])){
        $metadata['codec'] = $data['video']['codec'];
    }

    if(!empty($data['audio'])){
        unset($data['audio']['streams']);
        $metadata['audio'] = $data['audio'];
    }

    if(empty($metadata['created_timestamp'])){
        $created_timestamp = wp_get_media_creation_timestamp($data);

        if(false !== $created_timestamp){
            $metadata['created_timestamp'] = $created_timestamp;
        }
    }

    wp_add_id3_tag_data($metadata, $data);

    $file_format = isset($metadata['fileformat']) ? $metadata['fileformat'] : null;

    /**
     * Filters the array of metadata retrieved from a video.
     *
     * In core, usually this selection is what is stored.
     * More complete data can be parsed from the `$data` parameter.
     *
     * @param array       $metadata    Filtered video metadata.
     * @param string      $file        Path to video file.
     * @param string|null $file_format File format of video, as analyzed by getID3.
     *                                 Null if unknown.
     * @param array       $data        Raw metadata from getID3.
     *
     * @since 4.9.0
     *
     */
    return [
        'metadata'    => $metadata,
        'file'        => $file,
        'file_format' => $file_format,
        'data'        => $data
    ];
}

function wp_add_id3_tag_data(&$metadata, $data){
    foreach(array('id3v2', 'id3v1') as $version){
        if(!empty($data[$version]['comments'])){
            foreach($data[$version]['comments'] as $key => $list){
                if('length' !== $key && !empty($list)){
                    $metadata[$key] = wp_kses_post(reset($list));
                    // Fix bug in byte stream analysis.
                    if('terms_of_use' === $key && str_starts_with($metadata[$key], 'yright notice.')){
                        $metadata[$key] = 'Cop' . $metadata[$key];
                    }
                }
            }
            break;
        }
    }

    if(!empty($data['id3v2']['APIC'])){
        $image = reset($data['id3v2']['APIC']);
        if(!empty($image['data'])){
            $metadata['image'] = array(
                'data'   => $image['data'],
                'mime'   => $image['image_mime'],
                'width'  => $image['image_width'],
                'height' => $image['image_height'],
            );
        }
    }
    elseif(!empty($data['comments']['picture'])){
        $image = reset($data['comments']['picture']);
        if(!empty($image['data'])){
            $metadata['image'] = array(
                'data' => $image['data'],
                'mime' => $image['image_mime'],
            );
        }
    }
}

function wp_get_media_creation_timestamp($metadata){
    $creation_date = false;

    if(empty($metadata['fileformat'])){
        return $creation_date;
    }

    switch($metadata['fileformat']){
        case 'asf':
            if(isset($metadata['asf']['file_properties_object']['creation_date_unix'])){
                $creation_date = (int)$metadata['asf']['file_properties_object']['creation_date_unix'];
            }
            break;

        case 'matroska':
        case 'webm':
            if(isset($metadata['matroska']['comments']['creation_time'][0])){
                $creation_date = strtotime($metadata['matroska']['comments']['creation_time'][0]);
            }
            elseif(isset($metadata['matroska']['info'][0]['DateUTC_unix'])){
                $creation_date = (int)$metadata['matroska']['info'][0]['DateUTC_unix'];
            }
            break;

        case 'quicktime':
        case 'mp4':
            if(isset($metadata['quicktime']['moov']['subatoms'][0]['creation_time_unix'])){
                $creation_date = (int)$metadata['quicktime']['moov']['subatoms'][0]['creation_time_unix'];
            }
            break;
    }

    return $creation_date;
}

function removeCountryFromUrl($url){

    $pattern = "/playstation.com\/([a-zA-Z]{2}-[a-zA-Z]{2})(?![^\/])/i";
    return preg_replace($pattern, 'playstation.com', $url);
}

function max_video_filesize($channel){

    $max_file_size = 0;

    if($channel == 'facebook'){
        $max_file_size = 4294967296;
    }
    else if($channel == 'twitter'){
        $max_file_size = 536870912;
    }
    else if(in_array($channel, ['threads', 'instagram'])){
        $max_file_size = 1073741824;
    }

    return $max_file_size;
}

function socialPlanningMediaValidation($common_attachment, $channel, $do_process = false){

    $errors = [];

    $attachmentExt = strtolower(pathinfo($common_attachment, PATHINFO_EXTENSION));

    if(file_exists($common_attachment)){

        if(in_array($attachmentExt, ["jpg", "jpeg", "png", "webp"])){

            $mime_type        = mime_content_type($common_attachment);
            $imageSizeInBytes = filesize($common_attachment);

            $requiredSizeInBytes = false;
            if($channel == 'instagram' || $channel == 'threads'){
                $requiredSizeInBytes = 8000000;
            }
            else if($channel == 'twitter'){
                $requiredSizeInBytes = 5000000;
            }
            else if($channel == 'facebook'){
                $requiredSizeInBytes = 15000000;
            }

            if($requiredSizeInBytes && $imageSizeInBytes > $requiredSizeInBytes){

                if($do_process){

                    $simlpeImg = new SimpleImageCC();
                    $simlpeImg->load($common_attachment);

                    $reduce_width = ceil(($imageSizeInBytes - $requiredSizeInBytes) * 230 / (1024 * 1024)); //remove MB * 250 width

                    $min_with   = 1440;
                    $orig_width = $simlpeImg->getWidth();
                    $new_width  = $orig_width - $reduce_width;

                    do{

                        if($new_width < $min_with){
                            $new_width = $min_with;
                        }

                        $simlpeImg->resizeToWidth($new_width);
                        $simlpeImg->save($common_attachment, false, 100);
                        clearstatcache(false, $common_attachment);

                        //check image again for file size
                        $imageSizeInBytes = filesize($common_attachment);

                        $new_width = $new_width - round($orig_width / 10);
                    }
                    while($new_width > $min_with && $imageSizeInBytes > $requiredSizeInBytes);

                    //check image again for file size
                    $imageSizeInBytes = filesize($common_attachment);
                }

                if($imageSizeInBytes > $requiredSizeInBytes){

                    array_push($errors, ['message' => ucfirst($channel) . " don't support image file size over " . round($requiredSizeInBytes / 1000000) . "MB!", 'type' => $channel, 'media' => $common_attachment]);
                }
            }


            $valid_mime_types = array("image/jpeg", "image/jpg", "image/png", "image/webp", "image/avif");
            if(!@in_array($mime_type, $valid_mime_types)){ // Only process supported mime types

                if($do_process){

                    $simlpeImg = new SimpleImageCC();
                    $simlpeImg->load($common_attachment);
                    $simlpeImg->save($common_attachment, false, 100);
                    clearstatcache(false, $common_attachment);

                    //check image mime type again
                    $mime_type = mime_content_type($common_attachment);
                }

                if(!@in_array($mime_type, $valid_mime_types)){
                    array_push($errors, ['message' => "Media does not have valid mime type!", 'type' => $channel, 'media' => $common_attachment]);
                }
            }


            if($channel == 'instagram'){

                $minAspectRatio = 0.5625;
                $maxAspectRatio = 1.91;
                $dimensions     = getimagesize($common_attachment);

                if($dimensions === false){
                    array_push($errors, ['message' => "Media dimensions is not getting!", 'type' => 'instagram', 'media' => $common_attachment]);
                }
                else{

                    $width             = $dimensions[0];
                    $height            = $dimensions[1];
                    $actualAspectRatio = $width / $height;
                    $isValid           = $actualAspectRatio >= $minAspectRatio && $actualAspectRatio <= $maxAspectRatio;

                    if(!$isValid){
                        array_push($errors, ['message' => "Instagram posts supports aspect ratios between 9:16 and 191:100.", 'type' => 'instagram', 'media' => $common_attachment]);
                    }
                }

                /*
                list($width, $height) = getimagesize($common_attachment);
                if($width < 320 && $height > 1440){
                    array_push($errors, [ 'message' => "Instagram image should be between 320 and 1440.", 'type' => 'instagram', 'media' => $common_attachment]);
                }
                */
            }

            if($channel == 'threads'){

                /*
                list($width, $height) = getimagesize($common_attachment);
                if($width < 320 && $height > 1440){
                    array_push($errors, [ 'message' => "Threads image should be between 320 and 1440.", 'type' => 'threads', 'media' => $common_attachment]);
                }
                */
            }

            if($channel == 'stories'){

                $dimensions = getimagesize($common_attachment);

                if($dimensions === false){
                    array_push($errors, ['message' => "Media dimensions is not getting!", 'type' => 'story', 'media' => $common_attachment]);
                }
                else{

                    $width  = $dimensions[0];
                    $height = $dimensions[1];

                    if($width != 1080 || $height != 1920){
                        array_push($errors, ['message' => "Image size need to be 1080X1920", 'type' => 'story', 'media' => $common_attachment]);
                    }
                }
            }
        }

        elseif($attachmentExt == "mov" || $attachmentExt == "mp4"){

            $video_data = cc_read_video_metadata($common_attachment);

            if($channel == 'facebook'){

                if($video_data){

                    if(isset($video_data['metadata']['length']) && ($video_data['metadata']['length'] > 14400)){

                        array_push($errors, ['message' => "Video Duration must be less than 14400 seconds", 'type' => 'facebook', 'media' => $common_attachment]);
                    }

                    if(!isset($video_data['metadata']['filesize'])){
                        array_push($errors, ['message' => "Video file has some error, please upload again", 'type' => 'twitter', 'media' => $common_attachment]);
                    }
                    else if($video_data['metadata']['filesize'] > max_video_filesize($channel)){
                        array_push($errors, ['message' => "Video file size must be less than 4GB", 'type' => 'facebook', 'media' => $common_attachment]);
                    }
                }
                else{
                    array_push($errors, ['message' => "Video metadata not found!", 'type' => 'facebook', 'media' => $common_attachment]);
                }
            }

            if($channel == 'twitter'){

                if($video_data){

                    if(isset($video_data['metadata']['length']) && ($video_data['metadata']['length'] < 0.5 || $video_data['metadata']['length'] > 140)){

                        array_push($errors, ['message' => "Video Duration must be between 0.5 seconds and 140 seconds", 'type' => 'twitter', 'media' => $common_attachment]);
                    }

                    if(isset($video_data['metadata']['audio']['channelmode']) && (!in_array($video_data['metadata']['audio']['channelmode'], ['mono', 'stereo']))){

                        array_push($errors, ['message' => "Video audio must be mono or stereo", 'type' => 'twitter', 'media' => $common_attachment]);
                    }

                    if(!isset($video_data['metadata']['filesize'])){
                        array_push($errors, ['message' => "Video file has some error, please upload again", 'type' => 'twitter', 'media' => $common_attachment]);
                    }
                    else if($video_data['metadata']['filesize'] > max_video_filesize($channel)){
                        array_push($errors, ['message' => "Video file size must be less than 200MB", 'type' => 'twitter', 'media' => $common_attachment]);
                    }

                    /*
                    if($video_data['metadata']['width'] < 32 || $video_data['metadata']['width'] > 1280 || $video_data['metadata']['height'] < 32 || $video_data['metadata']['height'] > 1024){
                        array_push($errors, [ 'message' => "Video Dimensions must be between 32x32 and 1280x1024", 'type' => 'twitter', 'media' => $common_attachment]);
                    }
                    */

                    if(isset($video_data['data']['video']['frame_rate']) && $video_data['data']['video']['frame_rate'] > 60){
                        array_push($errors, ['message' => "Video Frame rate must be 60 FPS or less", 'type' => 'twitter', 'media' => $common_attachment]);
                    }

                    if($video_data['metadata']['height'] / $video_data['metadata']['width'] < 0.3333 || $video_data['metadata']['height'] / $video_data['metadata']['width'] > 3){
                        array_push($errors, ['message' => "Video Aspect ratio must be between 1:3 and 3:1", 'type' => 'twitter', 'media' => $common_attachment]);
                    }
                }
                else{
                    array_push($errors, ['message' => "Video metadata not found!", 'type' => 'twitter', 'media' => $common_attachment]);
                }
            }

            if(in_array($channel, ['threads', 'instagram'])){

                if($video_data){

                    if(
                        $do_process && ($video_data['metadata']['width'] > 1920
                            || (isset($video_data['data']['video']['bitrate']) && $video_data['data']['video']['bitrate'] > 26214400)
                        )
                    ){

                        $video_command = "";

                        if($video_data['metadata']['width'] > 1920){
                            $video_command .= ' -vf scale="1920:-1" ';
                        }

                        if(isset($video_data['data']['video']['bitrate']) && $video_data['data']['video']['bitrate'] > 26214400)
                            $video_command .= " -b:v 24000k -bufsize 24000k ";

                        //reduce video bitrate to 23mb
                        if($video_command != ""){

                            cc_process_video($common_attachment, $video_command);

                            clearstatcache(false, $common_attachment);

                            $video_data = cc_read_video_metadata($common_attachment);
                        }
                    }

                    if(isset($video_data['metadata']['width']) && $video_data['metadata']['width'] > 1920){
                        array_push($errors, ['message' => "Video width must be less than 1920", 'type' => $channel, 'media' => $common_attachment]);
                    }

                    if(!isset($video_data['metadata']['filesize'])){
                        array_push($errors, ['message' => "Video file has some error, please upload again", 'type' => 'twitter', 'media' => $common_attachment]);
                    }
                    else if(isset($video_data['metadata']['filesize']) && $video_data['metadata']['filesize'] > max_video_filesize($channel)){
                        array_push($errors, ['message' => "Video file size must be less than 1GB", 'type' => $channel, 'media' => $common_attachment]);
                    }

                    if(isset($video_data['data']['video']['bitrate']) && $video_data['data']['video']['bitrate'] > 26214400){

                        array_push($errors, ['message' => "Video bitrate: VBR, 25Mbps maximum", 'type' => $channel, 'media' => $common_attachment]);
                    }

                    if(isset($video_data['data']['video']['frame_rate']) && ($video_data['data']['video']['frame_rate'] > 60 || $video_data['data']['video']['frame_rate'] < 23)){
                        array_push($errors, ['message' => "Video Frame rate must be between 23 FPS and 60 FPS", 'type' => $channel, 'media' => $common_attachment]);
                    }

                    if(isset($video_data['metadata']['audio']['channelmode']) && (!in_array($video_data['metadata']['audio']['channelmode'], ['mono', 'stereo']))){

                        array_push($errors, ['message' => "Video audio must be mono or stereo", 'type' => $channel, 'media' => $common_attachment]);
                    }

                    if(isset($video_data['metadata']['audio']['sample_rate']) && $video_data['metadata']['audio']['sample_rate'] > 48000){

                        array_push($errors, ['message' => "Video audio must be 48khz sample rate maximum", 'type' => $channel, 'media' => $common_attachment]);
                    }
                }
            }

            if($channel == 'instagram'){

                if($video_data){

                    if(isset($video_data['metadata']['length']) && ($video_data['metadata']['length'] < 3 || $video_data['metadata']['length'] > 900)){

                        array_push($errors, ['message' => "Video Duration must be between 3 seconds and 900 seconds", 'type' => 'instagram', 'media' => $common_attachment]);
                    }
                }
                else{
                    array_push($errors, ['message' => "Video metadata not found!", 'type' => 'instagram', 'media' => $common_attachment]);
                }
            }

            if($channel == 'threads'){

                if($video_data){

                    if(isset($video_data['metadata']['length']) && ($video_data['metadata']['length'] > 300)){

                        array_push($errors, ['message' => "Video Duration must be less than 300 seconds", 'type' => 'threads', 'media' => $common_attachment]);
                    }

                    /*
                    //its working also when audio bitrate is high
                    if(isset($video_data['metadata']['audio']['bitrate']) && $video_data['metadata']['audio']['bitrate'] > 131072){
                        array_push($errors, [ 'message' => "Audio bitrate: VBR, 128kbps maximum", 'type' => 'threads', 'media' => $common_attachment]);
                    }
                    */
                }
                else{
                    array_push($errors, ['message' => "Video metadata not found!", 'type' => 'threads', 'media' => $common_attachment]);
                }
            }
        }

        elseif($attachmentExt == "gif"){

            if($channel == 'twitter'){

                $imageSizeInBytes = filesize($common_attachment);

                if($imageSizeInBytes > 15000000){ //15MB

                    array_push($errors, ['message' => "Twitter not supports file size over 15MB!", 'type' => 'twitter', 'media' => $common_attachment]);
                }
            }
        }
        else{

            array_push($errors, ['message' => "Not allowed file type!", 'type' => 'twitter', 'media' => $common_attachment]);
        }
    }
    else{

        array_push($errors, ['message' => "Attachment file is missing, please upload again!", 'type' => $channel, 'media' => $common_attachment]);
    }

    return $errors;
}

function cc_process_video($input_video, $command){

    $file_name    = pathinfo($input_video, PATHINFO_BASENAME);
    $output_video = str_replace($file_name, "output-" . $file_name, $input_video);

    $command = ffmpeg_path() . ' -y -i "' . $input_video . '" ' . $command . ' "' . $output_video . '"';
    shell_exec($command);

    if(file_exists($output_video) && filesize($output_video) > 0){

        File::move($output_video, $input_video);
    }
}

function ffmpeg_path(){

    if(is_localhost()){
        return "ffmpeg";
    }

    return "/usr/bin/ffmpeg";
}

function deleteDir($dirPath){

    if(is_dir($dirPath)){

        if(substr($dirPath, strlen($dirPath) - 1, 1) != '/'){
            $dirPath .= '/';
        }

        $files = glob($dirPath . '*', GLOB_MARK);

        foreach($files as $file){

            if(is_dir($file)){
                deleteDir($file);
            }
            else{
                unlink($file);
            }
        }

        rmdir($dirPath);
    }
}

function getInstagramPostFiles($channel_name, $instaPostId){

    $command   = "instaloader --no-video-thumbnails  --no-captions  --no-metadata-json -- -" . $instaPostId . " 2>&1";
    $output    = shell_exec($command);
    $dirPath   = '';
    $fileNames = [];

    if(strpos($output, "Errors or warnings") === false){

        $output = str_replace(" exists", "", $output);

        $dirName   = "-" . $instaPostId . "/";
        $fileNames = explode($dirName, $output);

        $fileNames = array_map('trim', $fileNames);
        $fileNames = array_filter($fileNames);

        if($fileNames && !empty($fileNames)){

            //refresh google access token
            $client = refreshGoogleAccessToken();

            //create folder name with channel
            $ugc_folder_id = '1O8qyPIyDhuFrE6G3E58K-rfDGHm3tEUt';
            $newFolder     = createGdriveFolder($client, $channel_name, $ugc_folder_id);

            if($newFolder){

                $dirPath = base_path() . "/" . $dirName . "/";

                foreach($fileNames as $fileName){

                    $attachment_file_src = $dirPath . $fileName;

                    if(file_exists($attachment_file_src)){

                        $new_file_name = $instaPostId . "_" . $fileName;
                        $drive_file_id = uploadDriveFile($client, false, $attachment_file_src, $new_file_name, $newFolder->id);
                    }
                }
            }
        }

        deleteDir($dirPath);

        return ['files' => $fileNames, 'folder_id' => ($newFolder) ? $newFolder->id : NULL];
    }
    else{

        return $output;
    }
}

function ccIsJson($string){

    json_decode($string);
    return json_last_error() === JSON_ERROR_NONE;
}

function apify_call($api_path, $method = "GET", $data = array(), $headers = []){

    $BASE_URL  = "https://api.apify.com/v2/";
    $api_token = "?token=**********************************************";

    $api_url = $BASE_URL . $api_path . $api_token;
    //echo $api_url; pr($data); pr($headers);

    $apiResp = call_curl($api_url, $method, $data, $headers);

    $apiRespArr = false;
    if($apiResp){
        $apiRespArr = json_decode($apiResp, true);
    }

    return $apiRespArr;
}

function copyExternalFileToUploads($media_url){

    // Define destination folder and file name
    $file_name          = basename($media_url);
    $uploadDestFullPath = public_path('uploads') . DIRECTORY_SEPARATOR . $file_name;

    // Download and save the image
    $image_data = file_get_contents($media_url);

    if($image_data !== false){
        file_put_contents($uploadDestFullPath, $image_data);
        return $file_name;
    }
}

function getStoreSaleProductForPriceClass($storeSale, $dayNeedle = "2024-01-01", $priceClass = 1, $minDiscountPercent = 5){

    $storeSaleId = $storeSale->id;

    $img_where = $storeSale->sale_image_template_id ? '1=1' : 'edition_key_art IS NOT NULL';

    \Illuminate\Support\Facades\Log::info(__METHOD__ . " - Start - Store Sale ID: $storeSaleId, Day Needle: $dayNeedle, Price Class: $priceClass, Min Discount Percent: $minDiscountPercent");

    // 1 = >0, 2 = >30, 3 = >50
    [$minPrice, $maxPrice] =
            [
                1 => [0, 30],
                2 => [30, 50],
                3 => [50, 100],
                4 => [100, 1000],
            ][$priceClass] ?? [null, null];
    if($maxPrice === null || $minPrice === null){
        \Illuminate\Support\Facades\Log::info(__METHOD__ . " - End - Price Class not found");
        return StoreSaleProduct::where('store_sale_id', $storeSaleId)
                               ->where('dis_percent', '>', 0)
                               ->where('used_in_copy', 0)
                               ->whereNotNull("portrait_banner")
                               ->where("portrait_banner", "!=", "")
                               ->whereNotNull("edition_key_art")
                               ->where("edition_key_art", "!=", "")
                               ->whereRaw($img_where)
                               ->where(DB::raw('DATE(start_date)'), '<=', $dayNeedle)->where(DB::raw('DATE(end_date)'), '>=', $dayNeedle)
                               ->inRandomOrder()->first();
    }
    $saleProduct = StoreSaleProduct::where('store_sale_id', $storeSaleId)
                                   ->where('dis_percent', '>', 0)
                                   ->where('used_in_copy', 0)
                                   ->whereRaw($img_where)
                                   ->where(DB::raw('DATE(start_date)'), '<=', $dayNeedle)->where(DB::raw('DATE(end_date)'), '>=', $dayNeedle)
                                   ->where("normal_price", ">=", $minPrice)
                                   ->where("normal_price", "<", $maxPrice)
                                   ->where("dis_percent", ">=", @(float)$minDiscountPercent)
                                   ->whereNotNull("portrait_banner")
                                   ->where("portrait_banner", "!=", "")
                                   ->whereNotNull("edition_key_art")
                                   ->where("edition_key_art", "!=", "")
                                   ->inRandomOrder()->first();
    if($saleProduct){
        \Illuminate\Support\Facades\Log::info(__METHOD__ . " - End - Sale Product Found: " . $saleProduct->id);
        return $saleProduct;
    }
    \Illuminate\Support\Facades\Log::info(__METHOD__ . " searching for: $storeSaleId, $dayNeedle, $priceClass, $minDiscountPercent");
    $saleProduct = StoreSaleProduct::where('store_sale_id', $storeSaleId)
                                   ->where('dis_percent', '>', 0)
                                   ->where('used_in_copy', 0)
                                   ->whereRaw($img_where)
                                   ->where(DB::raw('DATE(start_date)'), '<=', $dayNeedle)->where(DB::raw('DATE(end_date)'), '>=', $dayNeedle)
                                   ->where("normal_price", ">=", $minPrice)
                                   ->where("normal_price", "<", $maxPrice)
                                   ->inRandomOrder()->first();
    return $saleProduct ?: StoreSaleProduct::where('store_sale_id', $storeSaleId)
                                           ->where('dis_percent', '>', 0)
                                           ->where('used_in_copy', 0)
                                           ->whereRaw($img_where)
                                           ->where('start_date', '<=', $dayNeedle)->where('end_date', '>=', $dayNeedle)
                                           ->inRandomOrder()->first();
}

function removeHashtags(string $text): string{
    $pattern = '/#\w+/';
    if(preg_match($pattern, $text)){
        $cleaned = preg_replace($pattern, '', $text);    // Remove hashtags
        $cleaned = preg_replace('/\s+/', ' ', $cleaned); // Remove multiple spaces and trim
        return trim($cleaned);
    }
    return $text;
}

function generateAiSaleScriptionFromSalesObj($saleProductObj){
    $description = "";
    if($saleProductObj->description){
        $description = $saleProductObj->description;
    }
    else{
        $data        = gamecatalogeApi("product/detail/" . $saleProductObj->ps_id, "GET", []);
        $description = $data["data"]["overview"]["auto_extracted_text"] ?? ($data["data"]["info"]["description"]["LONG_NONE"] ?? "");
        $starAvg     = $data["data"]["star_rating"]["averageRating"] ?? 0;
        try{
            $saleProductObj->star_avg_rating = (float)$starAvg;
            $saleProductObj->description     = $description;
            $saleProductObj->save();
        }
        catch(Exception $e){
            \Illuminate\Support\Facades\Log::error(__METHOD__ . " - Error: " . $e->getMessage());
        }
    }
    $resultText = generateAiSaleDescription($saleProductObj->product_name, $description, $saleProductObj->dis_percent, $saleProductObj->discount_price);
    return ensureWhitespaceBeforeEuro($resultText);
}

function getDiscountDescription(int $percent): string{
    $discounts = [
        100 => "Komplett kostenlos",
        90  => "Extrem reduzierter Preis",
        80  => "Sehr stark reduzierter Preis",
        60  => "Stark reduzierter Preis",
        50  => "Genau der halbe Preis",
        40  => "Deutlich reduzierter Preis",
        20  => "Moderat reduzierter Preis",
        1   => "Klein reduzierter Preis",
        0   => "Regulärer Vollpreis"
    ];

    // Find the nearest discount level
    foreach($discounts as $threshold => $description){
        if($percent >= $threshold){
            return $description;
        }
    }

    return "Kein Rabatt";
}

function isJson($string){
    json_decode($string);
    return json_last_error() === JSON_ERROR_NONE;
}

function generateAiSaleDescription($productName = "", $productDescription = "", $discountPercent = "", $discountPrice = "", $model = "anthropic/claude-sonnet-4"){
    $discountStr = getDiscountDescription(@(int)$discountPercent);
    \Illuminate\Support\Facades\Log::info(__METHOD__ . " - Start - Product Name: $productName, Product Description: $productDescription, Discount Percent: $discountPercent, Discount Price: $discountPrice, Discount Str: $discountStr, Model: $model");
    $startTime = microtime(true);

    $oldDiscountText    = 'Rabatt: Aktuell um ' . $discountPercent . '% reduziert!';
    $newDiscountText    = 'Rabatt-Preis: ' . $discountPrice;
    $addDiscountPercent = 0;
    try{
        $addDiscountPercent = fS("ai_gen.add_discount_percent", 0);
    }
    catch(Exception $e){
        \Illuminate\Support\Facades\Log::error(__METHOD__ . " - Error: " . $e->getMessage());
    }
    $discountText = $addDiscountPercent ? $oldDiscountText : $newDiscountText;

    App\_modules\OpenrouterApi\AiInferenceExplanationStore::storeExplanation(
        "sale",
        "create_tweet",
        "Generating a gamer-friendly promotional tweet for a product with discount."
    );

    $result = AiInferenceExecutor::execute(
        '
        Aufgabe:
        Erstelle einen ansprechenden, lockeren und gamer-freundlichen Tweet, der einen aktuellen Sale für ein PlayStation-Produkt bewirbt. Der Tweet sollte speziell auf PlayStation-Gamer zugeschnitten sein und darauf abzielen, hohe Interaktion (Likes, Retweets, Kommentare) zu generieren.

        Produktdetails:
        Name des Produkts: [product_name]
        Beschreibung des Produkts: [product_description]
        Rabatt-Preis: [discount_price]
        [discount_text]

        Richtlinien für den Tweet:
        Sprache & Ton:
        Locker, cool und direkt an die Gaming-Community gerichtet.
        Gaming-Insider, Memes oder Trends können verwendet werden, wenn sie absolut relevant sind.
        Vermeide einen zu werblichen Ton – Authentizität ist entscheidend.
        Schreibe immer in der Ihr-Form anstelle von Du-Form.

        Formatierung:
        Halte den Text prägnant: Ideal sind weniger als 70 Zeichen (wenn möglich).
        Begrenze die Emoji-Anzahl auf maximal 2, um den Fokus zu bewahren.
        Vermeide übermäßige Links (maximal 1-2 URLs).
        Nutze max. 3 Erwähnungen oder Hashtags, um Lesbarkeit und Engagement zu steigern.

        Emotionale Wirkung:
        Zielt auf "Freude", "Überraschung" oder "Erwartung" ab, um den Sale spannend wirken zu lassen.
        Verwende eine direkte, ansprechende Ansprache, die Begeisterung weckt.

        Ziel:
        Betone den Sale-Vorteil klar und eindringlich.
        Der Fokus liegt darauf, Gamer zu motivieren, sofort zu handeln, ohne dabei aufdringlich zu wirken.

        Optimierung durch Daten:
        Wörter: Weniger als 28 für starke Performance.
        Charaktere: Zwischen 70-140 Zeichen, um die Engagement-Rate zu maximieren.
        Tonebene: Vermeide Ironie oder Verwirrung – Klarheit vor Humor.

        Heutiges Datum:
        [date]

        WICHTIG: Antworte ausschließlich mit dem generierten Tweet-Text weil dieser direkt automatisch verwendet wird!
        ',
        [
            'product_name'        => $productName,
            'product_description' => $productDescription,
            'discount_price'      => $discountPrice,
            'discount_text'       => $discountText,
            "date"                => date("d.m.Y"),
        ],
        "sale",
        "create_tweet",
        $model,
    );

    \Illuminate\Support\Facades\Log::info(__METHOD__ . " - End - Time: " . (microtime(true) - $startTime) . "s - Result: " . json_encode($result));
    return $result;
}

function generateSaleImage($store_sale, $sale_product, $sale_image_template, $type, $is_preview = false, $fixFileName = false){

    $assets = [];

    if($sale_image_template && $sale_image_template->template_type == "background" && $sale_product){

        $game_image  = $sale_product ? $sale_product->image : public_path("gfx/sale_game_image.png");
        $dis_percent = $sale_product ? '-' . numberFormat($sale_product->dis_percent) . '%' : '-75%';

        if($type == "story"){

            $bg_image             = public_path("uploads/" . $sale_image_template->story_image);
            $game_image_position  = $sale_image_template->story_game_image_position ? json_decode($sale_image_template->story_game_image_position) : [];
            $discount_bg_position = $sale_image_template->story_discount_bg_position ? json_decode($sale_image_template->story_discount_bg_position) : [];
        }
        else{

            $bg_image             = public_path("uploads/" . $sale_image_template->image);
            $game_image_position  = $sale_image_template->game_image_position ? json_decode($sale_image_template->game_image_position) : [];
            $discount_bg_position = $sale_image_template->discount_bg_position ? json_decode($sale_image_template->discount_bg_position) : [];
        }

        $image_types = [
            'image-template' => [
                'source'               => $bg_image,
                'game_image'           => $game_image,
                'game_image_position'  =>
                    ['x' => $game_image_position->x, 'y' => $game_image_position->y, 'w' => $game_image_position->w, 'h' => $game_image_position->h],
                'discount_bg_image'    => public_path("gfx/discount-bg.png"),
                'discount_bg_position' =>
                    ['x' => $discount_bg_position->x, 'y' => $discount_bg_position->y, 'a' => $discount_bg_position->a, 'fs' => $discount_bg_position->fs, 'w' => $discount_bg_position->w, 'h' => $discount_bg_position->h],
                'discount_text'        => $dis_percent,
            ]
        ];

        $discountFont = public_path("fonts/PSMontCaps-HeavyItalic.ttf");
        $blackColor   = ['r' => 0, 'g' => 0, 'b' => 0];

        foreach($image_types as $image_type => $img){

            //Template Image
            $main_image = new SimpleImageCC($img['source']);

            //game_image
            $game_image_wm = new SimpleImageCC($img['game_image']);
            $game_image_wm->resize($img['game_image_position']['w'], $img['game_image_position']['h']);
            $main_image->create_watermark($game_image_wm, 100, $img['game_image_position']['y'], $img['game_image_position']['x']);

            //discount_bg_image
            $discount_bg_image_wm = new SimpleImageCC($img['discount_bg_image']);
            $discount_bg_image_wm->resize($img['discount_bg_position']['w'], $img['discount_bg_position']['h']);

            $main_image->create_watermark($discount_bg_image_wm, 100, $img['discount_bg_position']['y'], $img['discount_bg_position']['x']);

            $text_x = $img['discount_bg_position']['x'] + (round($img['discount_bg_position']['w'] / 2));
            $text_y = $img['discount_bg_position']['y'] + (round($img['discount_bg_position']['h'] / 2)) - (round($img['discount_bg_position']['fs'] / 1.5));

            $main_image->text($img['discount_text'], $discountFont, $img['discount_bg_position']['fs'], $text_x, $text_y, $img['discount_bg_position']['a'], $blackColor, 'center');

            if($fixFileName !== false)
                $filename = $fixFileName;
            else
                $filename = $image_type . "_" . $sale_product->id . "_" . mt_rand(1, 9999) . ".png";

            if($is_preview){

                header('Content-type: image/png');
                imagepng($main_image->image);
                imagedestroy($main_image->image);
                exit();
            }
            else{

                $destImg = public_path("uploads/" . $filename);
                $main_image->save($destImg);

                $assets[$image_type] = url("public/uploads/" . $filename);
            }
        }
    }
    else if(($store_sale || ($sale_image_template && $sale_image_template->template_type == "overlay")) && $sale_product){

        $image_type  = 'image-template';
        $dis_percent = $sale_product ? '-' . numberFormat($sale_product->dis_percent) : '-15';

        if($type == "story"){

            $bg_image   = public_path("gfx/story-transparent-bg.png");
            $game_image = $sale_product ? $sale_product->portrait_banner : public_path("gfx/game_portrait_banner.jpg");

            if($sale_image_template && $sale_image_template->template_type == "overlay")
                $overlay_image = public_path("uploads/" . $sale_image_template->story_image);
            else
                $overlay_image = public_path("uploads/" . $store_sale->story_overlay_img);

            //$discount_position = ['fs' => 180, 'x' => 1035, 'y' => 1907, 'tw15' => 384];
        }
        else{

            $bg_image          = $sale_product ? $sale_product->edition_key_art : public_path("gfx/game_edition_key_art.jpg");
            $discount_position = ['fs' => 187, 'x' => 1528, 'y' => 833, 'tw15' => 347];

            if($sale_image_template && $sale_image_template->template_type == "overlay")
                $overlay_image = public_path("uploads/" . $sale_image_template->image);
            else
                $overlay_image = public_path("uploads/" . $store_sale->link_overlay_img);
        }

        //Background Image
        $main_image = new SimpleImageCC($bg_image);

        if($type == "story"){

            $game_image_wm = new SimpleImageCC($game_image);
            $game_image_wm->resizeToWidth(1080);
            $main_image->create_watermark($game_image_wm, 100, 0, 0);
        }
        else{

            $main_image->resizeToWidth(1920);
        }

        //overlay image
        $overlay_image_wm = new SimpleImageCC($overlay_image);
        $main_image->create_watermark($overlay_image_wm, 100, 0, 0);

        if($type != "story"){

            //discount text
            $discountFont = public_path("fonts/PSMontCaps-Bold.ttf");
            $whiteColor   = ['r' => 255, 'g' => 255, 'b' => 255];

            $text_w = $main_image->text_w($discount_position['fs'], 0, $discountFont, $dis_percent);
            $dis_x  = $discount_position['x'] + (($discount_position['tw15'] - $text_w[2]) / 2);
            $main_image->text($dis_percent, $discountFont, $discount_position['fs'], $dis_x, $discount_position['y'], 0, $whiteColor, 'center');
        }

        if($fixFileName !== false)
            $filename = $fixFileName;
        else
            $filename = $image_type . "_" . $sale_product->id . "_" . mt_rand(1, 9999) . ".png";

        if($is_preview){

            header('Content-type: image/png');
            imagepng($main_image->image);
            imagedestroy($main_image->image);
            exit();
        }
        else{

            $destImg = public_path("uploads/" . $filename);
            $main_image->save($destImg);

            $assets[$image_type] = url("public/uploads/" . $filename);
        }
    }

    return $assets;
}

function pressReleasePostType(){
    return ['link_post' => 'Link Post', 'gallery_post' => 'Gallery Post', 'video_post' => 'Video Post'];
}

function sonyReportingApiData(){

    return [
        'api_url' => (is_localhost()) ? "http://localhost/roland/sony_reporting/api/" : "https://sied05.contentcreators.at/api/",
        'token'   => 'e0chLtwvNbWCygA'
    ];
}

function getSonyReportingPost(){

    $api               = sonyReportingApiData();
    $postData['token'] = $api['token'];
    $api_url           = $api['api_url'] . 'get-monthly-best-performer-repost';
    return call_curl($api_url, "POST", $postData);
}

function replicatePostCopy($social_post_channel, $updatedData){

    $newPost = $social_post_channel->replicate();

    $newPost->created_at        = date('Y-m-d H:i:s', time());
    $newPost->status            = "Ready To Write";
    $newPost->is_posted         = 0;
    $newPost->is_error          = 0;
    $newPost->is_running        = 0;
    $newPost->is_sync           = 0;
    $newPost->clickup_task_id   = NULL;
    $newPost->task_id           = NULL;
    $newPost->error_text        = NULL;
    $newPost->channel_post_link = "";

    $old_medias = ($social_post_channel->medias) ? explode(',', $social_post_channel->medias) : [];

    foreach($updatedData as $key => $value){
        $newPost->{$key} = $value;
    }

    //here need to media copy
    $attachment = [];

    foreach($old_medias as $old_media){

        $uploadFile = copyFileToUploads($old_media, $newPost->channel, 'duplicate');

        if($uploadFile)
            array_push($attachment, $uploadFile);
    }

    $medias          = count($attachment) > 0 ? implode(',', $attachment) : NULL;
    $newPost->medias = $medias;
    //end

    $newPost->save();

    // first comment copy
    $old_comment = Comment::where('type', 'social_post')->where('type_id', $social_post_channel->id)->orderBy('id', 'asc')->first();

    if($old_comment){

        $new_comment          = $old_comment->replicate();
        $new_comment->type_id = $newPost->id;
        $new_comment->save();
    }

    return $newPost;
}

function copyFileToPressReleases($fileName, $channel, $type = ''){

    //copy files to uploads folder
    $tmpFileFullPath    = public_path('uploads/tmp') . DIRECTORY_SEPARATOR . $fileName;
    $uploadDestFullPath = public_path() . DIRECTORY_SEPARATOR . $fileName;

    if($type == 'duplicate' && file_exists($uploadDestFullPath)){

        $fileExt  = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        $fileName = mt_rand() . rand() . "_" . $channel . "." . $fileExt;

        //duplicate file to uploads directory
        $new_uploadDestFullPath = public_path('uploads') . DIRECTORY_SEPARATOR . $fileName;
        @File::copy($uploadDestFullPath, $new_uploadDestFullPath);
    }
    elseif(file_exists($tmpFileFullPath)){

        //copy to uploads directory
        @File::copy($tmpFileFullPath, $uploadDestFullPath);
    }

    return $fileName;
}

function validChannelVideoLink($link, $channel){

    if($channel == "youtube"){

        $pattern = '/https?:\/\/(?:www\.)?(?:youtube\.com|youtu\.be)\/[^\s"\'<>]+/i';
        preg_match_all($pattern, $link, $matches);
        return empty(array_filter($matches[0])) ? false : true;
    }
    else if($channel == "instagram"){

        $pattern = '/https?:\/\/(?:www\.|)(?:instagram\.com|ig\.me)\/[^\s"\'<>]+/i';
        preg_match_all($pattern, $link, $matches);
        return empty(array_filter($matches[0])) ? false : true;
    }
    else if($channel == "tiktok"){

        $pattern = '/https?:\/\/(?:www\.|m\.|vm\.|vt\.)?tiktok\.com\/[^\s"\'<>]+/i';
        preg_match_all($pattern, $link, $matches);
        return empty(array_filter($matches[0])) ? false : true;
    }

    return false;
}

function ensureWhitespaceBeforeEuro($text){
    if(!is_string($text)){
        return $text;
    }
    return preg_replace('/\s*€/u', ' €', $text);
}

function makeApifyRequest($video_link){

    if(validChannelVideoLink($video_link, "instagram")){

        $postData = json_encode([
            "addParentData"                     => true,
            "directUrls"                        => [$video_link],
            "enhanceUserSearchWithFacebookPage" => false,
            "isUserReelFeedURL"                 => false,
            "isUserTaggedFeedURL"               => false,
            "resultsLimit"                      => 1,
            "resultsType"                       => "details",
            "searchLimit"                       => 1,
            "searchType"                        => "hashtag"
        ]);

        $api_path    = "acts/apify~instagram-scraper/runs";
        $api_headers = ["Content-Type: application/json"];
        $apify_resp  = apify_call($api_path, "POST", $postData, $api_headers);

        $apify_id = $apify_resp && isset($apify_resp['data']['id']) ? $apify_resp['data']['id'] : '-';
    }
    else if(validChannelVideoLink($video_link, "tiktok")){

        $postData = json_encode([
            "postURLs"                      => [$video_link],
            "shouldDownloadCovers"          => false,
            "shouldDownloadSlideshowImages" => false,
            "shouldDownloadSubtitles"       => false,
            "shouldDownloadVideos"          => true,
        ]);

        $api_path    = "acts/clockworks~tiktok-video-scraper/runs";
        $api_headers = ["Content-Type: application/json"];
        $apify_resp  = apify_call($api_path, "POST", $postData, $api_headers);

        $apify_id = $apify_resp && isset($apify_resp['data']['id']) ? $apify_resp['data']['id'] : '-';
    }
    else{

        $apify_id = '-';
    }

    return $apify_id;
}

function apifyPostMedias($video_link, $apifyPost, $channel = false, $targetPublicFolder = 'uploads'){

    $postId    = "";
    $mediaUrls = [];

    if(!$channel){

        if(validChannelVideoLink($video_link, "instagram"))
            $channel = "instagram";
        else if(validChannelVideoLink($video_link, "youtube"))
            $channel = "youtube";
        else if(validChannelVideoLink($video_link, "tiktok"))
            $channel = "tiktok";
    }

    if($channel == "instagram"){

        $postId = getInstagramPostID($video_link);

        if(@$apifyPost['type'] == "Image"){

            $filename             = $apifyPost['id'] . ".jpg";
            $mediaUrls[$filename] = $apifyPost['displayUrl'];
        }
        else if(@$apifyPost['type'] == "Video"){

            $filename             = $apifyPost['id'] . ".mp4";
            $mediaUrls[$filename] = $apifyPost['videoUrl'];
        }
        else if(@$apifyPost['type'] == "Sidecar"){

            foreach($apifyPost['childPosts'] as $childPost){

                if($childPost['type'] == "Image"){

                    $filename             = $childPost['id'] . ".jpg";
                    $mediaUrls[$filename] = $childPost['displayUrl'];
                }
                else if($childPost['type'] == "Video"){

                    $filename             = $childPost['id'] . ".mp4";
                    $mediaUrls[$filename] = $childPost['videoUrl'];
                }
            }
        }
    }
    else if($channel == "tiktok"){

        $postId = $apifyPost['id'];

        if(isset($apifyPost['isSlideshow']) && $apifyPost['isSlideshow']){

            foreach($apifyPost['slideshowImageLinks'] as $ti => $tLinks){

                $ext = pathinfo($tLinks['downloadLink'], PATHINFO_EXTENSION);
                if($ext){
                    $extParts = explode("?", $ext);
                    $ext      = $extParts[0];
                }

                if(!in_array($ext, ['png', 'jpg', 'jpeg', 'gif']))
                    $ext = "jpg";

                $filename             = "slide-" . ($ti + 1) . "." . $ext;
                $mediaUrls[$filename] = $tLinks['downloadLink'];
            }
        }
        else if(isset($apifyPost['videoMeta'])){

            $filename             = "video.mp4";
            $mediaUrls[$filename] = $apifyPost['videoMeta']['downloadAddr'];
        }
    }

    //save medias
    $mi     = 1;
    $medias = [];

    foreach($mediaUrls as $mediaName => $mediaUrl){

        try {

            $new_file_name = $postId . "-" . $mi . "-" . $mediaName;
            $upload_path   = public_path($targetPublicFolder . "/" . $new_file_name);
            @File::copy($mediaUrl, $upload_path);

            if(file_exists($upload_path))  {
                $medias[] = basename($upload_path);
                $mi++;
            }
        }
        catch(Exception $e)  {  }
    }

    return $medias;
}

function ccDownloadYTVideo($video_url, $dest_path){

    $yt_id = get_youtube_id($video_url);
    //$ytDlpPath   = base_path("bin/yt-dlp"); // Within host /usr/local/bin/yt-dlp instead
    $target_path = rtrim(public_path($dest_path), "/\\");

    $command = 'yt-dlp -S "res:1080" --recode-video mp4 -P "' . $target_path . '" -o "' . $yt_id . '.mp4" "ytsearch:\'' . $video_url . '\'"';  //runuser -u dev09 --
    $result  = shell_exec($command);

    $output_video = $target_path . "/" . $yt_id . ".mp4";

    if(file_exists($output_video) && filesize($output_video) > 0){
        return basename($output_video);
    }

    \Illuminate\Support\Facades\Log::error(__METHOD__ . " - Error: " . ($result ?? ""));

    return false;
}

function cc_facebook_url_debugger($short_url){

    $returnArr = [];

    if($short_url){

        try{

            $fbApi = new Facebook([
                'app_id'                => fS("facebook.app_id", '829173042189246'),
                'app_secret'            => fS("facebook.app_secret", '********************************'),
                'default_graph_version' => fS("facebook.app_graph_version", 'v19.0'),
            ]);

            $facebook_access_token = fS("facebook.access_token");
            $fbApi->setDefaultAccessToken($facebook_access_token);

            $params = [
                "id"     => $short_url,
                "scrape" => 1
            ];
            $resp   = $fbApi->post("", $params)->getDecodedBody();

            $returnArr = array("success" => 1);
        }
        catch(FacebookResponseException $e){
            // When Graph returns an error
            if($e->getMessage() == "Invalid parameter"){
                $returnArr = array("success" => 1);
            }
            else{
                $returnArr = array("success" => 0, "message" => $e->getMessage());
            }
        }
        catch(FacebookSDKException $e){
            // When validation fails or other local issues
            $returnArr = array("success" => 0, "message" => $e->getMessage());
        }
        catch(Exception $e){
            $returnArr = array("success" => 0, "message" => $e->getMessage());
        }
    }

    return $returnArr;
}

function ccResizeToWidth($media, $new_width){

    if(file_exists($media)){

        $simlpeImg = new SimpleImageCC();
        $simlpeImg->load($media);

        $orig_width = $simlpeImg->getWidth();
        if($orig_width > $new_width){

            $simlpeImg->resizeToWidth($new_width);
            $simlpeImg->save($media, false, 100);
            clearstatcache(false, $media);
        }
    }
}

function getBestMediaForChannel($mediaFiles, $platform, $type){

    $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    $videoExtensions = ['mp4', 'mov'];

    $storyImages = [];
    $linkImages  = [];
    $allImages   = [];
    $videos      = [];

    foreach($mediaFiles as $file){

        $full_file = public_path() . '/' . $file;
        $extension = pathinfo($file, PATHINFO_EXTENSION);

        if(in_array($extension, $imageExtensions)){

            list($width, $height) = getimagesize($full_file);

            $ratio = $width / $height;

            if($width >= 1080 && $height >= 1920)
                $storyImages[] = $file; // Vertical image (preferred for Instagram Stories)
            else if($ratio > 1.4 && $height > 150)
                $linkImages[] = $file; // horizontal or square image

            $allImages[] = $file; // Store all images as backup
        }
        elseif(in_array($extension, $videoExtensions)){

            $videos[] = $file;
        }
    }

    // Instagram Story prefers vertical images
    if($platform === 'stories'){
        if(empty($storyImages))
            return [];
        else
            return array_slice($storyImages, 0, 3);
    }

    // Link post prefres horizontal image
    if($type === 'link_post' && !empty($linkImages)){
        shuffle($linkImages);
        return $linkImages[array_rand($linkImages)];
    }

    if($type === 'gallery_post'){
        shuffle($allImages);
        return array_slice($allImages, 0, 3);
    }

    // General selection based on type
    if($type === 'image' && !empty($allImages)){
        shuffle($allImages);
        return $allImages[array_rand($allImages)];
    }
    elseif($type === 'video'){

        if(!empty($videos))
            return $videos[array_rand($videos)];

        return "";
    }

    // Fallback: If the requested type isn't available, return whatever exists
    return $allImages[array_rand($allImages)];
}

function trimEveryLine($text){
    $lines        = explode("\n", $text);
    $trimmedLines = array_map('trim', $lines);
    $trimmedText  = implode("\n", $trimmedLines);
    try{
        $jsonPattern = '/\{(?:[^{}]|(?R))*\}|\[(?:[^\[\]]|(?R))*\]/';
        $matches     = [];
        preg_match_all($jsonPattern, $trimmedText, $matches);
        foreach($matches[0] as $jsonString){
            $json = json_decode($jsonString, true);
            if(json_last_error() === JSON_ERROR_NONE){
                $prettyJson  = json_encode($json, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
                $trimmedText = str_replace($jsonString, $prettyJson, $trimmedText);
            }
        }
    }
    catch(Exception $e){
        return $text;
    }
    return $trimmedText;
}

function convertLocalImagePathToUrl($localImagePath){
    $basePath            = base_path();
    $normalizedBasePath  = str_replace('\\', '/', $basePath);
    $normalizedImagePath = str_replace('\\', '/', $localImagePath);
    $relativeImagePath   = str_replace($normalizedBasePath, '', $normalizedImagePath);
    if($relativeImagePath[0] !== '/'){
        $relativeImagePath = '/' . $relativeImagePath;
    }
    return url($relativeImagePath);
}

function sortAiPromptTextSection($tabLabel, $sectionLabel, $sectionFields){
    $nonPromptMap = [];
    $promptMap    = [];
    foreach($sectionFields as $fieldLabel => $fieldVal){
        if(AiInference::getLatestInference($sectionLabel, $fieldLabel)){
            $promptMap[$fieldLabel] = $fieldVal;
        }
        else{
            $nonPromptMap[$fieldLabel] = $fieldVal;
        }
    }
    $sectionFields = [];
    foreach($promptMap as $fieldLabel => $fieldVal){
        $sectionFields[$fieldLabel] = $fieldVal;
    }
    foreach($nonPromptMap as $fieldLabel => $fieldVal){
        $sectionFields[$fieldLabel] = $fieldVal;
    }
    return [$sectionFields, $promptMap, $nonPromptMap];
}

function getPromptText($prompt = "", $keyGroup = "", $key = "", $data = []){
    $textKey = "ai_prompt." . $keyGroup . "." . $key;
    $textKey = str_replace(array("_image", "_video", "_webm"), array("img", "vid", "wb"), $textKey);
    return fT($textKey, $prompt, $data);
}

function getRandomTextByChannel($data, $channel, $blog_id){
    // Filter posts with is_used = 0
    $availablePosts = array_filter($data->{$channel}, function ($post){
        return $post->is_used == "0";
    });

    // If all posts are used, reset all to 0 or generate new ones
    if(empty($availablePosts)){
        $blog_promotion = BlogPromotion::where('id', $blog_id)->first();

        if($blog_promotion){
            for($i = 0; $i < 4; $i++){
                $textData['text']    = BlogPromotionCore::createSocialPost($blog_promotion->ai_summary, $channel);
                $textData['is_used'] = '0';
                $data->{$channel}[]  = (object)$textData; // Ensure object format
            }

            $availablePosts = array_filter($data->{$channel}, function ($post){
                return $post->is_used == "0";
            });
        }
        else{
            foreach($data->{$channel} as &$post){
                $post->is_used = "0";
            }
            // Refresh availablePosts after resetting
            $availablePosts = $data->{$channel};
        }
    }

    // Pick a random post
    $randomKey    = array_rand($availablePosts);
    $selectedPost = $availablePosts[$randomKey];

    // Update is_used flag to 1
    foreach($data->{$channel} as &$post){
        if($post->text === $selectedPost->text){
            $post->is_used = "1";
            break;
        }
    }

    return ['text' => $selectedPost->text, 'data' => $data];
}

function isStoryMedia($blog_media){

    $full_file = public_path() . '/' . $blog_media;

    list($width, $height) = getimagesize($full_file);

    if($width >= 1080 && $height >= 1920)
        return true;

    return false;
}

function getZoomRatio($blog_media){

    $full_file = public_path() . '/' . $blog_media;
    list($imageWidth, $imageHeight) = getimagesize($full_file);

    // Define max dimensions for the div (for example, responsive container size)
    $maxDivWidth  = 800;
    $maxDivHeight = 800;

    // Calculate aspect ratio
    $aspectRatio = $imageWidth / $imageHeight;

    // Calculate div dimensions while maintaining aspect ratio
    if($imageWidth > $maxDivWidth || $imageHeight > $maxDivHeight){
        if($imageWidth / $maxDivWidth > $imageHeight / $maxDivHeight){
            $divWidth  = $maxDivWidth;
            $divHeight = $maxDivWidth / $aspectRatio;
        }
        else{
            $divHeight = $maxDivHeight;
            $divWidth  = $maxDivHeight * $aspectRatio;
        }
    }
    else{
        // If the image is smaller than maxDiv, keep original size
        $divWidth  = $imageWidth;
        $divHeight = $imageHeight;
    }

    return ['divWidth' => $divWidth, 'divHeight' => $divHeight];
}

function blogChannels(){

    return ['twitter', 'threads', 'facebook', 'stories'];
}

function getFBViewClass($images, $module = ''){

    $public_path = public_path();
    if($module == 'social_planning'){
        $public_path = public_path('uploads');
    }

    $total_image      = count($images);
    $class_name       = '';
    $first_class_name = '';
    if($total_image > 0){

        $first_image = $images[0];

        $full_file = $public_path . '/' . $first_image;
        $extension = pathinfo($first_image, PATHINFO_EXTENSION);

        $imageWidth = $imageHeight = 0;
        if($extension == "mov" || $extension == "mp4"){
            $video_data = cc_read_video_metadata($full_file);
            if($video_data){
                $imageWidth  = $video_data['metadata']['width'];
                $imageHeight = $video_data['metadata']['height'];
            }
        }
        else{
            list($imageWidth, $imageHeight) = getimagesize($full_file);
        }

        if($imageWidth >= $imageHeight){
            $class_name       = 'horizontal';
            $first_class_name = 'horizontal';
        }
        else{
            $class_name       = 'vertical';
            $first_class_name = 'vertical';
        }

        $img_count = $total_image;
        if($total_image > 5)
            $img_count = 5;

        $class_name .= '-' . $img_count;

        if($total_image == 3){
            $horizontal_count = $vertical_count = 0;
            foreach($images as $image){
                $full_file = $public_path . '/' . $image;
                list($imageWidth, $imageHeight) = getimagesize($full_file);

                if($imageWidth >= $imageHeight)
                    $horizontal_count++;
                else
                    $vertical_count++;
            }

            if($first_class_name == 'horizontal')
                $class_name .= '-' . $horizontal_count . '-' . $first_class_name;
            else
                $class_name .= '-' . $vertical_count . '-' . $first_class_name;
        }
    }

    return $class_name;
}

function sanitizeAndResizeImage($tmpFileFullPath, $uploadDestFullPath, $resizeToWidth = false){

    if(file_exists($tmpFileFullPath)){

        // Sanitize uploaded image file
        $img_binary       = @file_get_contents($tmpFileFullPath);
        $f                = finfo_open();
        $mime_type        = finfo_buffer($f, $img_binary, FILEINFO_MIME_TYPE);
        $fileExt          = pathinfo($tmpFileFullPath, PATHINFO_EXTENSION);
        $valid_mime_types = array("image/jpeg", "image/jpg", "image/png", "image/webp", "image/avif");
        $valid_exts       = array("jpeg", "jpg", "png", "webp", "avif");

        if(@in_array($mime_type, $valid_mime_types) || (in_array(strtolower($fileExt), $valid_exts) && $mime_type == "application/octet-stream")){

            // Only process supported mime types

            try{

                // Create new SimpleImage instance from binary string
                $si_orig = new SimpleImageCC(imagecreatefromstring($img_binary));

                // Keep image alpha
                imagealphablending($si_orig->get_loaded(), false);
                imagesavealpha($si_orig->get_loaded(), true);

                /*
                if($resizeToWidth !== false && $si_orig->getWidth() > $resizeToWidth){
                    $si_orig->resizeToWidth($resizeToWidth);
                }
                */

                if(strtolower($fileExt) == "heic"){

                    $replFileExt        = ".jpg";
                    $uploadDestFullPath = str_replace("." . $fileExt, $replFileExt, $uploadDestFullPath);
                }

                // Save cleared image file
                $si_orig->save($uploadDestFullPath, false, 100);

                if($tmpFileFullPath != $uploadDestFullPath){
                    @File::delete($tmpFileFullPath);
                }

                //thumb image generate
                $pathInfo = pathinfo($uploadDestFullPath);
                $thumbnailPath = $pathInfo['dirname'] . DIRECTORY_SEPARATOR . 'thumb_' . $pathInfo['basename'];
                $si_thumb = new SimpleImageCC();
                $si_thumb->load($uploadDestFullPath);
                $si_thumb->resizeToWidth(250);
                $si_thumb->save($thumbnailPath, false, 100);

            }
            catch(Exception $e){

                if($tmpFileFullPath != $uploadDestFullPath)
                    @File::copy($tmpFileFullPath, $uploadDestFullPath);
            }
        }
        else{

            if($tmpFileFullPath != $uploadDestFullPath)
                @File::copy($tmpFileFullPath, $uploadDestFullPath);
        }
    }

    return $uploadDestFullPath;
}

function displayThumbImage($slider_image)  {

    // Remove 'public/' from the URL to get the relative path
    $image_path = str_replace(url('public/'), '', $slider_image);

    // Extract folder and filename
    $path_parts = pathinfo($image_path);
    $folder = $path_parts['dirname'];
    $filename = $path_parts['basename'];

    // Construct the thumbnail path
    $thumb_path = public_path("{$folder}/thumb_{$filename}");

    // Check if the thumbnail exists
    if (file_exists($thumb_path)) {
        return url("public/{$folder}/thumb_{$filename}");
    }

    // Return the original image if the thumbnail doesn't exist
    return $slider_image;
}

function getTwitterAccessTokenFromRefreshToken($refresh_token, $cons_key, $cons_sec) {

    $token_url = "https://api.twitter.com/2/oauth2/token";
    $authorization = base64_encode("$cons_key:$cons_sec");
    $header = array("Authorization: Basic {$authorization}","Content-Type: application/x-www-form-urlencoded");
    $content = "grant_type=refresh_token&client_id=$cons_key&refresh_token=".$refresh_token;

    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => $token_url,
        CURLOPT_HTTPHEADER => $header,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $content
    ));
    $response = curl_exec($curl);
    curl_close($curl);

    if ($response === false) {
        $response = json_encode(['error' => curl_error($curl)]);
    }

    return json_decode($response);
}

function getRolandUser()  {

    $email = is_localhost() ? '<EMAIL>' : '<EMAIL>';

    $user = GoogleUser::selectRaw("*" . AESdecypts(GoogleUser::encryptableFields()))
                ->whereRaw(AESdecypt('email', false) . " = ?", array($email))
                ->first();

    return $user;
}

function getMainLlmModel()  {

    $model = fS('llm.main_model', 'gpt-4o');
    if($model == 'gpt-4o'){
        $model = 'gpt-4o-mini';
    }
    else if($model == 'gpt-3.5-turbo'){
        $model = 'gpt-3.5-turbo-16k';
    }

    return $model;
}
