<?php

namespace App\Providers;

use App\Extensions\CustomDatabaseSessionHandler;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register(){
        
        // Change database session handler
        $this->app["session"]->extend("database", function ($app){
            $table        = config("session.table");
            $lifetime     = config("session.lifetime");
            $connection   = config("session.connection");
            $dbConnection = app()->make('db')->connection($connection);
            return new CustomDatabaseSessionHandler($dbConnection, $table, $lifetime, app());
        });

        // Support for older mysql server versions
        Schema::defaultStringLength(191);

        // Log all database queries while in dev environment
        if(($_SERVER["SERVER_NAME"] ?? "") === "localhost" && devModeActive())
            DB::listen(function ($query){
                Log::channel("mysql")->info(
                    sprintf("SQL: %s", $query->sql), ["bindings" => $query->bindings, "time" => $query->time]
                );
            });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot(){
        
		$domainWhitelist = [
			"ps.playstation.com",
		];
		$serverName      = $_SERVER["SERVER_NAME"] ?? "";
		if($serverName === "localhost") $domainWhitelist[] = "localhost";
		Request::setTrustedHosts($domainWhitelist);
		
		if(@$_SERVER['SERVER_NAME'] === null) return;
		if(!in_array(@$_SERVER['SERVER_NAME'], $domainWhitelist) ||
			!in_array(@$_SERVER['HTTP_HOST'], $domainWhitelist)){
			@http_response_code(404);
			exit();
		}
    }
}
