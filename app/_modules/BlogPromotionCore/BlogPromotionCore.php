<?php

namespace App\_modules\BlogPromotionCore;

use App\_modules\OpenrouterApi\AiInferenceExecutor;
use App\_modules\OpenrouterApi\AiInferenceExplanationStore;
use App\_modules\ScraperApi\ScraperWebRequest;
use DOMDocument;
use DOMXPath;
use Exception;
use Illuminate\Support\Facades\Log;
use RuntimeException;

class BlogPromotionCore{
    public static function extractWholeTextFromWebUrl($url){
        try{
            $page_html = ScraperWebRequest::get($url);
        }
        catch(Exception $e){
            Log::error(__METHOD__ . " " . $e->getMessage() . ", Fallback to file_get_contents");
            $context   = stream_context_create(['http' => ['follow_location' => true, 'max_redirects' => 10]]);
            $page_html = file_get_contents($url, false, $context);
        }
        if(!$page_html){
            Log::error(__METHOD__ . " Failed to load HTML from URL: " . $url);
            return '';
        }

        $doc = new DOMDocument();
        @$doc->loadHTML($page_html);
        if(!$doc->documentElement){
            Log::error(__METHOD__ . " Failed to load HTML from URL: " . $url);
            return '';
        }

        $xpath = new DOMXPath($doc);
        $nodes = $xpath->query('//body//text()');
        $text  = '';
        foreach($nodes as $node){
            $text .= $node->nodeValue;
        }
        if(empty(trim($text))){
            Log::error(__METHOD__ . " No text text from URL: " . $url);
            return '';
        }
        return trim($text);
    }

    public static function downloadAllImagesFromWebUrl($url, $saveDirectory){
        try{
            $doc = new DOMDocument();
            @$doc->loadHTML(ScraperWebRequest::get($url));
        }
        catch(Exception $e){
            Log::error(__METHOD__ . " " . $e->getMessage() . ", Fallback to file_get_contents");
            $context = stream_context_create(['http' => ['follow_location' => true, 'max_redirects' => 10]]);
            @$doc->loadHTML(file_get_contents($url, false, $context));
        }
        $xpath        = new DOMXPath($doc);
        $nodes        = $xpath->query('//img');
        $images       = [];
        $imagePattern = '/https?:\/\/.*\.(?:png|jpg|jpeg|gif|bmp|webp|svg)/i';
        foreach($nodes as $node){
            /** @var \DOMElement $node */
            $src   = $node->getAttribute('src');
            $class = $node->getAttribute('class');
            if(preg_match($imagePattern, $src) && strpos($class, "page-banner__takeover-img") === false){
                try{
                    $imageSize = getimagesize($src);
                    if($imageSize){
                        list($width, $height) = $imageSize;
                        if($height >= 550 || $width >= 900){
                            $images[] = $src;
                        }
                    }
                }
                catch(Exception $e){
                }
            }
        }

        if(!is_dir($saveDirectory) && !mkdir($saveDirectory, 0777, true) && !is_dir($saveDirectory)){
            throw new RuntimeException(sprintf('Directory "%s" was not created', $saveDirectory));
        }

        foreach($images as $imageUrl){
            $parsedUrl          = parse_url($imageUrl);
            $imagePath          = $parsedUrl['path'];
            $imageName          = basename($imagePath);
            $sanitizedImageName = self::sanitizeFileName($imageName);
            $savePath           = rtrim($saveDirectory, '/') . '/' . $sanitizedImageName;

            if(!file_exists($savePath)){
                try{
                    $imageData = ScraperWebRequest::downloadImage($imageUrl);
                }
                catch(Exception $e){
                    Log::error(__METHOD__ . " " . $e->getMessage() . ", Fallback to file_get_contents");
                    $imageData = @file_get_contents($imageUrl, false, $context);
                }
                if($imageData !== false && $imageData){
                    file_put_contents($savePath, $imageData);
                    sanitizeAndResizeImage($savePath, $savePath, 1920);
                }
            }
        }
        $savedImages = glob(rtrim($saveDirectory, '/') . '/*.{jpg,jpeg,png,gif,bmp,webp,svg}', GLOB_BRACE);
        return $savedImages;
        //return array_map('realpath', $savedImages);
    }

    private static function sanitizeFileName($fileName){
        $fileName = preg_replace('/[^a-zA-Z0-9-_\.]/', '_', $fileName);
        return substr($fileName, 0, 255);
    }

    public static function createAiSummary($pageText, $retries = 3){
        try{
            AiInferenceExplanationStore::storeExplanation(
                "blog",
                "page_summary",
                "Summary extraction of content from blog page text."
            );

            return AiInferenceExecutor::execute(
                "Extrahiere den vollständigen Inhalt des Page-Posts. Gib ausschließlich den extrahierten vollständigen Inhalt des Page-Posts als Antwort zurück: [page_text]",
                ["page_text" => $pageText],
                "blog",
                "page_summary",
                "anthropic/claude-sonnet-4",
                $retries
            );
        }
        catch(RuntimeException $e){
            Log::error(__METHOD__ . " " . $e->getMessage());
            throw new RuntimeException("Failed to create AI summary for page text: " . $pageText);
        }
    }

    public static function createAiPageTitle($pageText, $retries = 3){
        try{
            AiInferenceExplanationStore::storeExplanation(
                "blog",
                "page_title",
                "Extraction of title from blog page text."
            );

            return AiInferenceExecutor::execute(
                "Extrahieren Sie den Seitentitel. Geben Sie als Antwort nur den Titel der Auszugsseite zurück: [page_text]",
                ["page_text" => $pageText],
                "blog",
                "page_title",
                "",
                $retries,
            );
        }
        catch(RuntimeException $e){
            Log::error(__METHOD__ . " " . $e->getMessage());
            throw new RuntimeException("Failed to create AI summary for page title: " . $pageText);
        }
    }

    public static function createSocialPost($pageText, $postType = "twitter", $retries = 3){
        try{
            AiInferenceExplanationStore::storeExplanation(
                "blog",
                "create_social_post",
                "Generation of social media post based on blog content for posts."
            );

            return AiInferenceExecutor::execute(
                "Erstelle einen [post_type] Post basierend auf dem vollständigen Inhalt des Page-Posts.
                Gib ausschließlich den erstellten Social-Media-Post als Antwort zurück, Wichtige Richtlinien:
                - Maximale Länge pro Post: 200 Zeichen : [page_text]

                Heutiges Datum: [date]
                ",
                ["page_text" => $pageText, "post_type" => $postType, "date" => date("d.m.Y")],
                "blog",
                "create_social_post",
                "anthropic/claude-sonnet-4",
                $retries
            );
        }
        catch(RuntimeException $e){
            Log::error(__METHOD__ . " " . $e->getMessage());
            throw new RuntimeException("Failed to create social post for page text: " . $pageText);
        }
    }
}
