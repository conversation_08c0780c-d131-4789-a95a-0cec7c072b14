<?php

namespace App\_modules\TwitterAi\Utils;


use App\_modules\TwitterAi\Traits\AnalyticDatabaseDataTrait;
use Exception;
use JsonException;
use PDO;
use RuntimeException;

class AnalyticDatabase{
    use AnalyticDatabaseDataTrait;

    private PDO $pdo;

    /**
     * @param $dbPath
     *
     * @throws Exception
     */
    public function __construct($dbPath){
        $this->pdo = new PDO('sqlite:' . $dbPath);
    }

    /**
     * @return array
     * @throws JsonException
     */
    public function getFullTweetData(): array{
        $tweets        = $this->getAll("tweets");
        $tweetAnalysis = $this->getAll("tweet_analysis");
        $tweetMap      = [];
        foreach($tweets as $tweet){
            $tweetMap[$tweet['id']]['tweet'] = $tweet;
        }
        foreach($tweetAnalysis as $analysis){
            $data = json_decode($analysis["data"], true, 512, JSON_THROW_ON_ERROR);
            $data = $this->unpackLabelScoreData($data);
            $data = $this->unpackEntityScoreData($data);
            $data = $this->unpackEmojisData($data);
            $data = $this->unpackTagsData($data);
            $data = in_array($analysis["type"], ['topic', 'sentiment_2', 'irony', 'hate', 'offensive', 'emoji',
                                                 'emotion_2', 'sarcasm', 'humor']) ? $this->sortKeyValue($data) : $data;

            $tweetMap[$analysis['tweet_id']]["analytics"][$analysis["type"]] = $data;
        }
        return $tweetMap;
    }

    /**
     * @param string $tableName
     *
     * @return array
     */
    public function getAll(string $tableName = ""): array{
        return $this->query("SELECT * FROM $tableName");
    }

    /**
     * @param string $sql
     * @param array  $params
     *
     * @return array|false
     */
    public function query(string $sql, array $params = []): bool|array{
        $stmt = $this->pdo->prepare($sql);
        if($stmt === false){
            throw new RuntimeException("Failed to prepare statement: " . $this->pdo->errorInfo()[2]);
        }
        if(!$stmt->execute($params)){
            throw new RuntimeException("Failed to execute statement: " . $stmt->errorInfo()[2]);
        }
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
