<?php

namespace App\_modules\NintendoScraper\Models;

use Framework\src\Http\Traits\ManageableModelTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use JsonException;

/**
 * @property integer id
 * @property string  product_id
 * @property string  language_code
 * @property string  region_code
 * @property string  name
 * @property string  data_json
 *
 * @property string  last_scraped
 * @property string  created_at
 * @property string  updated_at
 *
 * @mixin Builder
 */
class NintendoProduct extends Model{
    use ManageableModelTrait {
        migrateIndices as migrateIndicesParent;
        migrateSchema as migrateSchemaParent;
    }

    protected static array $NON_ID_FIELDS = [
        "product_id"    => ["type" => "string", "length" => 256, "null" => true, "index" => true],
        "language_code" => ["type" => "string", "length" => 20, "null" => true, "index" => true],
        "region_code"   => ["type" => "string", "length" => 20, "null" => true, "index" => true],
        "name"          => ["type" => "string", "length" => 256, "null" => true, "index" => true], // Localized
        "data_json"     => ["type" => "json", "null" => true], // Contains complete dynamic data
        "last_scraped"  => ["type" => "datetime", "null" => true, "index" => true],
        // ----
        "created_at"    => ["type" => "created_at", "null" => true],
        "updated_at"    => ["type" => "updated_at", "null" => true],
    ];
    protected              $table         = 'nintendo_products';
    protected              $guarded       = [];

    /**
     * @param array $product
     *
     * @return void
     * @throws JsonException
     */
    public static function saveOrUpdateProduct(array $product): void{
        $nintendoProduct               = NintendoProduct::firstOrNew([
            "product_id"    => $product["id"],
            "region_code"   => $product["regionCode"],
            "language_code" => $product["languageCode"],
        ]);
        $nintendoProduct->name         = $product["name"];
        $nintendoProduct->data_json    = json_encode($product, JSON_THROW_ON_ERROR, 512);
        $nintendoProduct->last_scraped = now();
        $nintendoProduct->save();

        Log::info("id: " . $product["id"] . " name: " . $product["name"] . " regionCode: " .
            $product["regionCode"] . " languageCode: " . $product["languageCode"] . " data: " .
            substr($nintendoProduct->data_json, 0, 100) . "...");
    }
}
