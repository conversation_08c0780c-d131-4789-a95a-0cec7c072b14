<?php

namespace App\_modules\PressReleaseCore\Helpers;

use App\_modules\OpenrouterApi\AiInferenceExecutor;
use App\_modules\OpenrouterApi\AiInferenceExplanationStore;
use App\_modules\PressReleaseCore\Models\MailAttachment;
use App\_modules\PressReleaseCore\Models\PressMail;
use DOMDocument;
use Illuminate\Support\Facades\Log;
use Symfony\Component\DomCrawler\Crawler;

class EmailHtmlGenerator{
    public static function generatePressMailSummaryHtml(int $pressMailId): string{
        $pressMail = PressMail::find($pressMailId);
        if(!$pressMail){
            Log::error(__METHOD__ . " PressMail not found: " . $pressMailId);
            return '';
        }
        $pressMailInfos               = self::extractPressMailInfos($pressMail);
        $previousPressReleasesInfoStr = self::extractPreviousPressMailInfos($pressMail);
        $prompt                       = '
            Deine Aufgabe ist es eine sehr umfangreiche Zusammenfassung des Inhalts einer Pressemitteilung zu generieren.
            Die Zusammenfassung sollte komplett alle Informationen detailliert zum Produkt / Spiel der Pressemitteilung
            enthalten. Es ist zusätzlich auch wichtig, dass Informationen zu der Spiele Lore, sofern angegeben komplett
            enthalten sind. Stelle sicher, dass du above-the-fold zuerst dem User alle wichtigen Eckdaten der
            Pressemitteilung in einem übersichtlichen Element darstellst um ihn damit zu Unterstützen redaktionelle
            Entscheidungen zu treffen.

            Markiere prägnant wichtige Informationen mit <strong>, <em>, besonderes CSS, oder andere
            Designmittel. Das Ziel ist es die Informationen so zu präsentieren, dass sie für den User einfach zu lesen
            sind. Verwendete ein angenehmes Color-Schema (kein Rot oder Gelb) und Emojis (wo es Sinn macht um die
            Informationen besser zu repräsentieren). Generiere deine Zusammenfassung als neues übersichtliches HTML,
            welches direkt an einen User als Zusammenfassung E-Mail versendet werden kann.

            Die Zusammenfassung soll "alle" Informationen enthalten und auch alle Links / Urls / Verweise der E-Mail
            sauber für den User auflisten. Verwende nur inline CSS und keine externen CSS-Stylesheets, weil das HTML an
            den User via E-Mail versendet wird. Achte darauf auch CSS Klassen möglichst via prefixe Namen zu setzen,
            damit sie nicht mit anderen CSS Klassen von bestehenden E-Mail-Templates in Konflikt geraten. Achte auch
            darauf, dass das HTML eine maximale Breite / max-width von 800px hat, damit es in den meisten E-Mail-Clients
            gut aussieht. Verwende auch keine Attribute oder CSS Styling für das <html> Tag, weil der Inhalt automatisch
            extrahiert wird und sonst nicht korrekt angezeigt werden kann. Verwende auch keine cid: Attachments / <img>
            src="cid:..." Tags. Du kannst jedoch Youtube Video Thumbnails verwenden, wenn Youtube Links in der E-Mail
            enthalten sind ( Youtube-Thumbnail-Url-Format: https://img.youtube.com/vi/{YOUTUBE_VIDEO_ID}/hqdefault.jpg ).

            Heutiges Datum:
            [date]

            Hier ist die komplette E-Mail:
            [press_mail_infos]

            [previous_press_release_infos]
        ';

        AiInferenceExplanationStore::storeExplanation(
            "press",
            "press_mail_summary",
            "Generation of a detailed summary of the press release email content."
        );

        $response = AiInferenceExecutor::execute(
            $prompt, [
            "press_mail_infos"             => $pressMailInfos,
            "previous_press_release_infos" => $previousPressReleasesInfoStr,
            "date"                         => date("d.m.Y"),
        ],
            "press",
            "press_mail_notifier_html",
            getMainLlmModel(),
            3,
        );

        return self::cleanHtml($response);
    }

    public static function extractPressMailInfos($pressMail): string{
        $pressMailAttachments         = MailAttachment::where("press_mail_id", $pressMail->id)->get();
        $nonEmptyPressMailAttachments = $pressMailAttachments->filter(function ($attachment){
            return !empty($attachment->extracted_content);
        });
        $combinedContent              = "";
        if($nonEmptyPressMailAttachments->count()){
            $combinedContent = "Presse-Mitteilung E-Mail Anhänge:\n";
            foreach($nonEmptyPressMailAttachments as $attachment){
                $combinedContent  .= "--------------------\n";
                $combinedContent  .= "Datei-Anhang Name: " . basename($attachment->original_filename) . "\n";
                $filePathRelative = substr(str_replace(public_path(), "", $attachment->file_path), 1);
                if(file_exists(public_path($filePathRelative))){
                    $fileUrl         = url("public/" . $filePathRelative);
                    $combinedContent .= "Datei-URL: " . $fileUrl;
                    Log::info(__METHOD__ . " File URL: " . $fileUrl);
                }
                $combinedContent .= "Datei-Anhang extrahierte Informationen:\n";
                $combinedContent .= $attachment->extracted_content . "\n";
                $combinedContent .= "--------------------\n";
            }
            $combinedContent .= "\n\n";
        }
        $pressMailInfos = "\nPresse-Mitteilung Meta-Informationen:\n";
        $pressMailInfos .= "E-Mail ID: " . $pressMail->email_id . "\n";
        $pressMailInfos .= "E-Mail Datum: " . $pressMail->email_date . "\n";
        $pressMailInfos .= "Presse-Mitteilung Datum: " . $pressMail->press_release_date . "\n";
        $pressMailInfos .= "E-Mail Titel: " . $pressMail->email_title . "\n";
        $pressMailInfos .= "Produkt Titel: " . $pressMail->product_title . "\n";
        $pressMailInfos .= "Presse-Mitteilung Titel: " . $pressMail->press_release_title . "\n";
        $pressMailInfos .= "Haupt-Link: " . $pressMail->main_link . "\n";
        $pressMailInfos .= "Haupt-Link-Titel: " . $pressMail->main_link_cta_title . "\n\n";
        $pressMailInfos .= "Vollständiges E-Mail HTML:\n\n##---------- E-Mail HTML START ----------##\n" . $pressMail->html . "\n##---------- E-Mail HTML END ----------##\n\n";
        $pressMailInfos .= $combinedContent;
        return $pressMailInfos;
    }

    public static function extractPreviousPressMailInfos($pressMail): string{
        $previousPressMails = PressMail::where('id', '!=', $pressMail->id)
                                       ->where("product_title", $pressMail->product_title)
                                       ->orderBy('press_release_date', 'desc')
                                       ->get();
        if(!count($previousPressMails)){
            return '';
        }
        $previousPressReleasesInfoStr = "

                Info: Es gibt bereits vorherige Pressemitteilungen zu diesem Produkt. Diese Pressemitteilungen sind
                wichtig um den Kontext zu verstehen und um die Entwicklung des Produktes zu verfolgen. WICHTIG: Verwende
                diese Informationen über vorherige Presse-Mitteilungen NUR ergänzend für den Kontext, um ggf.
                Entwicklungen gegenüber vorherigen Presse-Mitteilungen besser dem User zu kommunizieren zu können.

                Hier sind die vorherigen Pressemitteilung-E-Mails welche vor dieser Pressemitteilung veröffentlicht
                worden sind:

            ";
        foreach($previousPressMails as $previousPressMail){
            $previousPressReleasesInfoStr .= "\n\n--------------------\n";
            $previousPressReleasesInfoStr .= self::extractPressMailInfos($previousPressMail);
            $previousPressReleasesInfoStr .= "\n--------------------\n";
        }
        return $previousPressReleasesInfoStr;
    }

    public static function cleanHtml(string $html): string{
        $dom        = new DOMDocument('1.0', 'UTF-8');
        $prevErrors = libxml_use_internal_errors(true);
        $dom->loadHTML('<?xml encoding="UTF-8"?><div id="wrapper">' . self::extractBodyContent($html) . '</div>',
            LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        libxml_use_internal_errors($prevErrors);
        if(!($wrapper = $dom->getElementById('wrapper'))){
            return '';
        }
        self::removeUnwantedTags($wrapper);
        $dom->encoding = 'UTF-8';
        $result        = '';
        foreach($wrapper->childNodes as $child){
            $result .= $dom->saveHTML($child);
        }
        $containerAttribute = "max-width: 800px; margin: 0 auto; padding: 20px; background-color: #f6f6f6;";
        return '<div class="email_main_content" style="' . $containerAttribute . '">' . $result . '</div>';
    }

    public static function extractBodyContent(string $html): string{
        if(empty($html)){
            return '';
        }
        $crawler    = new Crawler($html);
        $headStyles = '';
        $styles     = $crawler->filter('head style');
        foreach($styles as $style){
            $headStyles .= $style->ownerDocument->saveHTML($style);
        }
        $body = $crawler->filter('body');
        if($body->count() > 0){
            $bodyContent = '';
            foreach($body->children() as $child){
                $bodyContent .= $child->ownerDocument->saveHTML($child);
            }
            return $headStyles . $bodyContent;
        }
        return $headStyles . $crawler->html();
    }

    private static function removeUnwantedTags(\DOMNode $parent): void{
        if(!$parent->hasChildNodes()){
            return;
        }
        $nodesToRemove = [];
        foreach($parent->childNodes as $child){
            if($child->nodeType === XML_ELEMENT_NODE){
                if(in_array(strtolower($child->nodeName), ['html', 'head', 'body'])){
                    self::removeUnwantedTags($child);
                    while($child->firstChild){
                        $parent->insertBefore($child->firstChild, $child);
                    }
                    $nodesToRemove[] = $child;
                }
                else{
                    self::removeUnwantedTags($child);
                }
            }
        }
        foreach($nodesToRemove as $node){
            $parent->removeChild($node);
        }
    }
}
