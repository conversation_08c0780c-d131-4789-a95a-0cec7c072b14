<?php

namespace App\_modules\VideoLabeler\Models;

use Framework\src\Http\Traits\ManageableModelTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * @property integer id
 *
 * @property string  name
 *
 * @property string  created_at
 * @property string  updated_at
 *
 * @mixin Builder
 */
class VideoCategory extends Model{
    use ManageableModelTrait;

    protected $table   = 'video_categories';
    protected $guarded = [];

    protected static array $NON_ID_FIELDS = [
        "name"       => ["type" => "string", "length" => 255, "null" => true, "index" => true],
        // ----
        "created_at" => ["type" => "created_at", "null" => true],
        "updated_at" => ["type" => "updated_at", "null" => true],
    ];
}
