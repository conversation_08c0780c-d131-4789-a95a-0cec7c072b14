<?php

namespace App\_modules\PsStoreScraper\Scraper\DetailPageBatarangs;

use App\_modules\PsStoreScraper\Traits\BatarangTrait;
use Illuminate\Support\Arr;

class OverviewBatarang{
    use BatarangTrait;

    /**
     *
     */
    public function __construct(){
        $this->name = "overview";
    }

    /**
     * @param array $batarang
     *
     * @return array
     */
    public function parseBatarang(array $batarang = []): array{
        return [
            "auto_extracted_text" => Arr::get($batarang, "data"), // Jage deine AlbträumeEin einsamer Reisender. Eine ve...
        ];
    }

    /**
     * @param array $batarang
     *
     * @return array
     */
    public function getTranslationMap(array $batarang = []): array{
        return Arr::get($batarang, "data.translations", []) ?? [];
    }

    /**
     * @return array[]
     */
    public function getDataStructure(): array{ // Types -> Key, Array, array[... -> list placeholder], Text, Number, Null (means optional null)
        return [
            "auto_extracted_text" => "", // Jage deine AlbträumeEin einsamer Reisender. Eine ve...
        ];
    }
}
