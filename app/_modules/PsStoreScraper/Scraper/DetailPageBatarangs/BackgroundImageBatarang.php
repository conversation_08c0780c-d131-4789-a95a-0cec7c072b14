<?php

namespace App\_modules\PsStoreScraper\Scraper\DetailPageBatarangs;

use App\_modules\PsStoreScraper\Traits\BatarangTrait;
use Illuminate\Support\Arr;

class BackgroundImageBatarang{
    use BatarangTrait;

    /**
     *
     */
    public function __construct(){
        $this->name = "background-image";
    }

    /**
     * @param array $batarang
     *
     * @return array
     */
    public function parseBatarang(array $batarang = []): array{
        $data = [];

        $concepts = $this->getDataCacheByType($batarang, "Concept");
        foreach($concepts as $concept){
            $media = $concept["media"] ?? [];
            foreach($media as $imageEntry){
                $data[$imageEntry["role"]][] = $imageEntry["url"]; // BACKGROUND_LAYER_ART -> https://image.api.playstation.com/vulcan/ap/rnd/202010/2615/Jz1AzNTMqWsNEA2dWQyNVNRK.png
            }
        }

        $products = $this->getDataCacheByType($batarang, "Product");
        foreach($products as $product){
            $media = $product["media"] ?? [];
            foreach($media as $imageEntry){
                $data[$imageEntry["role"]][] = $imageEntry["url"]; // BACKGROUND_LAYER_ART -> https://image.api.playstation.com/vulcan/ap/rnd/202010/2615/Jz1AzNTMqWsNEA2dWQyNVNRK.png
            }
        }

        return $data;
    }

    /**
     * @param array $batarang
     *
     * @return array
     */
    public function getTranslationMap(array $batarang = []): array{
        return Arr::get($batarang, "data.translations", []) ?? [];
    }

    /**
     * @return array[]
     */
    public function getDataStructure(): array{ // Types -> Key, Array, array[... -> list placeholder], Text, Number, Null (means optional null)
        return [
            "..." => [],
        ];
    }
}
