<?
if(isset($_GET['token']) && $_GET['token'] == "update"):
	echo shell_exec('export LC_CTYPE="de_DE.UTF-8";2>&1 svn update ' . dirname(__FILE__) . ' --non-interactive --username cc_server --password X58RNGqJ9fCaKn5x');
	die;
endif;

@SQ::q("SET sql_mode = '';");

error_reporting(0);
ini_set('display_errors', 0);

//error_reporting(E_ALL);
//ini_set('display_errors', 1);

function uc_base($file_path, $underscore_replace = true){
	$base_name = basename($file_path, ".php");
	$base_name = $underscore_replace ? str_replace("_", " ", $base_name) : $base_name;
	return ucwords($base_name);
}

function ul_base($file_path, $underscore_replace = true){
	$base_name = basename($file_path, ".php");
	$base_name = $underscore_replace ? str_replace("_", " ", $base_name) : $base_name;
	return strtolower($base_name);
}

FW::require_class("nast_bezirk");
FW::require_class("nast_site_upload");
FW::require_class("nast_import_legacy");
FW::require_class("site_feiertage");
FW::require_class("nast_ausfaelle");
FW::require_class("nast_dtv");
FW::require_class("nast_dtv_jahre");
FW::require_class("nast_dtv_jahresausw");
FW::require_class("nast_monatsausw_text");
FW::require_class("nast_wetterdaten");
FW::require_class("nast_zaehldaten");
FW::require_class("nast_zaehlstellen");
FW::require_class("nast_chart_dispenser");

function get_background_by_call(){
	$module_map = array("default"               => "cycling-headers.jpg",
	                    "radverkehrszaehlungen" => "cycling-headers.jpg",
	                    "monatsauswertung"      => "cycling-headers.jpg",
	                    "zaehlstellenvergleich" => "cycling-headers.jpg",
	                    "jahresauswertung"      => "cycling-headers.jpg",
	                    "monatsuebersicht"      => "cycling-headers.jpg",
	                    "entwicklung"           => "cycling-headers.jpg",
	                    "weitere_informationen" => "cycling-headers.jpg",
	                    "haendische_zaehlungen" => "cycling-headers.jpg",
	                    "jahresberichte"        => "cycling-headers.jpg");

	return M($module_map, "default", "bg01.jpg");
}


function get_content_module_by_call(){
	$module_map = array("default"                => "site.radverkehrszaehlungen",
	                    "radverkehrszaehlungen"  => "site.radverkehrszaehlungen",
	                    "monatsauswertung"       => "site.monatsauswertung",
	                    "zaehlstellenvergleich"  => "site.zaehlstellenvergleich",
	                    "jahresauswertung"       => "site.jahresauswertung",
	                    "monatsuebersicht"       => "site.monatsuebersicht",
	                    "entwicklung"            => "site.entwicklung",
	                    "weitere_informationen"  => "site.weitere_informationen",
	                    "haendische_zaehlungen"  => "site.haendische_zaehlungen",
	                    "jahresberichte"         => "site.jahresberichte",
	                    "zaehlstellenuebersicht" => "site.zaehlstellenuebersicht",
	                    "aktuelle_entwicklungen" => "site.aktuelle_entwicklungen");

	$called_module = M($module_map, FW::call(0), M($module_map, "default", "dataset01"));
	return module::get($called_module);
}

function get_legend_module_by_call(){
	$module_map = array("default"               => "blank",
	                    "radverkehrszaehlungen" => "legend",
	                    "monatsauswertung"      => "legend",
	                    "zaehlstellenvergleich" => "legend",
	                    "jahresauswertung"      => "legend",
	                    "monatsuebersicht"      => "legend",
	                    "entwicklung"           => "legend",
	                    "weitere_informationen" => "blank",
	                    "haendische_zaehlungen" => "legend",
	                    "aktuelle_entwicklungen" => "legend",
	                    "jahresberichte"        => "legend");

	$called_module = M($module_map, FW::call(0), M($module_map, "default", "legend"));
	return module::get($called_module);
}

function ajax_change_monatsauswertung($M){
	FW::log(array("ajax_change_monatsauswertung" => $M));

	$data                        = array();
	$_SESSION["select_date"]     = $data["select_date"] = date("Y-m-d", M($M, "select_date"));
	$data["check_line"]          = M($M, "checkbox_line");
	$data["check_bar"]           = M($M, "checkbox_bar");
	$_SESSION["legend_show_sum"] = $data["check_sum"] = M($M, "checkbox_sum");
	$_SESSION["simple_temp"]     = $data["simple_temp"] = M($M, "checkbox_simple_temperature");
	$_SESSION["detail_temp"]     = $data["detail_temp"] = M($M, "checkbox_detailed_temperature");
	$_SESSION["rainfall"]        = $data["rainfall"] = M($M, "checkbox_rainfall");
	$data["option_scale"]        = M($M, "option_scale");

	ajax::js("$('#monatsauswertung_ctn').replaceWith(" . '(' . json_encode(array('val' => module::get("site.monatsauswertung", $data))) . ').val' . ")");
	ajax::js("$('#legend_ctn').replaceWith(" . '(' . json_encode(array('val' => module::get("legend"))) . ').val' . ")");
}

function ajax_change_zaehlstellenvergleich($M){
	FW::log(array("ajax_change_zaehlstellenvergleich" => $M));

	// Unpack zid_list
	$zid_list = array();
	foreach($M as $key => $value) if(substr($key, 0, 4) == "zid_" && $value == "Y") $zid_list[] = (int)str_replace("zid_", "", $key);

	$data                    = array();
	$_SESSION["select_date"] = $data["select_date"] = date("Y-m-d", M($M, "select_date"));
	$_SESSION["z_list"]      = $data["z_list"] = $zid_list;

	$cur_url = URL . "zaehlstellenvergleich/" . (int)date("Y", strtotime(M($data, "select_date"))) . "/" . (int)date("n", strtotime(M($data, "select_date"))) . "/";
	ajax::js("window.history.replaceState('Object', '', '$cur_url');");
	ajax::js("$('#zaehlstellenvergleich_ctn').replaceWith(" . '(' . json_encode(array('val' => module::get("site.zaehlstellenvergleich", $data))) . ').val' . ")");
	ajax::js("$('#legend_ctn').replaceWith(" . '(' . json_encode(array('val' => module::get("legend"))) . ').val' . ")");
}

function ajax_change_entwicklung($M){
	FW::log(array("ajax_change_entwicklung" => $M));

	// Unpack year_list
	$year_list = array();
	foreach($M as $key => $value) if(substr($key, 0, 5) == "year_" && $value == "Y") $year_list[] = (int)str_replace("year_", "", $key);

	// Unpack month_list
	$month_list = array();
	foreach($M as $key => $value) if(substr($key, 0, 6) == "month_" && $value == "Y") $month_list[] = (int)str_replace("month_", "", $key);

	// Check if month_list is empty or all values evaluate to false
	$has_valid_months = !empty($month_list);

	$data                   = array();
	$_SESSION["year_list"]  = $data["year_list"] = $year_list;
	$_SESSION["month_list"] = $data["month_list"] = $month_list;
	$_SESSION["option_dtv"] = $data["option_dtv"] = M($M, "option_dtv");
	$data["show_only_years"] = !$has_valid_months;

	ajax::js("$('#entwicklung_ctn').replaceWith(" . '(' . json_encode(array('val' => module::get("site.entwicklung", $data))) . ').val' . ")");
	ajax::js("$('#legend_ctn').replaceWith(" . '(' . json_encode(array('val' => module::get("legend"))) . ').val' . ")");
}

function ajax_change_jahresauswertung($M){
	FW::log(array("ajax_change_jahresauswertung" => $M));

	$data                    = array();
	$_SESSION["select_date"] = $data["select_date"] = date("Y-m-d", M($M, "select_date"));

	ajax::js("$('#jahresauswertung_ctn').replaceWith(" . '(' . json_encode(array('val' => module::get("site.jahresauswertung", $data))) . ').val' . ")");
	ajax::js("$('#legend_ctn').replaceWith(" . '(' . json_encode(array('val' => module::get("legend"))) . ').val' . ")");
}

function ajax_change_zaehlstellenuebersicht($M){
	FW::log(array("ajax_change_zaehlstellenvergleich" => $M));

	$data              = array();
	$data["cur_year"]  = M($M, "select_year");
	$data["cur_month"] = M($M, "select_month");
	$cur_url           = URL . "zaehlstellenuebersicht/" . (int)$data["cur_year"] . "/" . (int)$data["cur_month"] . "/";

	ajax::js("window.history.replaceState('Object', '', '$cur_url');");
	ajax::js("$('#zaehlstellenuebersicht_ctn').replaceWith(" . '(' . json_encode(array('val' => module::get("site.zaehlstellenuebersicht", $data))) . ').val' . ")");
}

function ajax_zaehlstellenuebersicht_export($M){
	$data         = $_GET;
	$monate       = array(1 => "Jänner", 2 => "Februar", 3 => "März", 4 => "April", 5 => "Mai", 6 => "Juni", 7 => "Juli",
	                      8 => "August", 9 => "September", 10 => "Oktober", 11 => "November", 12 => "Dezember");
	$year_min_max = SQ::assoc("SELECT MIN(Jahr) as min, MAX(Jahr) as max FROM nast_zaehldaten", true);
	$year_min     = intval(M($year_min_max, "min"));
	$year_max     = intval(M($year_min_max, "max"));

	$valid_fields      = array_keys(SQ::table("nast_zaehldaten"));
	$zaehlstellen_list = SQ::assoc("SELECT id, name_voll, richtung_1, richtung_2, reverse_dir FROM nast_zaehlstellen WHERE del = 0");
	$zaehlstellen_list = $zaehlstellen_list ?: array();
	$z_data_map        = array();
	foreach($zaehlstellen_list as $zaehlstelle) $z_data_map[M($zaehlstelle, "id")] = array(M($zaehlstelle, "name_voll"), M($zaehlstelle, "richtung_1"), M($zaehlstelle, "richtung_2"), M($zaehlstelle, "reverse_dir"));

	$cur_year   = (int)M($data, "cur_year", (FW::call(1) ? FW::call(1) : M($year_min_max, "max")));
	$cur_year   = $cur_year < $year_min ? $year_min : $cur_year;
	$cur_year   = $cur_year > $year_max ? $year_max : $cur_year;
	$cur_year_e = SQ::esc($cur_year);

	$cur_month   = (int)M($data, "cur_month", (FW::call(2) ? FW::call(2) : SQ::val("SELECT Monat FROM nast_zaehldaten ORDER BY Jahr DESC, Monat DESC Limit 1")));
	$cur_month   = $cur_month < 1 ? 1 : $cur_month;
	$cur_month   = $cur_month > 12 ? 12 : $cur_month;
	$cur_month_e = SQ::esc($cur_month);

	$text_assoc = SQ::assoc("SELECT zaehlstellen_id, text, text_uebersicht FROM nast_monatsausw_text WHERE Monat = $cur_month_e AND Jahr = $cur_year_e AND del = 0");
	$text_assoc = $text_assoc ?: array();
	$z_text_map = array();
	foreach($text_assoc as $text_e) $z_text_map[M($text_e, "zaehlstellen_id")] = M($text_e, "text_uebersicht");

	$z_dtv_list = computeDtvPerRichtung(null, $cur_year, $cur_month);
	ob_get_clean();
	ob_end_clean();

	FW::require_class("PHPExcel");
	require_once PATH_THEME . "class" . DS . "PHPExcel" . DS . "PHPExcel" . DS . "IOFactory.php";
	require_once PATH_THEME . "class" . DS . "PHPExcel" . DS . "PHPExcel" . DS . "Writer" . DS . "Excel2007.php";

	$objPHPExcel = new PHPExcel();
	$page_2      = new PHPExcel_Worksheet($objPHPExcel);
	$objPHPExcel->addSheet($page_2, 1);

	// Set properties
	$objPHPExcel->getProperties()->setCreator("NAST");
	$objPHPExcel->getProperties()->setLastModifiedBy("NAST");
	$objPHPExcel->getProperties()->setTitle("Zählstellenübersicht - " . M($monate, $cur_month) . " " . $cur_year);
	$objPHPExcel->getProperties()->setSubject("Zählstellenübersicht");
	$objPHPExcel->getProperties()->setDescription("Created by NAST - Zählstellenübersicht - Export @ " . date("d.m.Y - H:i:s"));

	// Add data
	$objPHPExcel->setActiveSheetIndex(0);

	// Row 1
	$objPHPExcel->getActiveSheet()->SetCellValue('A1', 'Zählstellenname');
	$objPHPExcel->getActiveSheet()->SetCellValue('B1', 'Richtung');

	// Create DTV weekday column header with special formatting
	$objRichText = new PHPExcel_RichText();
	$objText     = $objRichText->createTextRun('DTV');
	$objText->getFont()->setBold(true);
	$objSubScript = $objRichText->createTextRun('W');
	$objSubScript->getFont()->setSubScript(true);
	$objSubScript->getFont()->setBold(true);
	$objText = $objRichText->createTextRun(' Mo - Fr');
	$objText->getFont()->setBold(true);

	$objPHPExcel->getActiveSheet()->SetCellValue('C1', $objRichText);
	$objPHPExcel->getActiveSheet()->SetCellValue('D1', 'DTV Sa');
	$objPHPExcel->getActiveSheet()->SetCellValue('E1', 'DTV So + FT');
	$objPHPExcel->getActiveSheet()->SetCellValue('F1', 'Anmerkungen');
	$objPHPExcel->getActiveSheet()->getStyle("A1")->getFont()->setBold(true);
	$objPHPExcel->getActiveSheet()->getStyle("B1")->getFont()->setBold(true);
	$objPHPExcel->getActiveSheet()->getStyle("C1")->getFont()->setBold(true);
	$objPHPExcel->getActiveSheet()->getStyle("D1")->getFont()->setBold(true);
	$objPHPExcel->getActiveSheet()->getStyle("E1")->getFont()->setBold(true);
	$objPHPExcel->getActiveSheet()->getStyle("F1")->getFont()->setBold(true);
	$objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(22);
	$objPHPExcel->getActiveSheet()->getColumnDimension('B')->setWidth(22);
	$objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(15);
	$objPHPExcel->getActiveSheet()->getColumnDimension('D')->setWidth(15);
	$objPHPExcel->getActiveSheet()->getColumnDimension('E')->setWidth(15);
	$objPHPExcel->getActiveSheet()->getColumnDimension('F')->setWidth(50);
	$objPHPExcel->getActiveSheet()->getStyle("A1")->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
	$objPHPExcel->getActiveSheet()->getStyle("B1")->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
	$objPHPExcel->getActiveSheet()->getStyle("C1")->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
	$objPHPExcel->getActiveSheet()->getStyle("D1")->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
	$objPHPExcel->getActiveSheet()->getStyle("E1")->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
	$objPHPExcel->getActiveSheet()->getStyle("F1")->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);

	// Set the horizontal line border
	$objPHPExcel->getActiveSheet()->getStyle('A2:F2')->getBorders()->getBottom()->setBorderStyle(PHPExcel_Style_Border::BORDER_THIN);

	// Row 2
	$objPHPExcel->getActiveSheet()->SetCellValue('A2', '');
	$objPHPExcel->getActiveSheet()->SetCellValue('B2', '');
	$objPHPExcel->getActiveSheet()->SetCellValue('C2', 'Radverkehr/24h');
	$objPHPExcel->getActiveSheet()->SetCellValue('D2', 'Radverkehr/24h');
	$objPHPExcel->getActiveSheet()->SetCellValue('E2', 'Radverkehr/24h');
	$objPHPExcel->getActiveSheet()->SetCellValue('F2', '');
	$objPHPExcel->getActiveSheet()->getStyle("A2")->getFont()->setBold(true);
	$objPHPExcel->getActiveSheet()->getStyle("B2")->getFont()->setBold(true);
	$objPHPExcel->getActiveSheet()->getStyle("C2")->getFont()->setBold(true);
	$objPHPExcel->getActiveSheet()->getStyle("D2")->getFont()->setBold(true);
	$objPHPExcel->getActiveSheet()->getStyle("E2")->getFont()->setBold(true);
	$objPHPExcel->getActiveSheet()->getStyle("F2")->getFont()->setBold(true);
	$objPHPExcel->getActiveSheet()->getStyle("A2")->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
	$objPHPExcel->getActiveSheet()->getStyle("B2")->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
	$objPHPExcel->getActiveSheet()->getStyle("C2")->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
	$objPHPExcel->getActiveSheet()->getStyle("D2")->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
	$objPHPExcel->getActiveSheet()->getStyle("E2")->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
	$objPHPExcel->getActiveSheet()->getStyle("F2")->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

	// Row 3 - beyond
	$current_row = 2;
	foreach($z_dtv_list as $zid => $data){
		foreach($data as $jahr => $data_s){
			foreach($data_s as $monat => $data_e){
				$z_data            = M($z_data_map, $zid);
				$reverse_direction = M($z_data, 3);

				// Spacing row
				++$current_row;
				$objPHPExcel->getActiveSheet()->SetCellValue("A{$current_row}", "");
				$objPHPExcel->getActiveSheet()->SetCellValue("B{$current_row}", "");
				$objPHPExcel->getActiveSheet()->SetCellValue("C{$current_row}", "");
				$objPHPExcel->getActiveSheet()->SetCellValue("D{$current_row}", "");
				$objPHPExcel->getActiveSheet()->SetCellValue("E{$current_row}", "");
				$objPHPExcel->getActiveSheet()->SetCellValue("F{$current_row}", "");

				if(!$reverse_direction){
					// Richtung 1 row
					++$current_row;
					$objPHPExcel->getActiveSheet()->SetCellValue("A{$current_row}", M($z_data, 0));
					$objPHPExcel->getActiveSheet()->SetCellValue("B{$current_row}", M($z_data, 1));
					$objPHPExcel->getActiveSheet()->SetCellValue("C{$current_row}", round(M($data_e, "1_wt"), 0));
					$objPHPExcel->getActiveSheet()->SetCellValue("D{$current_row}", round(M($data_e, "1_sa"), 0));
					$objPHPExcel->getActiveSheet()->SetCellValue("E{$current_row}", round(M($data_e, "1_so"), 0));
					$objPHPExcel->getActiveSheet()->SetCellValue("F{$current_row}", "");

					// Richtung 2 row
					++$current_row;
					$objPHPExcel->getActiveSheet()->SetCellValue("A{$current_row}", M($z_data, 0));
					$objPHPExcel->getActiveSheet()->SetCellValue("B{$current_row}", M($z_data, 2));
					$objPHPExcel->getActiveSheet()->SetCellValue("C{$current_row}", round(M($data_e, "2_wt"), 0));
					$objPHPExcel->getActiveSheet()->SetCellValue("D{$current_row}", round(M($data_e, "2_sa"), 0));
					$objPHPExcel->getActiveSheet()->SetCellValue("E{$current_row}", round(M($data_e, "2_so"), 0));
					$objPHPExcel->getActiveSheet()->SetCellValue("F{$current_row}", "");
				}
				else{
					// Richtung 2 row
					++$current_row;
					$objPHPExcel->getActiveSheet()->SetCellValue("A{$current_row}", M($z_data, 0));
					$objPHPExcel->getActiveSheet()->SetCellValue("B{$current_row}", M($z_data, 2));
					$objPHPExcel->getActiveSheet()->SetCellValue("C{$current_row}", round(M($data_e, "2_wt"), 0));
					$objPHPExcel->getActiveSheet()->SetCellValue("D{$current_row}", round(M($data_e, "2_sa"), 0));
					$objPHPExcel->getActiveSheet()->SetCellValue("E{$current_row}", round(M($data_e, "2_so"), 0));
					$objPHPExcel->getActiveSheet()->SetCellValue("F{$current_row}", "");

					// Richtung 1 row
					++$current_row;
					$objPHPExcel->getActiveSheet()->SetCellValue("A{$current_row}", M($z_data, 0));
					$objPHPExcel->getActiveSheet()->SetCellValue("B{$current_row}", M($z_data, 1));
					$objPHPExcel->getActiveSheet()->SetCellValue("C{$current_row}", round(M($data_e, "1_wt"), 0));
					$objPHPExcel->getActiveSheet()->SetCellValue("D{$current_row}", round(M($data_e, "1_sa"), 0));
					$objPHPExcel->getActiveSheet()->SetCellValue("E{$current_row}", round(M($data_e, "1_so"), 0));
					$objPHPExcel->getActiveSheet()->SetCellValue("F{$current_row}", "");
				}

				// Sum 2 row
				++$current_row;
				$objPHPExcel->getActiveSheet()->SetCellValue("A{$current_row}", M($z_data, 0));
				$objPHPExcel->getActiveSheet()->SetCellValue("B{$current_row}", "gesamt");
				$objPHPExcel->getActiveSheet()->SetCellValue("C{$current_row}", round(M($data_e, "1_wt"), 0) + round(M($data_e, "2_wt"), 0));
				$objPHPExcel->getActiveSheet()->SetCellValue("D{$current_row}", round(M($data_e, "1_sa"), 0) + round(M($data_e, "2_sa"), 0));
				$objPHPExcel->getActiveSheet()->SetCellValue("E{$current_row}", round(M($data_e, "1_so"), 0) + round(M($data_e, "2_so"), 0));
				$objPHPExcel->getActiveSheet()->SetCellValue("F{$current_row}", "");
				$objPHPExcel->getActiveSheet()->getStyle("A{$current_row}")->getFont()->setBold(true);
				$objPHPExcel->getActiveSheet()->getStyle("B{$current_row}")->getFont()->setBold(true);
				$objPHPExcel->getActiveSheet()->getStyle("C{$current_row}")->getFont()->setBold(true);
				$objPHPExcel->getActiveSheet()->getStyle("D{$current_row}")->getFont()->setBold(true);
				$objPHPExcel->getActiveSheet()->getStyle("E{$current_row}")->getFont()->setBold(true);
				$objPHPExcel->getActiveSheet()->getStyle("F{$current_row}")->getFont()->setBold(true);

				// Merge the anmerkungen cells and set anmerkung text
				$objPHPExcel->setActiveSheetIndex(0)->mergeCells("F" . ($current_row - 3) . ":F{$current_row}");
				$objPHPExcel->getActiveSheet()->SetCellValue("F" . ($current_row - 3), strip_tags(preg_replace('#<br\s*/?>#i', "\n", M($z_text_map, $zid))));
				$objPHPExcel->getActiveSheet()->getStyle("F" . ($current_row - 3) . ":F{$current_row}")->getAlignment()->setWrapText(true);
				$objPHPExcel->getActiveSheet()->getStyle("F" . ($current_row - 3) . ":F{$current_row}")->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);
			}
		}
	}

	// Set the vertical line border
	$objPHPExcel->getActiveSheet()->getStyle("A1:A{$current_row}")->getBorders()->getRight()->setBorderStyle(PHPExcel_Style_Border::BORDER_THIN);
	$objPHPExcel->getActiveSheet()->getStyle("B1:B{$current_row}")->getBorders()->getRight()->setBorderStyle(PHPExcel_Style_Border::BORDER_THIN);
	$objPHPExcel->getActiveSheet()->getStyle("C1:C{$current_row}")->getBorders()->getRight()->setBorderStyle(PHPExcel_Style_Border::BORDER_THIN);
	$objPHPExcel->getActiveSheet()->getStyle("D1:D{$current_row}")->getBorders()->getRight()->setBorderStyle(PHPExcel_Style_Border::BORDER_THIN);
	$objPHPExcel->getActiveSheet()->getStyle("E1:E{$current_row}")->getBorders()->getRight()->setBorderStyle(PHPExcel_Style_Border::BORDER_THIN);
	$objPHPExcel->getActiveSheet()->getStyle("F1:F{$current_row}")->getBorders()->getRight()->setBorderStyle(PHPExcel_Style_Border::BORDER_THIN);

	// Rename sheet
	$umlaute_map = array(' ' => '_', 'Ä' => 'Ae', 'Ö' => 'Oe', 'Ü' => 'Ue', 'ä' => 'ae', 'ö' => 'oe', 'ü' => 'ue', 'ß' => 'ss');
	$objPHPExcel->getActiveSheet()->setTitle(M($monate, $cur_month) . " " . $cur_year);


	$objPHPExcel->setActiveSheetIndex(1);
	$objPHPExcel->getActiveSheet()->setTitle("Legende");
	$objPHPExcel->getActiveSheet()->SetCellValue('A1', 'Legende:');
	$objPHPExcel->getActiveSheet()->getStyle("A1")->getFont()->setBold(true);

	$objPHPExcel->getActiveSheet()->SetCellValue('A3', 'Kürzel');
	$objPHPExcel->getActiveSheet()->SetCellValue('B3', 'Erläuterung');
	$objPHPExcel->getActiveSheet()->SetCellValue('C3', 'Einheit');

	// Create DTV weekday column header with special formatting
	$objRichText  = new PHPExcel_RichText();
	$objText      = $objRichText->createTextRun('DTV');
	$objSubScript = $objRichText->createTextRun('W');
	$objSubScript->getFont()->setSubScript(true);
	$objText = $objRichText->createTextRun(' Mo - Fr');
	$objPHPExcel->getActiveSheet()->SetCellValue('A4', $objRichText);
	$objPHPExcel->getActiveSheet()->SetCellValue('B4', 'Durchschnittlicher täglicher Radverkehr an Werktagen (Montag - Freitag)');
	$objPHPExcel->getActiveSheet()->SetCellValue('C4', 'RadfahrerInnen/24h');

	$objPHPExcel->getActiveSheet()->SetCellValue('A5', 'DTV Sa');
	$objPHPExcel->getActiveSheet()->SetCellValue('B5', 'Durchschnittlicher täglicher Radverkehr (Samstag)');
	$objPHPExcel->getActiveSheet()->SetCellValue('C5', 'RadfahrerInnen/24h');

	$objPHPExcel->getActiveSheet()->SetCellValue('A6', 'DTV So + FT');
	$objPHPExcel->getActiveSheet()->SetCellValue('B6', 'Durchschnittlicher täglicher Radverkehr (Sonn- und Feiertag)');
	$objPHPExcel->getActiveSheet()->SetCellValue('C6', 'RadfahrerInnen/24h');

	$objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(14);
	$objPHPExcel->getActiveSheet()->getColumnDimension('B')->setWidth(66);
	$objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(21);

	$objPHPExcel->setActiveSheetIndex(0);

	// Set headers
	$file_name = "Zaehlstellenuebersicht_" . filter::websafe_name(strtr(M($monate, $cur_month), $umlaute_map)) . "_" . $cur_year . ".xlsx";
	header('Content-Description: File Transfer');
	header('Content-Type: application/octet-stream');
	header("Content-Disposition: attachment; filename=$file_name");
	header("Content-Transfer-Encoding: binary");

	// Save Excel 2007 file
	$objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);


	if (ob_get_level()) ob_end_clean();
	$objWriter->save("php://output");

	die();
}

// Internet Explorer exclusive
function ajax_download_decode($M){
	$data                  = M($M, "data");
	$file_name             = M($data, "file_name");
	$data                  = str_replace("data:", "data://", $data);
	$_SESSION["img_data"]  = file_get_contents($data);
	$_SESSION["file_name"] = $file_name;
}

function ajax_download_decoded(){
	$file_name      = M($_GET, "file_name", M($_SESSION, "file_name"));
	$timestamp_rand = time() . rand(1, 9999);
	$full_path      = PATH_UPLOADS . $timestamp_rand . ".png";
	file_put_contents($full_path, $_SESSION["img_data"]);
	ob_get_clean();
	ob_end_clean();
	header('Content-Description: File Transfer');
	header('Content-Type: application/octet-stream');
	header('Content-Disposition: attachment; filename="' . $file_name . '"'); //<<< Note the " " surrounding the file name
	header('Content-Transfer-Encoding: binary');
	header('Connection: Keep-Alive');
	header('Expires: 0');
	header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
	header('Pragma: public');
	header('Content-Length: ' . filesize($full_path));
	readfile($full_path);
	unlink($full_path);
	die();
}

function ajax_download_uebersicht_zip(){
	$month = (int)M($_GET, "month");
	$year = (int)M($_GET, "year");
	$file_name = "uebersicht_" . $year . "_" . $month . ".zip";
	$file_name_temp = nast_chart_dispenser::get_zip_download($year, $month, (time() . rand(1, 9999) . $file_name));
	$full_path_temp = PATH_UPLOADS . "chart_zip" . DS . $file_name_temp;

	// Clear output buffer
	while (ob_get_level()) {
		ob_end_clean();
	}

	// Disable error reporting
	error_reporting(0);
	ini_set('display_errors', 0);

	// Set headers
	header('Content-Description: File Transfer');
	header('Content-Type: application/zip');
	header('Content-Disposition: attachment; filename="' . $file_name . '"');
	header('Content-Transfer-Encoding: binary');
	header('Expires: 0');
	header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
	header('Pragma: public');
	header('Content-Length: ' . filesize($full_path_temp));

	// Read the file and output its content
	readfile($full_path_temp);

	// Delete the temporary file
	unlink($full_path_temp);
	die();
}

/*
function ajax_download_uebersicht_zip(){
	$month          = (int)M($_GET, "month");
	$year           = (int)M($_GET, "year");
	$file_name      = "uebersicht_" . $year . "_" . $month . ".zip";
	$file_name_temp = nast_chart_dispenser::get_zip_download($year, $month, (time() . rand(1, 9999) . $file_name));
	$full_path_temp = PATH_UPLOADS . "chart_zip" . DS . $file_name_temp;
	//ob_get_clean();
	header('Content-Description: File Transfer');
	header('Content-Type: application/octet-stream');
	header('Content-Disposition: attachment; filename="' . $file_name . '"'); //<<< Note the " " surrounding the file name
	header('Content-Transfer-Encoding: binary');
	header('Connection: Keep-Alive');
	header('Expires: 0');
	header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
	header('Pragma: public');
	header('Content-Length: ' . filesize($full_path_temp));
	readfile($full_path_temp);
	unlink($full_path_temp);
	die();
}
*/

function ahex2rgb($hex){
	$hex = str_replace("#", "", $hex);

	if(strlen($hex) == 3){
		$r = hexdec(substr($hex, 0, 1) . substr($hex, 0, 1));
		$g = hexdec(substr($hex, 1, 1) . substr($hex, 1, 1));
		$b = hexdec(substr($hex, 2, 1) . substr($hex, 2, 1));
	}
	else{
		$r = hexdec(substr($hex, 0, 2));
		$g = hexdec(substr($hex, 2, 2));
		$b = hexdec(substr($hex, 4, 2));
	}
	$rgb = array($r, $g, $b);
	//return implode(",", $rgb); // returns the rgb values separated by commas
	return $rgb; // returns an array with the rgb values
}

function aresizeImage($filename, $max_width, $max_height){
	list($orig_width, $orig_height) = getimagesize($filename);
	$width  = $orig_width;
	$height = $orig_height;

	# taller
	if($height > $max_height){
		$width  = ($max_height / $height) * $width;
		$height = $max_height;
	}

	# wider
	if($width > $max_width){
		$height = ($max_width / $width) * $height;
		$width  = $max_width;
	}

	$image_p = imagecreatetruecolor(round($width), round($height));
	$image   = imagecreatefrompng($filename);
	imagecopyresampled($image_p, $image, 0, 0, 0, 0,
		round($width), round($height), round($orig_width), round($orig_height));

	return $image_p;
}

function acreate_zip($files = array(), $destination = '', $overwrite = false){
	//if the zip file already exists and overwrite is false, return false
	if(file_exists($destination) && !$overwrite){
		return false;
	}
	//vars
	$valid_files = array();
	//if files were passed in...
	if(is_array($files)){
		//cycle through each file
		foreach($files as $file){
			//make sure the file exists
			if(file_exists($file)){
				$valid_files[] = $file;
			}
		}
	}

	//if we have good files...
	if(count($valid_files)){
		//create the archive
		$zip             = new ZipArchive();
		$open_mask       = file_exists($destination) ? ZIPARCHIVE::OVERWRITE : ZIPARCHIVE::CREATE;
		$zip_result_code = $zip->open($destination, $open_mask);
		if($zip_result_code !== true){ // ex: ZipArchive::ER_NOZIP
			return false;
		}
		//add the files
		foreach($valid_files as $file){
			$zip->addFile($file, basename($file));
		}
		//debug
		//echo 'The zip archive contains ',$zip->numFiles,' files with a status of ',$zip->status;

		//close the zip -- done!
		$zip->close();

		//check to make sure the file exists
		return file_exists($destination);
	}
	else{
		return false;
	}
}

function ajax_haendische_zaehlungen_change_bezirk($M){
	FW::log(array("ajax_haendische_zaehlungen_change_bezirk" => $M));
	$bezirk_id = M($M, "bezirk_id");
	ajax::html(module::get("haendische_zaehlung", array("bezirk_id" => $bezirk_id)), "#bezirk_content");
}

// Generate a array of 20 different random colors to be assigned to the chart
mt_srand(3);
$color_data = array();
for($i = 0; $i < 20; $i++){
	$color_data[] = sprintf('#%06X', mt_rand(0, 0xFFFFFF));
}
$_SESSION["color_list"] = $color_data;

function rep_del_zero($str, $force_zero = false){
	if($force_zero) return 0;
	$str = @number_format($str, 1, ',', '.') ? @number_format($str, 1, ',', '.') : "-";
	return $str;
}

function rep_del($str, $force_zero = false){
	if($force_zero) return 0;
	$str = @number_format($str, 1, ',', '.') ? @number_format($str, 1, ',', '.') : "-";
	$str = substr($str, 0, 3) === "0,0" ? str_replace("0,0", "-", $str) : $str;
	return $str;
}

function rep_del_p($str, $force_zero = false){
	if($force_zero) return "0,0";
	$not_zero = trim($str) != "0";
	$fraction = abs(@floatval($str) - @floatval(@floor(@floatval($str))));
	if(@abs(@intval($str)) < 1 && $fraction < 0.01) return "0,0";
	$str = @number_format($str, 1, ',', '.') ? @number_format($str, 1, ',', '.') : "-";
	$str = substr($str, 0, 3) === "0,0" ? str_replace("0,0", "-", $str) : $str;
	$str = trim($str);
	$str = $str == "0" ? "0,0" : $str;
	$str = $str == "-" && $not_zero ? "0,0" : $str;
	$str = str_replace("-0,0", "0,0", $str);
	return $str;
}

function rep_del_n($str, $force_zero = false){
	if($force_zero) return 0;
	if($str === "0" || $str === 0) return "-";
	$str = str_replace(".", ",", $str);
	$str = substr($str, 0, 3) === "0,0" ? str_replace("0,0", "-", $str) : $str;
	return $str;
}

function numbers_equal($num_a = false, $num_b = 1){
	return is_numeric($num_a) && is_numeric($num_b) && $num_a == $num_b;
}

if(IS_CLI && M($argv, 1, "") == "dtv"){
	FW::log("rebuild dtv");
	error_reporting(E_ALL);
	ini_set('display_errors', 1);
	FW::log("ajax_recompute_everything()");
	@ini_set('max_execution_time', 1800);
	@set_time_limit(1800);
	@ini_set("memory_limit", '4096M');
	@define('WP_MEMORY_LIMIT', '4096M');
	populateDtv();
	populateDtvJahresausw();
	FW::log("ajax_recompute_everything() - Done!");
}