name: Run tests

on:
  workflow_call:
    inputs:
      deps:
        type: string
        required: false
        default: stable
      continue_on_error:
        type: boolean
        required: false
        default: false

jobs:
  test:
    name: PHP ${{ matrix.php }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        php: ['7.4', '8.0', '8.1', '8.2', '8.3', '8.4']

    steps:
      - name: Check out code
        uses: actions/checkout@v4.2.2

      - name: Install PHP
        uses: shivammathur/setup-php@2.32.0
        with:
          php-version: ${{ matrix.php }}
          tools: composer:v2
          coverage: none

      - name: Set up problem matchers
        run: echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"

      - name: Update to dev stability
        if: inputs.deps == 'dev'
        run: |
          composer config minimum-stability dev
          composer config prefer-stable false

      - name: Install dependencies
        if: inputs.deps != 'lowest'
        uses: nick-fields/retry@v3.0.2
        with:
          timeout_minutes: 5
          max_attempts: 5
          command: composer update --no-interaction --no-progress

      - name: Install dependencies (--prefer-lowest)
        if: inputs.deps == 'lowest'
        uses: nick-fields/retry@v3.0.2
        with:
          timeout_minutes: 5
          max_attempts: 5
          command: composer update --prefer-lowest --prefer-stable --no-interaction --no-progress

      - name: Install PHPUnit
        uses: nick-fields/retry@v3.0.2
        with:
          timeout_minutes: 5
          max_attempts: 5
          command: composer bin phpunit update --no-interaction --no-progress

      - name: Run tests
        if: inputs.continue_on_error == false
        run: make test

      - name: Run tests (but don't complain)
        if: inputs.continue_on_error == true
        run: make test
        continue-on-error: true

      - name: Upload coverage
        uses: codecov/codecov-action@v5.4.0
