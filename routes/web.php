<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::prefix("admin")->group(function () {

    // DashboardController
    Route::get('/', 'Admin\DashboardController@overview')
        ->name("admin_overview");
		 
    // Faqs
    Route::get('/faqs', 'Admin\FaqController@index')
        ->name("admin_faqs");
    Route::get('/faqs/list', 'Admin\FaqController@ajaxList')
        ->name("admin_ajax_faq_list");
    Route::get('/faqs/edit/{id?}', 'Admin\FaqController@edit')
        ->name("admin_faq_edit");
    Route::post('/faqs/save/{id?}', 'Admin\FaqController@save')
        ->name("admin_faq_save");
    Route::delete('/faqs/remove', 'Admin\FaqController@remove')
        ->name("admin_faq_remove");

    // SettingController
    Route::get('/settings/{module?}', 'Admin\SettingController@settingForm')
        ->name("admin_settings");
    Route::post('/ajax_save_setting', 'Admin\SettingController@ajaxSaveSetting')
        ->name("admin_save_setting");
    
    // UploadController
    Route::post('/gfx_upload', 'Admin\UploadController@ajaxImageUpload')
        ->name('admin_ajax_image_upload');
    Route::post('/pdf_upload', 'Admin\UploadController@ajaxPdfUpload')
        ->name('admin_ajax_pdf_upload');
    Route::post('/video_upload', 'Admin\UploadController@ajaxVideoUpload')
        ->name('admin_ajax_video_upload');
    Route::post('/audio_upload', 'Admin\UploadController@ajaxAudioUpload')
        ->name('admin_ajax_audio_upload');

    //Texts admin module
    Route::get('/app_texts/{module?}', 'Admin\TextController@appTextForm')
        ->name("admin_app_texts");
    Route::post('/ajax_save_text', 'Admin\TextController@ajaxSaveText')
        ->name("admin_save_text");
    Route::post('/ajax_delete_text', 'Admin\TextController@ajaxDeleteText')
        ->name("admin_delete_text");
    Route::post('/import_texts', 'Admin\TextController@importTexts')
        ->name("admin_import_texts");
    Route::get('/export_texts/{module?}', 'Admin\TextController@exportTexts')
        ->name("admin_export_texts");
    Route::post('/send_preview_mail', 'Admin\TextController@sendPreviewEmail')
        ->name("admin_send_preview_mail");
    
    // AdminController
    Route::get('/list', 'Admin\AdminController@listAdmin')
        ->name('admin_list_admin');
    Route::get('/admin/ajax/list', 'Admin\AdminController@ajaxList')
        ->name('admin_ajax_admin_list');
    Route::get('/new-admin', 'Admin\AdminController@newAdmin')
        ->name('admin_new');
    Route::post('/save-admin', 'Admin\AdminController@saveAdmin')
        ->name('admin_save');
    Route::post('/save-admin/{id?}', 'Admin\AdminController@saveAdmin')
        ->name('admin_save_now');
    Route::get('/edit-admin/{id}', 'Admin\AdminController@editAdmin')
        ->name('admin_edit');
    Route::get('/regenerate/{id}', 'Admin\AdminController@regenerate')
        ->name('admin_regenerate');
    Route::delete('/delete-admin', 'Admin\AdminController@deleteAdmin')
        ->name('admin_delete');

    // BackendController
    Route::get('/change-pass', 'Admin\BackendController@changePassword')
        ->name('admin_change_password');
    Route::get('/logout', 'Admin\BackendController@logout')
        ->name('admin_logout');
    Route::get('/login', 'Admin\BackendController@login')
        ->name('admin_login');
    Route::post('/login', 'Admin\BackendController@validateLogin')
        ->name('admin_validate_login');
    Route::post('/save-pass', 'Admin\BackendController@saveNewPassword')
        ->name('admin_save_new_password');
    Route::get('/change-password/{type}', 'Admin\BackendController@forceChangePassword')
        ->name('admin_force_change_password');
    Route::post('/change-password', 'Admin\BackendController@saveChangedPassword')
        ->name('admin_save_changed_password');
    Route::get('/forgot-password', 'Admin\BackendController@forgotPassword')
        ->name('admin_forgot_password');
    Route::post('/forgot-password', 'Admin\BackendController@submitForgotPassword')
        ->name('admin_forgot_password_submit');
    Route::get('/resend-mail', 'Admin\BackendController@resendEmail')
        ->name('admin_resend_email');
    Route::get('/reset-password/{code}', 'Admin\BackendController@resetPassword')
        ->name('admin_reset_password');
    Route::post('/reset-password/{code}', 'Admin\BackendController@saveResetPassword')
        ->name('admin_save_reset_password');
    Route::post('/google/login/validate', 'Admin\BackendController@validateGoogleLogin')
        ->name('admin_validate_google_login');
});

Route::prefix("deploy")->middleware(["deploy"])->group(function (){

    // DeploymentController
    Route::get('/migrate', "Deploy\DeploymentController@migrate")
        ->name("deploy_migrate")->withoutMiddleware("web");
    Route::get('/rebuild_cache', "Deploy\DeploymentController@rebuildCache")
        ->name("deploy_rebuild_cache");
    Route::get('/create_first_user', "Deploy\DeploymentController@createFirstUser")
        ->name("deploy_create_first_user");
    Route::get('/maintenance', "Deploy\DeploymentController@maintenance")
        ->name("deploy_maintenance");
	Route::get('/add_maintenance_ip', "Deploy\DeploymentController@addMaintenanceIP")
		->name("deploy_add_ip");
	Route::get('/add_backend_ip', "Deploy\DeploymentController@addBackendIP")
		->name("deploy_add_backend_ip");
     Route::get('/revert_htaccess', "Deploy\DeploymentController@revertHtaccess")
        ->name("deploy_revert_htaccess");
     Route::get('/optimize_images', "Deploy\DeploymentController@optimizeImages")
        ->name("deploy_optimize_images");
     /*Route::get('/generate_webp', "Deploy\DeploymentController@generateWebp")
        ->name("deploy_generate_webp");*/
});

// SiteController
Route::get("/", 'Site\SiteController@index')
    ->name("site_index");

// PWAController
Route::get('/offline', 'Site\PWAController@offlinePage')
    ->name('site_offline');
Route::get('/manifest.json', 'Site\PWAController@manifestJson')
    ->name('site_manifest');