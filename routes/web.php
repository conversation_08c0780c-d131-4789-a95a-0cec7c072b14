<?php

use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\ApiAccessController;
use App\Http\Controllers\Admin\ApiExplorerController;
use App\Http\Controllers\Admin\ApiStatsController;
use App\Http\Controllers\Admin\BackendController;
use App\Http\Controllers\Admin\CategoriesController;
use App\Http\Controllers\Admin\CategorizerController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\FieldOverwriteController;
use App\Http\Controllers\Admin\GameController;
use App\Http\Controllers\Admin\LanguageController;
use App\Http\Controllers\Admin\MicrosoftGameController;
use App\Http\Controllers\Admin\NintendoGameController;
use App\Http\Controllers\Admin\PlatformController;
use App\Http\Controllers\Admin\ProductsExplorerController;
use App\Http\Controllers\Admin\PublisherController;
use App\Http\Controllers\Admin\SettingController;
use App\Http\Controllers\Admin\TextController;
use App\Http\Controllers\Admin\TweetAiBrowseController;
use App\Http\Controllers\Admin\TweetAiCreateController;
use App\Http\Controllers\Admin\VideoCreatorController;
use App\Http\Controllers\DebugController;
use App\Http\Controllers\Deploy\DeploymentController;
use App\Http\Controllers\Site\CronController;
use App\Http\Controllers\Site\EditlyController;
use App\Http\Controllers\Site\InstagramHookTestController;
use App\Http\Controllers\Site\Photo2WayTestController;
use App\Http\Controllers\Site\SiteController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::prefix("admin")->group(function (){
    // DashboardController
    Route::controller(DashboardController::class)->group(function (){
        Route::get('/', 'overview')->name("admin_overview");
        Route::post('/export-games', 'exportGames')->name("admin_export_game");

    });

    // SettingController
    Route::controller(SettingController::class)->group(function (){
        Route::get('/settings', 'settingForm')->name("admin_settings");
        Route::post('/ajax_save_setting', 'ajaxSaveSetting')->name("admin_save_setting");
        Route::post('/gfx_upload', 'ajaxImageUpload')->name("admin_settings_ajax_upload");
    });

    // TextController
    Route::controller(TextController::class)->group(function (){
        Route::get('/app_texts', 'appTextForm')->name("admin_app_texts");
        Route::post('/ajax_save_text', 'ajaxSaveText')->name("admin_save_text");
        Route::post('/ajax_delete_text', 'ajaxDeleteText')->name("admin_delete_text");
        Route::post('/import_texts', 'importTexts')->name("admin_import_texts");
        Route::get('/export_texts', 'exportTexts')->name("admin_export_texts");
    });

    // LanguageController
    Route::controller(LanguageController::class)->group(function (){
        Route::get('/languages', 'listLanguages')->name('admin_list_languages');
        Route::get('/languages/list', 'ajaxList')->name('admin_ajax_language_list');
        Route::post('/language/update-rtl', 'updateRtl')->name('admin_language_rtl');
        Route::get('/edit-language/{id}', 'editLanguage')->name('admin_language_edit');
        Route::post('/save-language/{id}', 'saveLanguage')->name('admin_save_language');
    });

    // GameController
    Route::controller(GameController::class)->group(function (){
        Route::get('/games', 'index')->name('admin_games');
        Route::get('/games/list', 'ajaxList')->name('admin_ajax_game_list');
        Route::get('/game-form/{id}', 'edit')->name('admin_game_edit');
        Route::post('/save-game/{id}', 'save')->name('admin_save_game');
        Route::delete('/delete-game', 'remove')->name('admin_delete_game');
    });

    // NintendoGameController
    Route::controller(NintendoGameController::class)->prefix("/nintendo/")->group(function (){
        Route::get('/games', 'index')->name('admin_nintendo_games');
        Route::get('/games/list', 'ajaxList')->name('admin_nintendo_ajax_game_list');
        Route::get('/game-form/{id}', 'edit')->name('admin_nintendo_game_edit');
    });

    // MicrosoftGameController
    Route::controller(MicrosoftGameController::class)->prefix("/microsoft/")->group(function (){
        Route::get('/games', 'index')->name('admin_microsoft_games');
        Route::get('/games/list', 'ajaxList')->name('admin_microsoft_ajax_game_list');
        Route::get('/game-form/{id}', 'edit')->name('admin_microsoft_game_edit');
    });

    // ApiAccessController
    Route::controller(ApiAccessController::class)->group(function (){
        Route::get('/api_access', 'index')->name('admin_api_access');
        Route::get('/api_access/list', 'ajaxList')->name('admin_ajax_api_access_list');
        Route::get('/api-access-form/{id?}', 'edit')->name('admin_api_access_edit');
        Route::post('/save-api-access/{id?}', 'save')->name('admin_save_api_access');
        Route::delete('/revoke-api-access', 'revoke')->name('admin_revoke_api_access');
    });

    // ApiExplorerController
    Route::controller(ApiExplorerController::class)->group(function (){
        Route::get('/api_explorer', 'index')->name('admin_api_explorer');
    });

    // FieldOverwriteController
    Route::controller(FieldOverwriteController::class)->group(function (){
        Route::get('/api_overwrites', 'index')->name('admin_field_overwrites');
    });

    // ApiStatsController
    Route::controller(ApiStatsController::class)->group(function (){
        Route::get('/api_stats', 'index')->name('admin_api_stats');
    });

    // GamesExplorerController
    Route::controller(ProductsExplorerController::class)->group(function (){
        Route::get('/products', 'index')->name('admin_products_explorer');
    });

    // CategoriesController
    Route::controller(CategoriesController::class)->group(function (){
        Route::get('/categories', 'index')->name('admin_categories');
    });

    // VideoCreatorController
    Route::controller(VideoCreatorController::class)->group(function (){
        Route::get('/video-creators', 'index')->name('admin_video_creators');
        Route::get('/video-creators/list', 'ajaxList')->name('admin_ajax_video_creator_list');
        Route::get('/video-creator-form/{id?}', 'edit')->name('admin_video_creator_edit');
        Route::post('/save-video-creator/{id?}', 'save')->name('admin_save_video_creator');
        Route::get('/video-creators/games/list/{id}', 'ajaxGameList')->name('admin_ajax_video_creator_game_list');
        Route::post('/video-creators/set-games', 'setGames');
        Route::get('/video-creators/last-step/{id}', 'lastStep')->name('admin_video_creator_game_last_step');
        Route::get('/video-creators/last-step-list/{id}/{status}', 'listGames')->name('admin_ajax_selected_video_creator_game_list');
        Route::post('/video-creators/update-game-trailer', 'updateGameTrailer');
        Route::delete('/delete-video-game', 'remove')->name('admin_delete_video_game');
        Route::get('/video-creators/make-video-game/{id}', 'makeVideo')->name('admin_make_video_game');
        Route::get('/video-creators/recreate-video-game/{id}', 'recreateVideo')->name('admin_recreate_video_game');
        Route::get('/video-creators/url-list/{id?}', 'urlList')->name('admin_download_url_list');
        Route::get('/video-creators/delete-videos/{id?}', 'deleteVideos')->name('admin_delete_videos_from_url_list');
    });

    // Publishers
    Route::controller(PublisherController::class)->group(function (){
        Route::get('/publishers', 'index')->name("admin_publishers");
        Route::get('/publishers/list', 'ajaxList')->name("admin_ajax_publisher_list");
        Route::get('/publishers/edit/{id?}', 'edit')->name("admin_publisher_edit");
        Route::post('/publishers/save/{id?}', 'save')->name("admin_publisher_save");
        Route::delete('/publishers/remove', 'remove')->name("admin_publisher_remove");
        Route::post('/import_publishers', 'importPublishers')->name("admin_import_publishers");
        Route::get('/export_publishers', 'exportPublishers')->name("admin_export_publishers");
    });

    // Platforms
    Route::controller(PlatformController::class)->group(function (){

        Route::get('/platforms', 'index')->name("admin_platforms");
        Route::get('/platforms/list', 'ajaxList')->name("admin_ajax_platform_list");
        Route::get('/platforms/edit/{id?}', 'edit')->name("admin_platform_edit");
        Route::post('/platforms/save/{id?}', 'save')->name("admin_platform_save");
        Route::delete('/platforms/remove', 'remove')->name("admin_platform_remove");

    });

    // AdminController
    Route::controller(AdminController::class)->group(function (){
        Route::get('/list', 'listAdmin')->name('admin_list_admin');
        Route::get('/admin/ajax/list', 'ajaxList')->name('admin_ajax_admin_list');
        Route::get('/new-admin', 'newAdmin')->name('admin_new');
        Route::post('/save-admin', 'saveAdmin')->name('admin_save');
        Route::post('/save-admin/{id?}', 'saveAdmin')->name('admin_save_now');
        Route::get('/edit-admin/{id}', 'editAdmin')->name('admin_edit');
        Route::get('/regenerate/{id}', 'regenerate')->name('admin_regenerate');
        Route::delete('/delete-admin', 'deleteAdmin')->name('admin_delete');
    });

    // BackendController
    Route::controller(BackendController::class)->group(function (){
        Route::get('/change-pass', 'changePassword')->name('admin_change_password');
        Route::get('/logout', 'logout')->name('admin_logout');
        Route::get('/login', 'login')->name('admin_login');
        Route::post('/login', 'validateLogin')->name('admin_validate_login');
        Route::post('/save-pass', 'saveNewPassword')->name('admin_save_new_password');
        Route::get('/change-password/{type}', 'forceChangePassword')->name('admin_force_change_password');
        Route::post('/change-password', 'saveChangedPassword')->name('admin_save_changed_password');
        Route::get('/forgot-password', 'forgotPassword')->name('admin_forgot_password');
        Route::post('/forgot-password', 'submitForgotPassword')->name('admin_forgot_password_submit');
        Route::get('/resend-mail', 'resendEmail')->name('admin_resend_email');
        Route::get('/reset-password/{code}', 'resetPassword')->name('admin_reset_password');
        Route::post('/reset-password/{code}', 'saveResetPassword')->name('admin_save_reset_password');
        Route::post('/google/login/validate', 'validateGoogleLogin')->name('admin_validate_google_login');
    });

    // Tweet AI Browse
    Route::controller(TweetAiBrowseController::class)->group(function (){
        Route::get('/tweet-ai-browse', 'index')->name('admin_tweet_ai_browse');
    });

    // Tweet AI Create
    Route::controller(TweetAiCreateController::class)->group(function (){
        Route::get('/tweet-ai-create', 'index')->name('admin_tweet_ai_create');
    });

    // Categorizer
    Route::controller(CategorizerController::class)->group(function (){
        Route::get('/video_categorizer', 'index')->name('admin_video_categorizer');
        Route::post('/upload_video', 'videoUpload')->name('admin_video_categorizer_upload_video');
        Route::post('/delete_video', 'deleteVideo')->name('admin_video_categorizer_delete_video');
    });
});

// DeploymentController
Route::prefix("deploy")->middleware(["deploy"])->controller(DeploymentController::class)->group(function (){
    Route::get('/api_token', "getDevApiToken")->name("dev_api_token")->withoutMiddleware("web");
    Route::get('/migrate', "migrate")->name("deploy_migrate")->withoutMiddleware("web");
    Route::get('/rebuild_cache', "rebuildCache")->name("deploy_rebuild_cache");
    Route::get('/create_first_user', "createFirstUser")->name("deploy_create_first_user");
    Route::get('/add_backend_ip', "addBackendIP")->name("deploy_add_backend_ip");
    Route::get('/revert_htaccess', "revertHtaccess")->name("deploy_revert_htaccess");
    Route::get('/optimize_images', "optimizeImages")->name("deploy_optimize_images");
    Route::get('/maintenance', "maintenance")->name("deploy_maintenance");
    Route::get('/add_maintenance_ip', "addMaintenanceIP")->name("deploy_add_ip");
    Route::get('/generate_docs', "generateApiDocs")->name("deploy_generate_docs");
    Route::get('/auto_allow_videos', "autoAllowVideos")->name("deploy_auto_allow_videos");
});

// CronController
Route::controller(CronController::class)->prefix("cron")->group(function (){
    Route::get("/ps_tv_games", 'psTvGameExport');
    Route::get("/trailer_to_mp4", 'trailerToMp4');
    Route::get('/make-video', 'makeVideo')->name("cron_make_video_ps-plus");
    Route::get('/make-standard-video', 'makeStandardVideo')->name("cron_make_video_standard");
});

// EditlyController
Route::controller(EditlyController::class)->prefix("editly")->group(function (){
    Route::get('/make-video', 'makeVideo');
});

// SiteController
Route::controller(SiteController::class)->group(function (){
    Route::get("/", 'index')->name("site_index");
    Route::get("/import-thumbnails", 'importThumbnails')->name("site_import_thumbnails");

    Route::get("/game-list", 'gameList')->name("site_game_list");

    Route::get("/update-publisher", 'updatePublisher');
});

// Foto2WayTestController
Route::controller(Photo2WayTestController::class)->group(function (){
    Route::get("/photo2way", 'index')->name("photo2waytest_index");
    Route::get("/photo2way/link/{link_id}", 'link')->name("photo2waytest_link");
    Route::post("/photo2way/link_action", 'linkAction')->name("photo2waytest_link_action");
});

// InstagramHookTestController
Route::controller(InstagramHookTestController::class)->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)->group(function (){
    Route::get("/instagram_hook_test", 'index')->name("instagram_hook_test");
    Route::any("/instagram_hook_test/on_message", 'onMessage')->name("instagram_hook_test_on_message");
});

// DebugController
Route::controller(DebugController::class)->prefix("debug_scrapers")->group(function (){
    Route::get("/", 'index')->name("debug_index");
});
