<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/rpc/context/attribute_context.proto

namespace GPBMetadata\Google\Rpc\Context;

class AttributeContext
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Protobuf\Any::initOnce();
        \GPBMetadata\Google\Protobuf\Duration::initOnce();
        \GPBMetadata\Google\Protobuf\Struct::initOnce();
        \GPBMetadata\Google\Protobuf\Timestamp::initOnce();
        $pool->internalAddGeneratedFile(
            '
�
*google/rpc/context/attribute_context.protogoogle.rpc.contextgoogle/protobuf/duration.protogoogle/protobuf/struct.protogoogle/protobuf/timestamp.proto"�
AttributeContext9
origin (2).google.rpc.context.AttributeContext.Peer9
source (2).google.rpc.context.AttributeContext.Peer>
destination (2).google.rpc.context.AttributeContext.Peer=
request (2,.google.rpc.context.AttributeContext.Request?
response (2-.google.rpc.context.AttributeContext.Response?
resource (2-.google.rpc.context.AttributeContext.Resource5
api (2(.google.rpc.context.AttributeContext.Api(

extensions (2.google.protobuf.Any�
Peer

ip (	
port (E
labels (25.google.rpc.context.AttributeContext.Peer.LabelsEntry
	principal (	
region_code (	-
LabelsEntry
key (	
value (	:8L
Api
service (	
	operation (	
protocol (	
version (	
Auth
	principal (	
	audiences (	
	presenter (	\'
claims (2.google.protobuf.Struct

access_levels (	�
Request

id (	
method (	J
headers (29.google.rpc.context.AttributeContext.Request.HeadersEntry
path (	
host (	
scheme (	
query (	(
time	 (2.google.protobuf.Timestamp
size
 (
protocol (	
reason (	7
auth
 (2).google.rpc.context.AttributeContext.Auth.
HeadersEntry
key (	
value (	:8�
Response
code (
size (K
headers (2:.google.rpc.context.AttributeContext.Response.HeadersEntry(
time (2.google.protobuf.Timestamp2
backend_latency (2.google.protobuf.Duration.
HeadersEntry
key (	
value (	:8�
Resource
service (	
name (	
type (	I
labels (29.google.rpc.context.AttributeContext.Resource.LabelsEntry
uid (	S
annotations (2>.google.rpc.context.AttributeContext.Resource.AnnotationsEntry
display_name (	/
create_time (2.google.protobuf.Timestamp/
update_time	 (2.google.protobuf.Timestamp/
delete_time
 (2.google.protobuf.Timestamp
etag (	
location (	-
LabelsEntry
key (	
value (	:82
AnnotationsEntry
key (	
value (	:8B�
com.google.rpc.contextBAttributeContextProtoPZUgoogle.golang.org/genproto/googleapis/rpc/context/attribute_context;attribute_context�bproto3'
        , true);

        static::$is_initialized = true;
    }
}

