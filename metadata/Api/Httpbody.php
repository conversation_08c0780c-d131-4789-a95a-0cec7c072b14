<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/httpbody.proto

namespace GPBMetadata\Google\Api;

class Httpbody
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Protobuf\Any::initOnce();
        $pool->internalAddGeneratedFile(
            '
�
google/api/httpbody.proto
google.api"X
HttpBody
content_type (	
data ((

extensions (2.google.protobuf.AnyBe
com.google.apiB
HttpBodyProtoPZ;google.golang.org/genproto/googleapis/api/httpbody;httpbody�GAPIbproto3'
        , true);

        static::$is_initialized = true;
    }
}

