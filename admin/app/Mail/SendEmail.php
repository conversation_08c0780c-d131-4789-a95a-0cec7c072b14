<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SendEmail extends Mailable{
    use Queueable, SerializesModels;

    /**
     * The admins object instance.
     *
     * @var Admin
     */
    public $mail_key;
	public $mail_data;
	
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($mail_key, $mail_data=[]){

        $this->mail_key = $mail_key;
		$this->mail_data = $mail_data;
    }
	
    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(){
		
		$to_email 	= $this->variables(fT('mail.'.$this->mail_key.'.to_email', '[email]'));
		$to_name 	= $this->variables(fT('mail.'.$this->mail_key.'.to_name', '[email]'));

        //check if force to email is set for preview
        if(isset($this->mail_data['force_to_email']) && $this->mail_data['force_to_email'] != "")  {

            $to_email 	= $this->mail_data['force_to_email'];
        }
        
		$from_email = $this->variables(fT('mail.'.$this->mail_key.'.from_email', config('mail.from.address')));
		$from_name 	= $this->variables(fT('mail.'.$this->mail_key.'.from_name', config('mail.from.name')));
		
		$reply_to_email = $this->variables(fT('mail.'.$this->mail_key.'.reply_to_email', config('mail.from.address')));
		$reply_to_name 	= $this->variables(fT('mail.'.$this->mail_key.'.reply_to_name', config('mail.from.name')));
		
		$ndr_envelope_to = $this->variables(fT('mail.'.$this->mail_key.'.ndr_envelope_to', ''));
		
		$bcc_email 	= $this->variables(fT('mail.'.$this->mail_key.'.bcc_email', ''));
		$bcc_name 	= $this->variables(fT('mail.'.$this->mail_key.'.bcc_name', ''));
		
		$subject 	= $this->variables(fT('mail.'.$this->mail_key.'.subject', $this->mail_key.' subject'));
		$mail_body 	= $this->variables(fT('mail.'.$this->mail_key.'.body', $this->mail_key.' body text'));
		
        $mailObj = $this->from($from_email, $from_name)
					->subject($subject)
					->view('mails.email_template')
                    ->with(array(
						'mail_body' => $mail_body,
                    ));
		
		//set to emails for multiple case
		$to_emails = explode(",", $to_email);
		if(count($to_emails) > 1)  {
			$trimmed_emails = array_map('trim', $to_emails);
			$mailObj->to($trimmed_emails);
		}	else	{
			$mailObj->to($to_email, $to_name);
		}
		
		if($reply_to_email)
			$mailObj->replyTo(trim($reply_to_email), $reply_to_name);
		
		if($bcc_email)  {

            $bcc_emails = explode(",", $bcc_email);
            $bcc_emails = array_map('trim', $bcc_emails);

            $mailObj->bcc($bcc_emails, $bcc_name);
        }
		
		$this->withSwiftMessage(function ($message) use ($ndr_envelope_to, $from_email){
            $returnPath = trim($ndr_envelope_to ?? "") ?: $from_email;
            $message->getHeaders()->addTextHeader('Return-Path', $returnPath);
        });
		
		return $mailObj;
    }
	
	private function variables($string)  {
		
		foreach($this->mail_data as $field => $val)  {
			$string 	= str_replace("[".$field."]", $val, $string);
		}

        $img_url = url('public/gfx/mail');
        if(is_localhost())
            $img_url = "https://ebhost01.contentcreators.at/nl/win2day/images";

        $string 	= str_replace("[img_url]", $img_url, $string);
		
		return $string;
	}

}
?>