include:
  - project: 'dev/ci-templates'
    file: '/base-template.yml'

before_script: # Install dependencies if missing
  - apt-get update -qq && apt-get install -y -qq rsync openssh-client sshpass

variables: # Default to dev
  REMOTE_USER: gitlab_tmp # user with SSH access on remotes hosts like devhost (REMOTE_PASSWORD comes via global group variable)
  REMOTE_HOST: *************** # stefflhost *new*
  REMOTE_DIR: steffl02
  LOCAL_DIR: ./
  SSH_PORT: 22  # Modify if you're using a non-default SSH port

check-variables:
  stage: .pre
  script:
    - |
      REQUIRED_VARS=(REMOTE_USER REMOTE_HOST REMOTE_DIR LOCAL_DIR SSH_PORT REMOTE_PASSWORD)
      for VAR in "${REQUIRED_VARS[@]}"; do
        if [ -z "${!VAR}" ]; then
          echo "Error: $VAR is not defined or is empty."
          exit 1
        fi
      done

deploy-production-job:
  variables:
    REMOTE_USER: gitlab_tmp # user with SSH access on remotes hosts like devhost (REMOTE_PASSWORD comes via global group variable)
    REMOTE_HOST: *************** # stefflhost *new*
    REMOTE_DIR: steffl01
    LOCAL_DIR: ./
    SSH_PORT: 22  # Modify if you're using a non-default SSH port
  stage: deploy
  script:
    # Set up known hosts to allow SSH connections
    - mkdir -p ~/.ssh
    - ssh-keyscan -p $SSH_PORT $REMOTE_HOST >> ~/.ssh/known_hosts

    # Check is right project for right remote directory
    - |
      sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST '
      REMOTE_DIR="'$REMOTE_DIR'";
      REMOTE_USER="'$REMOTE_USER'";
      USER_CLAIM_FILE="/srv/html/htdocs/$REMOTE_DIR/user-claim.txt";
      TIMESTAMP=$(date +%Y%m%d_%H%M%S);
      ARCHIVE_FOLDER="/home/<USER>/pre_first_deploy";
      ARCHIVE_PATH="$ARCHIVE_FOLDER/${REMOTE_DIR}_old_${TIMESTAMP}.tar.gz";

      if [ -z "$REMOTE_DIR" ]; then
        echo "Error: REMOTE_DIR variable is not defined or is empty."
        exit 1
      fi

      if ! sudo test -f "$USER_CLAIM_FILE"; then
        if [ "$(find /srv/html/htdocs/$REMOTE_DIR/ -maxdepth 1 -type f ! -name ".*" | wc -l)" -gt 0 ]; then
          sudo mkdir -p "$ARCHIVE_FOLDER"
          ARCHIVE_RESULT=$(sudo tar -czf "$ARCHIVE_PATH" -C /srv/html/htdocs/ "$REMOTE_DIR")
          if [ $? -ne 0 ]; then
            echo "Error: Failed to archive $REMOTE_DIR to $ARCHIVE_PATH Error: $ARCHIVE_RESULT";
            exit 1
          fi
          echo "Archived $REMOTE_DIR to $ARCHIVE_PATH";
        fi
        echo "$REMOTE_DIR" | sudo tee "$USER_CLAIM_FILE";
        echo "Created new user-claim file with value: $REMOTE_DIR";
      elif [ "$(sudo cat $USER_CLAIM_FILE)" != "$REMOTE_DIR" ]; then
        echo "Error: user-claim file contains incorrect value: $(sudo cat $USER_CLAIM_FILE). Expected: $REMOTE_DIR" This is probably the wrong project for this remote directory. Please make sure you REALLY want to deploy the project to this directory. If you are sure, please remove the user-claim file and try again.;
        exit 1;
      else
        echo "user-claim file is valid, no changes needed.";
      fi'

    # Rsync files to a temporary location on the remote server (e.g., /tmp)
    - sshpass -p "$REMOTE_PASSWORD" rsync -avz --delete --exclude=".svn/" --exclude=".git/" --exclude='.env' --exclude='storage/' --exclude='bootstrap/cache/' --exclude='public/uploads/' --exclude='public/min/' --exclude='user-claim.txt' $LOCAL_DIR $REMOTE_USER@$REMOTE_HOST:/home/<USER>/$CI_PROJECT_NAME/

    # Use rsync with sudo to move files from the temp location to the final directory on the remote server
    - sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST "sudo rsync -avz --delete --exclude=".svn/" --exclude=".git/" --exclude='.env' --exclude='storage/' --exclude='bootstrap/cache/' --exclude='tmp/' --exclude='public/uploads/' --exclude='public/min/' --exclude='user-claim.txt' /home/<USER>/$CI_PROJECT_NAME/ /srv/html/htdocs/$REMOTE_DIR/"

    # Create a tmp directory in the destination directory
    - sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST "sudo mkdir -p $REMOTE_USER:$REMOTE_USER /srv/html/htdocs/$REMOTE_DIR/tmp"

    # Change ownership of the files in the destination directory to deploy user
    - sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST "sudo chown -R $REMOTE_USER:$REMOTE_USER /srv/html/htdocs/$REMOTE_DIR"

    # Copy .env.live to .env
    - sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST "cp /srv/html/htdocs/$REMOTE_DIR/.env.live /srv/html/htdocs/$REMOTE_DIR/.env || true"

    # Remove bootstrap/cache contents
    - sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST "rm -rf /srv/html/htdocs/$REMOTE_DIR/bootstrap/cache/* || true"

    # Now after copying we can remove the files from the temp location
    - sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST "rm -rf /home/<USER>/$CI_PROJECT_NAME || true"

    # Cd to web-app root and run commands (mkdir tmp, test example, add php cli_install.php and migration command etc. later when laravel added)
    - sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST "cd /srv/html/htdocs/$REMOTE_DIR && (php cli_install.php || true) && (php artisan app:migrate-db || true)"

    # Change ownership of the files in the destination directory to web user
    - sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST "sudo chown -R $REMOTE_DIR:$REMOTE_DIR /srv/html/htdocs/$REMOTE_DIR"

  only:
    - main_release  # Deploy only from


deploy-dev-job:
  variables:
    REMOTE_USER: gitlab_tmp # user with SSH access on remotes hosts like devhost (REMOTE_PASSWORD comes via global group variable)
    REMOTE_HOST: *************** # stefflhost *new*
    REMOTE_DIR: steffl02
    LOCAL_DIR: ./
    SSH_PORT: 22  # Modify if you're using a non-default SSH port
  stage: deploy
  script:
    # Set up known hosts to allow SSH connections
    - mkdir -p ~/.ssh
    - ssh-keyscan -p $SSH_PORT $REMOTE_HOST >> ~/.ssh/known_hosts

    # Check is right project for right remote directory
    - |
      sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST '
      REMOTE_DIR="'$REMOTE_DIR'";
      REMOTE_USER="'$REMOTE_USER'";
      USER_CLAIM_FILE="/srv/html/htdocs/$REMOTE_DIR/user-claim.txt";
      TIMESTAMP=$(date +%Y%m%d_%H%M%S);
      ARCHIVE_FOLDER="/home/<USER>/pre_first_deploy";
      ARCHIVE_PATH="$ARCHIVE_FOLDER/${REMOTE_DIR}_old_${TIMESTAMP}.tar.gz";

      if [ -z "$REMOTE_DIR" ]; then
        echo "Error: REMOTE_DIR variable is not defined or is empty."
        exit 1
      fi

      if ! sudo test -f "$USER_CLAIM_FILE"; then
        if [ "$(find /srv/html/htdocs/$REMOTE_DIR/ -maxdepth 1 -type f ! -name ".*" | wc -l)" -gt 0 ]; then
          sudo mkdir -p "$ARCHIVE_FOLDER"
          ARCHIVE_RESULT=$(sudo tar -czf "$ARCHIVE_PATH" -C /srv/html/htdocs/ "$REMOTE_DIR")
          if [ $? -ne 0 ]; then
            echo "Error: Failed to archive $REMOTE_DIR to $ARCHIVE_PATH Error: $ARCHIVE_RESULT";
            exit 1
          fi
          echo "Archived $REMOTE_DIR to $ARCHIVE_PATH";
        fi
        echo "$REMOTE_DIR" | sudo tee "$USER_CLAIM_FILE";
        echo "Created new user-claim file with value: $REMOTE_DIR";
      elif [ "$(sudo cat $USER_CLAIM_FILE)" != "$REMOTE_DIR" ]; then
        echo "Error: user-claim file contains incorrect value: $(sudo cat $USER_CLAIM_FILE). Expected: $REMOTE_DIR" This is probably the wrong project for this remote directory. Please make sure you REALLY want to deploy the project to this directory. If you are sure, please remove the user-claim file and try again.;
        exit 1;
      else
        echo "user-claim file is valid, no changes needed.";
      fi'

    # Rsync files to a temporary location on the remote server (e.g., /tmp)
    - sshpass -p "$REMOTE_PASSWORD" rsync -avz --delete --exclude=".svn/" --exclude=".git/" --exclude='.env' --exclude='storage/' --exclude='bootstrap/cache/' --exclude='public/uploads/' --exclude='public/min/' --exclude='user-claim.txt' $LOCAL_DIR $REMOTE_USER@$REMOTE_HOST:/home/<USER>/$CI_PROJECT_NAME/

    # Use rsync with sudo to move files from the temp location to the final directory on the remote server
    - sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST "sudo rsync -avz --delete --exclude=".svn/" --exclude=".git/" --exclude='.env' --exclude='storage/' --exclude='bootstrap/cache/' --exclude='tmp/' --exclude='public/uploads/' --exclude='public/min/' --exclude='user-claim.txt' /home/<USER>/$CI_PROJECT_NAME/ /srv/html/htdocs/$REMOTE_DIR/"

    # Create a tmp directory in the destination directory
    - sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST "sudo mkdir -p $REMOTE_USER:$REMOTE_USER /srv/html/htdocs/$REMOTE_DIR/tmp"

    # Change ownership of the files in the destination directory to deploy user
    - sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST "sudo chown -R $REMOTE_USER:$REMOTE_USER /srv/html/htdocs/$REMOTE_DIR"

    # Copy .env.dev to .env
    - sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST "cp /srv/html/htdocs/$REMOTE_DIR/.env.dev /srv/html/htdocs/$REMOTE_DIR/.env || true"

    # Remove bootstrap/cache contents
    - sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST "rm -rf /srv/html/htdocs/$REMOTE_DIR/bootstrap/cache/* || true"

    # Now after copying we can remove the files from the temp location
    - sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST "rm -rf /home/<USER>/$CI_PROJECT_NAME || true"

    # Cd to web-app root and run commands (mkdir tmp, test example, add php cli_install.php and migration command etc. later when laravel added)
    - sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST "cd /srv/html/htdocs/$REMOTE_DIR && (php cli_install.php || true) && (php artisan app:migrate-db || true)"

    # Change ownership of the files in the destination directory to web user
    - sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no -p $SSH_PORT $REMOTE_USER@$REMOTE_HOST "sudo chown -R $REMOTE_DIR:$REMOTE_DIR /srv/html/htdocs/$REMOTE_DIR"

  #only: # Deploy from all
  #  - main  # Deploy only from the main branch
