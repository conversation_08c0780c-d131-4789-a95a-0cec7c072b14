<?php

namespace Framework\src\Http\Models;

use Framework\src\Http\Traits\ManageableModelTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * @property integer id
 * @property string  value
 * @property integer expiration
 *
 * @mixin Builder
 */
class Cache extends Model{
    use ManageableModelTrait;

    protected        $table         = 'cache';
    protected        $guarded       = [];
    protected static $NON_ID_FIELDS = [
        "key"        => ["type" => "string", "unique" => true],
        "value"      => ["type" => "mediumtext", "change_def" => true],
        "expiration" => ["type" => "integer"],
    ];
}
